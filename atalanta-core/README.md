# atalanta #

A common backend for all web infra frontend requirement.

Each usecase can create a seperate module and can write their code independently.

The idea is to later on move all the modules to seperate repos and packages and connect all of them on run time. This need to be achieved in future. Currently we are starting with keeping all code in same repository in moduler form

## Set up
### How to add new module
- Create new folder for your module at `src/<module-name>`
- Create index.ts and add below code 


```javascript
        import { Express } from 'express';
        export default {
            name: '<MODULE-NAME>',
            setup: (app: Express) => {
                // Express app instence is passed here
            }
        }
```




- basically all you application/module related setup goes inside setup section of your index.ts file. and this become entry point for your application
- Go to `src/index.ts` and register your module there, see [example](https://bitbucket.org/zetaengg/atalanta/src/22f8ec5fcf32c1f75619c597a8a2d219d4fefda7/src/index.ts#lines-7)
- You should add Router in your module for various routing controllers, the conventions to follow is `/api/v1/<moduleName>/<custom-module-routes>`
- If your application serve, server side pages, you can keep your pages at `/views/<module-name>` that resolves to ejs templates

- You can check `sign-up` module on how this has been handeled as reference


## Dependencies
- Please try to reuse dependencies for a single usecase. do not try to introduce new
    - example, we have already added ejs for SSR pages
    - axios, for http call
- When updating version, make sure the backword compatibility
## Database configuration
- No Database as of now
## How to run tests
- NA

## How to run in local
- `npm install`
- `npm run dev`
- you can test with `http://localhost:3000/ping`
- you can get healthcheck with `http://localhost:3000/health`
- you can hit to `localhost:3000/api/v1/<moduleName>/<module-routes>`

## Staging deployment instructions
- This application is part of hercules cluster.
    - Run CI Job for [atalanta](https://build.internal.olympus-world.zetaapps.in/job/atalanta/)
    - You can switch and build your branch by changing the branch in jenkins job config
    - This will generate snapshot version. Eg: `atalanta:0.1.6-snapshot.master.22f8ec5` JUST SEARCH `--destination=813361731051.dkr.ecr.ap-south-1.amazonaws.com` TO GET THE VERSION NEXT TO IT
    - Update snapshot version in [ep-cluster](https://bitbucket.org/zetaengg/ep-cluster/src/master/helm-charts/hercules/aws-default-staging-mumbai/hercules-atalanta.yaml) repo
        - You can also update if any new properties are introduce or existing one require update
    - Increment [main chart version](https://bitbucket.org/zetaengg/ep-cluster/src/641a01a073eca464f538f4c939277419d95e733b/helm-charts/hercules/Chart.yaml#lines-4) for hercules cluster
    - Run [chartmuseum-publish](https://build.internal.olympus-world.zetaapps.in/job/chartmuseum-publish/) job with filter `hercules`
    - Check in [argo cd dashboard](https://argocd.internal.olympus-world.zetaapps.in/applications/hercules-staging) for the autodeployment
## Production/Preprod Release
- Once your PR is merged in master, run `npm run release:minor`, `npm run release:major`, `npm run release:patch` based on your usecase
- Run CI Job for [atalanta-release](https://build.internal.olympus-world.zetaapps.in/job/atalanta-release/)
- This will build for version. Eg: `atalanta:0.1.6` JUST SEARCH `--destination=813361731051.dkr.ecr.ap-south-1.amazonaws.com` TO GET THE VERSION NEXT TO IT
- Update version in [ep-cluster](https://bitbucket.org/zetaengg/ep-cluster/src/master/helm-charts/hercules/aws-default-pp-mumbai/hercules-atalanta.yaml) repo for preprod and for Prod update in [prod](https://bitbucket.org/zetaengg/ep-cluster/src/master/helm-charts/hercules/aws-common-prod-mumbai/hercules-atalanta.yaml) folder of it
    - You can also update if any new properties are introduce or existing one require update
- Increment [main chart version](https://bitbucket.org/zetaengg/ep-cluster/src/641a01a073eca464f538f4c939277419d95e733b/helm-charts/hercules/Chart.yaml#lines-4) for hercules cluster
- Run [chartmuseum-publish](https://build.internal.olympus-world.zetaapps.in/job/chartmuseum-publish/) job with filter `hercules`
- Create PR in argo cd repo to deploy the main chart version of hercules cluster
    - in [argo cd](https://bitbucket.org/zetaengg/argocd-apps) repo go to env specific folder, search hercules in values.yaml and update version for it
    - get PR merged, you can raise PR to anyone in web infra. this get auto synced
- Check in [argo cd pp dashboard](https://argocd.internal.olympus-world.zetaapps.in/applications/hercules-pp) for the prepord autodeployment and [argo cd prod dashboard](https://argocd.internal.olympus-world.zeta.in/applications/hercules-common-prod) for the prod autodeployment and 


### Contribution guidelines ###

* Writing tests
    - NA
* Code review
    - Follow [Typescript coding principles](https://google.github.io/styleguide/tsguide.html)
    - Always keep backend services implementation in [services](src/commons/services/) folder
    - Any code logic should be part of your specifc modules
    - All the exceptions should be handled properly and each module should have it's module exception handler method extends to `BaseServiceException`
    - Update Readme if any new module is added
* Other guidelines
    - NA

### Who do I talk to? ###

* Repo owner or admin 
    - write to [web-infra](<EMAIL>)
* Other community or team contact (Team name responsible for each modules)
    - sign-up :  [web-infra](<EMAIL>)

