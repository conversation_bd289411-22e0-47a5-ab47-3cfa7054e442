### Changelog

All notable changes to this project will be documented in this file. Dates are displayed in UTC.

Generated by [`auto-changelog`](https://github.com/CookPete/auto-changelog).

#### [1.6.6](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.6.5...1.6.6)

- fix(USSM-6509): shutdown end point restricted from external access [`1fd2baa`](https://github.com/Zeta-Enterprise/atalanta-core/commit/1fd2baa87e12861027a1fc98fbd6c7e20f555e8d)

#### [1.6.5](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.6.4...1.6.5)

> 15 May 2025

- feat(SPE-560): fetch trace id from headers object instead of header function [`b7d6491`](https://github.com/Zeta-Enterprise/atalanta-core/commit/b7d6491823e6547a7bfdb3d6bf3c63e00635930b)
- Release 1.6.5 [`48a2bab`](https://github.com/Zeta-Enterprise/atalanta-core/commit/48a2bab7ba1be15bad1321a4fadf001bb3dcc5ca)

#### [1.6.4](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.6.3...1.6.4)

> 14 May 2025

- Release 1.6.4 [`c528f7d`](https://github.com/Zeta-Enterprise/atalanta-core/commit/c528f7d009bd0262266c3f672695d4b2c670be50)
- feat(SPE-560): move mdc.trace_id out [`2e31769`](https://github.com/Zeta-Enterprise/atalanta-core/commit/2e31769bc72a58d4366e6c3cfb5102bc1e566490)

#### [1.6.3](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.6.2...1.6.3)

> 13 May 2025

- feat(SPE-560): fix baggage issue [`ff11622`](https://github.com/Zeta-Enterprise/atalanta-core/commit/ff11622406fe095e30b6d7d0bd18b3f571a72031)
- Release 1.6.3 [`d04d9e8`](https://github.com/Zeta-Enterprise/atalanta-core/commit/d04d9e8841b4550ac7abcee0da06661bc6d76d0e)

#### [1.6.2](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.6.1...1.6.2)

> 12 May 2025

- feat(SPE-560): downgrade build node to 14 [`ae002aa`](https://github.com/Zeta-Enterprise/atalanta-core/commit/ae002aa665233291973735fab7eb8460ccc5b463)
- Release 1.6.2 [`6791dcd`](https://github.com/Zeta-Enterprise/atalanta-core/commit/6791dcde067ab130d18b59f80e560d0699b661b8)

#### [1.6.1](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.6.0...1.6.1)

> 12 May 2025

- feat(SPE-560): changes for node-logger [`#43`](https://github.com/Zeta-Enterprise/atalanta-core/pull/43)
- Release 1.6.1 [`c0f710e`](https://github.com/Zeta-Enterprise/atalanta-core/commit/c0f710e3f5464158b89201c71216d2c192a6711e)

#### [1.6.0](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.5.40...1.6.0)

> 14 April 2025

- feat(USSM-6509): Handle SIGTERM event(in kubernetes) to enable graceful shutdown [`#42`](https://github.com/Zeta-Enterprise/atalanta-core/pull/42)
- feat: replaced console with logger, added event handling for SIGINT [`4666e85`](https://github.com/Zeta-Enterprise/atalanta-core/commit/4666e853076907111e18220e9f9f68ab7f735034)
- Release 1.6.0 [`30e0b41`](https://github.com/Zeta-Enterprise/atalanta-core/commit/30e0b418695c5c9e85119848b02c7e0e59a37ade)
- chore: readiness probe endpoint handling added [`e2ed9a8`](https://github.com/Zeta-Enterprise/atalanta-core/commit/e2ed9a8ece9c0bbddf3d10c6066c7aaeb36f3303)

#### [1.5.40](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.5.39...1.5.40)

> 8 April 2025

- fix(spe-3175):removed token logging from orchestra service [`#41`](https://github.com/Zeta-Enterprise/atalanta-core/pull/41)
- Release 1.5.40 [`a4d6785`](https://github.com/Zeta-Enterprise/atalanta-core/commit/a4d67853c66a8ed9b6e1ccde00d2d7cfec031918)

#### [1.5.39](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.5.38...1.5.39)

> 28 March 2025

- feat(SPE-3244): auraCharge-api-integration [`#40`](https://github.com/Zeta-Enterprise/atalanta-core/pull/40)
- Release 1.5.39 [`166eb97`](https://github.com/Zeta-Enterprise/atalanta-core/commit/166eb9767ba1870c6a22fa1292481ec65bbffbf2)

#### [1.5.38](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.5.37...1.5.38)

> 25 March 2025

- feat(TCH-20896) : adjust minor contract changes [`#38`](https://github.com/Zeta-Enterprise/atalanta-core/pull/38)
- Release 1.5.38 [`ebc76bf`](https://github.com/Zeta-Enterprise/atalanta-core/commit/ebc76bf46bb0be6c36c61410d8c7b5973806c37a)

#### [1.5.37](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.5.36...1.5.37)

> 20 March 2025

- feat(TCH-20896) : add get childCoa List api [`#37`](https://github.com/Zeta-Enterprise/atalanta-core/pull/37)
- Release 1.5.37 [`60d251e`](https://github.com/Zeta-Enterprise/atalanta-core/commit/60d251eeb265c1a44e782d5cb2e29c9a595ccd9a)
- feat(TCH-20896) : resolve PR comment [`b47bb06`](https://github.com/Zeta-Enterprise/atalanta-core/commit/b47bb0609d1ee714000f0604663fbc9e5bc9880e)

#### [1.5.36](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.5.35...1.5.36)

> 11 March 2025

- (feature/TCH-28971): added ArchivedItemExistsException condition to checkIfItemExists [`#36`](https://github.com/Zeta-Enterprise/atalanta-core/pull/36)
- Release 1.5.36 [`aade00c`](https://github.com/Zeta-Enterprise/atalanta-core/commit/aade00c194cc8d7b87a0105da9f965e38eba4a09)
- added ArchivedItemExistsException condition to checkIfItemExists [`f3c6f8f`](https://github.com/Zeta-Enterprise/atalanta-core/commit/f3c6f8f12e2f8d1df5bad87a28d6a319784d898a)

#### [1.5.35](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.5.34...1.5.35)

> 19 February 2025

- feat(TCH-35944): updated-policy-url [`#34`](https://github.com/Zeta-Enterprise/atalanta-core/pull/34)
- Release 1.5.35 [`2b16b22`](https://github.com/Zeta-Enterprise/atalanta-core/commit/2b16b225b88acd0a097230a0977c8ddcbb4f3393)

#### [1.5.34](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.5.33...1.5.34)

> 19 February 2025

- feat(TCH-35944): integrated-policies-api [`#33`](https://github.com/Zeta-Enterprise/atalanta-core/pull/33)
- Release 1.5.34 [`1179b66`](https://github.com/Zeta-Enterprise/atalanta-core/commit/1179b66ff647535b294e48e44c0d6a573794780d)

#### [1.5.33](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.5.32...1.5.33)

> 13 February 2025

- (feat/TCH-28971): added delete View API [`#32`](https://github.com/Zeta-Enterprise/atalanta-core/pull/32)
- added delete VIew API [`cf27e9b`](https://github.com/Zeta-Enterprise/atalanta-core/commit/cf27e9b7ed1002451dea3a66f9e0f985d1212767)
- attended to review comments [`1cd7a39`](https://github.com/Zeta-Enterprise/atalanta-core/commit/1cd7a394e26ad043731245478bb05daf6b83a12b)
- Release 1.5.33 [`db6c1c7`](https://github.com/Zeta-Enterprise/atalanta-core/commit/db6c1c7c8dda21af2b320f46d4db82b40ccfc689)

#### [1.5.32](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.5.31...1.5.32)

> 11 February 2025

- fix(TCU-10608) update filter for credit [`#31`](https://github.com/Zeta-Enterprise/atalanta-core/pull/31)
- Release 1.5.32 [`a828b52`](https://github.com/Zeta-Enterprise/atalanta-core/commit/a828b5225a7f43b18ef0f8f783ec1f11aba5c171)

#### [1.5.31](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.5.30...1.5.31)

> 30 January 2025

- fix(TCU-10608) update debit txn logic to get base txn instead of fee [`#30`](https://github.com/Zeta-Enterprise/atalanta-core/pull/30)
- Release 1.5.31 [`f4ab855`](https://github.com/Zeta-Enterprise/atalanta-core/commit/f4ab8550228984121c76fd46ef5908ac10b07f9f)
- fix [`0aa469f`](https://github.com/Zeta-Enterprise/atalanta-core/commit/0aa469f45ef1b27cc22d68675efa5d27a4864524)

#### [1.5.30](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.5.29...1.5.30)

> 17 January 2025

- fix: build issue [`#29`](https://github.com/Zeta-Enterprise/atalanta-core/pull/29)
- Release 1.5.30 [`b9ac2cd`](https://github.com/Zeta-Enterprise/atalanta-core/commit/b9ac2cde1ff4ecf3fea12b4fa7dde3683b787d40)

#### [1.5.29](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.5.29-2...1.5.29)

> 17 January 2025

- fix(ussm-7401): feat back rub url [`#28`](https://github.com/Zeta-Enterprise/atalanta-core/pull/28)
- Release 1.5.29 [`d5b2a50`](https://github.com/Zeta-Enterprise/atalanta-core/commit/d5b2a50cfa35d3a4d37e297ee9129ae279083a5f)
- feat: backrub support [`edc2689`](https://github.com/Zeta-Enterprise/atalanta-core/commit/edc268947f6f9324fa41bfaec5aa5a97065d044a)

#### [1.5.29-2](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.5.29-1...1.5.29-2)

> 16 January 2025

- Release 1.5.29-2 [`c340f87`](https://github.com/Zeta-Enterprise/atalanta-core/commit/c340f877e3ce10715a7bd94c89e21b1c70cf7cdd)
- fixL build issue [`d79eacb`](https://github.com/Zeta-Enterprise/atalanta-core/commit/d79eacbfdeb0f9942a9a1ad1aa0d0f45d9c58fd8)

#### [1.5.29-1](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.5.29-0...1.5.29-1)

> 16 January 2025

- Release 1.5.29-1 [`4e11b02`](https://github.com/Zeta-Enterprise/atalanta-core/commit/4e11b027696b735ee7f883def63f6c6ee5b23bfd)

#### [1.5.29-0](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.5.28...1.5.29-0)

> 16 January 2025

- Release 1.5.29-0 [`dd0d1d5`](https://github.com/Zeta-Enterprise/atalanta-core/commit/dd0d1d565bdfe64c7b6ef84100516e930895d9bc)

#### [1.5.28](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.5.27...1.5.28)

> 16 January 2025

- Release 1.5.28 [`cedfac6`](https://github.com/Zeta-Enterprise/atalanta-core/commit/cedfac621f15a5f156094831207c8b296a0d433c)

#### [1.5.27](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.5.26...1.5.27)

> 15 January 2025

- Release 1.5.27 [`09bb61b`](https://github.com/Zeta-Enterprise/atalanta-core/commit/09bb61be9ddf0cdc4d4ec7ae69fa93372e255412)

#### [1.5.26](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.5.25...1.5.26)

> 15 January 2025

- fix(USSM-7401): updated orchestra service base url [`#26`](https://github.com/Zeta-Enterprise/atalanta-core/pull/26)
- Release 1.5.26 [`bb2280a`](https://github.com/Zeta-Enterprise/atalanta-core/commit/bb2280afffc788c018a560cbbd6ef7b5e5a8d224)
- feat: updated orchestra service base url [`396a12b`](https://github.com/Zeta-Enterprise/atalanta-core/commit/396a12bae3c94cf7cbb83172aae676c84e847740)
- feat: added orchestra backrub url support [`c3eae93`](https://github.com/Zeta-Enterprise/atalanta-core/commit/c3eae93d1c17cb343fe696add624e9187c494918)

#### [1.5.25](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.5.24...1.5.25)

> 14 November 2024

- feat: Collection service API time out issue fix [`#23`](https://github.com/Zeta-Enterprise/atalanta-core/pull/23)
- Release 1.5.25 [`30efa9d`](https://github.com/Zeta-Enterprise/atalanta-core/commit/30efa9d5c4abf62599f2206984c99c2df094d939)
- chore: bug fix for last page [`36cd8ed`](https://github.com/Zeta-Enterprise/atalanta-core/commit/36cd8ed6657a9b75a57bbd34e3465619b4ed138c)

#### [1.5.24](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.5.23...1.5.24)

> 5 November 2024

- enhance(USSM-6509): integrate security middleware and improve logging middleware [`#22`](https://github.com/Zeta-Enterprise/atalanta-core/pull/22)
- fix(USSM-6509): add helmet, validation, sanitization and improved logging [`9ec4b47`](https://github.com/Zeta-Enterprise/atalanta-core/commit/9ec4b47026c01bb9b3fb675476b74ff28b88fdfa)
- enhance(USSM-6509): add validation only xss [`04ddf92`](https://github.com/Zeta-Enterprise/atalanta-core/commit/04ddf9266e8f124b6ea3f9bce4a124b0d8148a06)
- enhance(USSM-6509): usage of middle ware only environment variable purpose [`4809ac9`](https://github.com/Zeta-Enterprise/atalanta-core/commit/4809ac93b5d9365d738867f3f942bca8c51a3508)

#### [1.5.23](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.5.22...1.5.23)

> 24 October 2024

- feat : update merchant credit [`#21`](https://github.com/Zeta-Enterprise/atalanta-core/pull/21)
- Release 1.5.23 [`9f85b28`](https://github.com/Zeta-Enterprise/atalanta-core/commit/9f85b28b17c363a994169b397f3d81b78dcd9d0a)

#### [1.5.22](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.5.21...1.5.22)

> 21 October 2024

- Feat/tch 29898 merchant credit api [`#20`](https://github.com/Zeta-Enterprise/atalanta-core/pull/20)
- feat: add postings for merchant credit [`d3f14ad`](https://github.com/Zeta-Enterprise/atalanta-core/commit/d3f14ad1e0e307158b266ad32a9632566df9f66f)
- Release 1.5.22 [`9c9ca41`](https://github.com/Zeta-Enterprise/atalanta-core/commit/9c9ca41ae1bdc1460a944fb0b1c40a2f92e47904)
- feat : update url issues [`fa2e355`](https://github.com/Zeta-Enterprise/atalanta-core/commit/fa2e355e5b04af7ac827ba441f648cf86506ba68)

#### [1.5.21](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.5.20...1.5.21)

> 14 October 2024

- Updated the type for audit event and entity definitions [`#19`](https://github.com/Zeta-Enterprise/atalanta-core/pull/19)
- Release 1.5.21 [`5505fd4`](https://github.com/Zeta-Enterprise/atalanta-core/commit/5505fd42f5b8581898ca400be210d6f9cfe14cd1)

#### [1.5.20](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.5.19...1.5.20)

> 8 October 2024

- Added the endpoint for audit entity [`#17`](https://github.com/Zeta-Enterprise/atalanta-core/pull/17)
- Release 1.5.20 [`73116d2`](https://github.com/Zeta-Enterprise/atalanta-core/commit/73116d27f85eeb2bb2de1f6b242d838ac5240b8f)

#### [1.5.19](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.5.18...1.5.19)

> 8 October 2024

- Release 1.5.19 [`ffa3c85`](https://github.com/Zeta-Enterprise/atalanta-core/commit/ffa3c85d18d77748ee9f77fb08129e9691d803b4)

#### [1.5.18](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.5.17...1.5.18)

> 8 October 2024

- fix(TCU-7944): Added try catch in transform response util method in case the response is not a valid json string [`#16`](https://github.com/Zeta-Enterprise/atalanta-core/pull/16)
- Release 1.5.18 [`3d27739`](https://github.com/Zeta-Enterprise/atalanta-core/commit/3d27739f5bd20432af46c1792def8ff3223d947c)

#### [1.5.17](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.5.16...1.5.17)

> 27 September 2024

- fix: error.toJSON fix | transform request and response util methods added  [`#15`](https://github.com/Zeta-Enterprise/atalanta-core/pull/15)
- feat: transform request and response util methods added to handle bigInt [`813fbd1`](https://github.com/Zeta-Enterprise/atalanta-core/commit/813fbd1f2ccc0eefa3d8c6e0397be52eae596fc6)
- Release 1.5.17 [`6eb5c11`](https://github.com/Zeta-Enterprise/atalanta-core/commit/6eb5c11160f999fa542fd9afa743cb10163fe0ad)

#### [1.5.16](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.5.15...1.5.16)

> 25 September 2024

- Release 1.5.16 [`ec4dae9`](https://github.com/Zeta-Enterprise/atalanta-core/commit/ec4dae9786222fb50123fc7c42ab0403160a9ec9)
- Updated header for authorization in get audit definitions list [`ccd61e2`](https://github.com/Zeta-Enterprise/atalanta-core/commit/ccd61e2f21d92fdc31c1f36d1e0c98c52fc5cbd3)

#### [1.5.15](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.5.14...1.5.15)

> 23 September 2024

- feat/(TCH-20881) Manual GL Handoff APIs [`#14`](https://github.com/Zeta-Enterprise/atalanta-core/pull/14)
- fix createGlHandOffRequest post api call response handling [`e756251`](https://github.com/Zeta-Enterprise/atalanta-core/commit/e756251f98428488c6182fd13eace20b35db6986)
- Release 1.5.15 [`f3cd4d6`](https://github.com/Zeta-Enterprise/atalanta-core/commit/f3cd4d61607784e4c7c235f5e3d66bcaf2359dcd)

#### [1.5.14](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.5.13...1.5.14)

> 18 September 2024

- feat/(TCH-20881) Manual GL Handoff APIs [`#8`](https://github.com/Zeta-Enterprise/atalanta-core/pull/8)
- Added manual GL handoff generation APIs [`02a0dc5`](https://github.com/Zeta-Enterprise/atalanta-core/commit/02a0dc5eeb6a4d293295e09c3c67e8c8a5f1d6ae)
- add getGlHandoffRuns api [`38699b5`](https://github.com/Zeta-Enterprise/atalanta-core/commit/38699b55e17046ec136d6f6c269c903aa0c58b01)
- Release 1.5.14 [`7490a67`](https://github.com/Zeta-Enterprise/atalanta-core/commit/7490a6717147488dd7b1f50b4b7e2d17f3e83bf0)

#### [1.5.13](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.5.12...1.5.13)

> 16 September 2024

- add mtd ytd logic for balance explorer [`#9`](https://github.com/Zeta-Enterprise/atalanta-core/pull/9)
- add env flag [`7e5f616`](https://github.com/Zeta-Enterprise/atalanta-core/commit/7e5f616741e1d7e4c708ad0f7cf372416ede6752)
- Release 1.5.13 [`7b603a7`](https://github.com/Zeta-Enterprise/atalanta-core/commit/7b603a7e4d5b028560de16ca596e654a2d344d63)

#### [1.5.12](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.5.11...1.5.12)

> 11 September 2024

- Added get Audit definition endpoint [`#10`](https://github.com/Zeta-Enterprise/atalanta-core/pull/10)
- Release 1.5.12 [`fb11fa0`](https://github.com/Zeta-Enterprise/atalanta-core/commit/fb11fa0921087e5192e312ec3c67fead0b93114d)

#### [1.5.11](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.5.10...1.5.11)

> 29 August 2024

- Added session login activity service to Cipher [`#7`](https://github.com/Zeta-Enterprise/atalanta-core/pull/7)
- Release 1.5.11 [`acb6678`](https://github.com/Zeta-Enterprise/atalanta-core/commit/acb6678192a71046e5a4870fbc6c68292abe071f)
- Add CODEOWNERS file [`185643e`](https://github.com/Zeta-Enterprise/atalanta-core/commit/185643e486e4fe0eab8218dee4e9d58c4715e90d)
- Replace Bitbucket SCM URL in all POM files [`9f4c8fb`](https://github.com/Zeta-Enterprise/atalanta-core/commit/9f4c8fbe56826bd93373bda3750396d9b39f7e0f)

#### [1.5.10](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.5.9...1.5.10)

> 3 July 2024

- feat(TCH-10556) use token from request [`#72`](https://github.com/Zeta-Enterprise/atalanta-core/pull/72)
- Release 1.5.10 [`f6b115a`](https://github.com/Zeta-Enterprise/atalanta-core/commit/f6b115aa86da29df0d50ae9b0b2b76bdc8195c67)

#### [1.5.9](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.5.8...1.5.9)

> 26 June 2024

- feat(TCH-10556) get preference data using Eurus [`#71`](https://github.com/Zeta-Enterprise/atalanta-core/pull/71)
- Release 1.5.9 [`5439966`](https://github.com/Zeta-Enterprise/atalanta-core/commit/543996643848798a61be01dad61079cd2f436438)

#### [1.5.8](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.5.8-fix-vulnerabilities.0...1.5.8)

> 24 June 2024

- Feat/TCH-27016/fix vulnerabilities [`#70`](https://github.com/Zeta-Enterprise/atalanta-core/pull/70)
- Release 1.5.8 [`e443f97`](https://github.com/Zeta-Enterprise/atalanta-core/commit/e443f9729f148d6e2688e7cb7162a40e0cad4957)

#### [1.5.8-fix-vulnerabilities.0](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.5.7...1.5.8-fix-vulnerabilities.0)

> 17 June 2024

- Release 1.5.8-fix-vulnerabilities.0 [`a0986ec`](https://github.com/Zeta-Enterprise/atalanta-core/commit/a0986ecd045c5d500b3638e5b89838f45e3228da)
- feat(TCH-27016): merge from master [`86d6194`](https://github.com/Zeta-Enterprise/atalanta-core/commit/86d6194d1c8fed819a9c99821f9df6a472b4cb4b)
- Release 1.2.16-7f35f57.0 [`4897814`](https://github.com/Zeta-Enterprise/atalanta-core/commit/4897814fce79c9692a41de5a50700d53d09954bc)

#### [1.5.7](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.5.6...1.5.7)

> 3 June 2024

- Added getEnrichedContext and getAuthprofile apis [`#69`](https://github.com/Zeta-Enterprise/atalanta-core/pull/69)
- Release 1.5.7 [`1d73c50`](https://github.com/Zeta-Enterprise/atalanta-core/commit/1d73c50311c7c8acfa8a4d41ad2d3d0ba2c6052b)

#### [1.5.6](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.5.5...1.5.6)

> 30 May 2024

- feat: Atalanta API for ErrorCodes [`#68`](https://github.com/Zeta-Enterprise/atalanta-core/pull/68)
- Release 1.5.6 [`112feb0`](https://github.com/Zeta-Enterprise/atalanta-core/commit/112feb04bc3904247745c619cd73bb7fab40e7ca)

#### [1.5.5](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.5.3...1.5.5)

> 29 May 2024

- fix(TCT-13376) : added logs in collection service [`#67`](https://github.com/Zeta-Enterprise/atalanta-core/pull/67)
- Feat: Update ArgusService to include coaCode in getCoaSummaryGoldenSchema requestParams [`#66`](https://github.com/Zeta-Enterprise/atalanta-core/pull/66)
- Release 1.5.4 [`7ba0539`](https://github.com/Zeta-Enterprise/atalanta-core/commit/7ba0539956369e97cdf3f69b3b1b86f35a890a4b)
- Release 1.5.5 [`55f31db`](https://github.com/Zeta-Enterprise/atalanta-core/commit/55f31db3d1b64b953939b5c550da4c775abc0bea)

#### [1.5.3](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.5.2...1.5.3)

> 23 May 2024

- fix : Authorisation decorator token extraction logic added for bearer and non bearer token [`#65`](https://github.com/Zeta-Enterprise/atalanta-core/pull/65)
- Release 1.5.3 [`00fe77a`](https://github.com/Zeta-Enterprise/atalanta-core/commit/00fe77a6ad25e72eda84e513a6d862151607fb9c)

#### [1.5.2](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.5.1...1.5.2)

> 20 May 2024

- fix(TCH-20915) : added type for getView method in collection service [`#64`](https://github.com/Zeta-Enterprise/atalanta-core/pull/64)
- Release 1.5.2 [`638b057`](https://github.com/Zeta-Enterprise/atalanta-core/commit/638b0572681625f2ea840b61fa995cb662377ed2)

#### [1.5.1](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.5.0...1.5.1)

> 20 May 2024

- feat(TCH-20915) : 1. Added method in collection service to fetch a single collection. 2. Added crux token's JID in the view permission records. This is needed so that every view can be accessed using a crux token. [`#63`](https://github.com/Zeta-Enterprise/atalanta-core/pull/63)
- Release 1.5.1 [`f678c66`](https://github.com/Zeta-Enterprise/atalanta-core/commit/f678c660c141cc8acf975f82c10f57d3ed38d0bc)

#### [1.5.0](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.4.14...1.5.0)

> 14 May 2024

- [TCH-26801] Add generate statement by statementId API [`#62`](https://github.com/Zeta-Enterprise/atalanta-core/pull/62)
- Release 1.5.0 [`34d5a9c`](https://github.com/Zeta-Enterprise/atalanta-core/commit/34d5a9c140153578217c8d07555dce49be576646)

#### [1.4.14](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.4.13...1.4.14)

> 5 April 2024

- Added orchesta url through query [`#61`](https://github.com/Zeta-Enterprise/atalanta-core/pull/61)
- Release 1.4.14 [`62ded8c`](https://github.com/Zeta-Enterprise/atalanta-core/commit/62ded8ca68478148f010f20d1080b01f4f9398c9)

#### [1.4.13](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.4.12...1.4.13)

> 3 April 2024

- feat(TCU-3879) : Refactore collection service and added auth response authorization decorator [`#60`](https://github.com/Zeta-Enterprise/atalanta-core/pull/60)
- Release 1.4.13 [`1229dd3`](https://github.com/Zeta-Enterprise/atalanta-core/commit/1229dd3e87237ab5b269ba4fdaf032fe94fd1a47)

#### [1.4.12](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.4.11...1.4.12)

> 19 March 2024

- fix(TCU-3789) : Added a check while saving a view if the view's itemID already exists [`#59`](https://github.com/Zeta-Enterprise/atalanta-core/pull/59)
- feat(TCH-8066) : Collection service public views isPublic fix [`#56`](https://github.com/Zeta-Enterprise/atalanta-core/pull/56)
- Release 1.4.12 [`f1353af`](https://github.com/Zeta-Enterprise/atalanta-core/commit/f1353af5db97eddc53e0a9121b9e96cea35ce52c)

#### [1.4.11](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.4.10...1.4.11)

> 18 March 2024

- feat(USSM-4078): Updating Node logger for Auth token fix [`#58`](https://github.com/Zeta-Enterprise/atalanta-core/pull/58)
- Release 1.4.11 [`108df04`](https://github.com/Zeta-Enterprise/atalanta-core/commit/108df04617e0839f822d4f8599aaa4c71dbccd27)

#### [1.4.10](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.4.9...1.4.10)

> 18 March 2024

- build error fix [`0c6ccc6`](https://github.com/Zeta-Enterprise/atalanta-core/commit/0c6ccc668cf1b426dbfdbc0c4b8b34f367fe5254)
- Release 1.4.10 [`6d02e09`](https://github.com/Zeta-Enterprise/atalanta-core/commit/6d02e093ee6cbeb4beae453f0400783246ff8cc1)

#### [1.4.9](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.4.9-error.0...1.4.9)

> 18 March 2024

- fix(USSM-3233): Removed Auth token from Request params and updated node logger version along with proper loggers [`#57`](https://github.com/Zeta-Enterprise/atalanta-core/pull/57)
- Release 1.4.9 [`6be5cb1`](https://github.com/Zeta-Enterprise/atalanta-core/commit/6be5cb118fb44d825dfcdb4b259a8bf8cfda6edc)

#### [1.4.9-error.0](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.4.8...1.4.9-error.0)

> 2 March 2024

- fix: Refactored Internal API exception [`7bc650f`](https://github.com/Zeta-Enterprise/atalanta-core/commit/7bc650f090a53c4c7d4e4727441b6ee589e83a16)
- fix(USSM-3233): Removed Auth token from Request params and updated node logger version along with proper loggers [`8b85974`](https://github.com/Zeta-Enterprise/atalanta-core/commit/8b85974a3de529fb54ce27eb35b210c3d5618623)
- Release 1.4.9-error.0 [`d6d702f`](https://github.com/Zeta-Enterprise/atalanta-core/commit/d6d702f0e0c4262ff60a30522e6fbdf1a4363d37)

#### [1.4.8](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.4.7...1.4.8)

> 20 February 2024

- feature(TCH-22503): moved coagoldenschema api to argus service [`#54`](https://github.com/Zeta-Enterprise/atalanta-core/pull/54)
- Release 1.4.8 [`b568587`](https://github.com/Zeta-Enterprise/atalanta-core/commit/b5685871f72ced34971b43122b7cbb8269504769)

#### [1.4.7](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.4.6...1.4.7)

> 20 February 2024

- Added service for getting parent coa active connector [`#55`](https://github.com/Zeta-Enterprise/atalanta-core/pull/55)
- Release 1.4.7 [`9dcfbc9`](https://github.com/Zeta-Enterprise/atalanta-core/commit/9dcfbc9a476173cf5ca4da19947d9865c096ef96)

#### [1.4.6](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.4.5...1.4.6)

> 14 February 2024

- fix(TCH-8066): Save view method fixed for handling axios response data property [`#53`](https://github.com/Zeta-Enterprise/atalanta-core/pull/53)
- Release 1.4.6 [`5b14c37`](https://github.com/Zeta-Enterprise/atalanta-core/commit/5b14c3725b5ab9052be39855c151418ceb93813e)

#### [1.4.5](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.4.4...1.4.5)

> 14 February 2024

- Release 1.4.5 [`cc0dd0e`](https://github.com/Zeta-Enterprise/atalanta-core/commit/cc0dd0efcd89a8f2a1e61bf825173a17537454db)
- Fix conditional statement in AuraService.ts [`b955216`](https://github.com/Zeta-Enterprise/atalanta-core/commit/b955216cdd2a6cb7c035cfa2ed6e03f0947f3469)

#### [1.4.4](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.4.3...1.4.4)

> 14 February 2024

- feature(TCH-22503): Fix length check for postingCategories in AuraService [`#52`](https://github.com/Zeta-Enterprise/atalanta-core/pull/52)
- Release 1.4.4 [`893bf90`](https://github.com/Zeta-Enterprise/atalanta-core/commit/893bf90d089c758a6e8082287b814318263e5393)

#### [1.4.3](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.4.2...1.4.3)

> 12 February 2024

- feature(TCH-22503): validation fix for ganymede query [`#51`](https://github.com/Zeta-Enterprise/atalanta-core/pull/51)
- Release 1.4.3 [`a53ce6f`](https://github.com/Zeta-Enterprise/atalanta-core/commit/a53ce6f366114d6f535f59c4ade0a519f1965414)

#### [1.4.2](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.4.1...1.4.2)

> 7 February 2024

- Release 1.4.2 [`7e1f19f`](https://github.com/Zeta-Enterprise/atalanta-core/commit/7e1f19febbe3c0ee52568fc7619959693ea00fa1)
- chore: added space [`7e9e98c`](https://github.com/Zeta-Enterprise/atalanta-core/commit/7e9e98cccceb849994fc3a345e384e8ce8416e15)

#### [1.4.1](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.4.0...1.4.1)

> 7 February 2024

- feature(TCH-22503): create atalanta endpoint for balance explorer [`#50`](https://github.com/Zeta-Enterprise/atalanta-core/pull/50)
- Release 1.4.1 [`98708e4`](https://github.com/Zeta-Enterprise/atalanta-core/commit/98708e4c705d797fe17e7dfe21f922da1681291a)

#### [1.4.0](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.3.16...1.4.0)

> 7 February 2024

- feat(TCH-8066): Collection Service added for save views and get views [`#49`](https://github.com/Zeta-Enterprise/atalanta-core/pull/49)
- Release 1.4.0 [`21313f1`](https://github.com/Zeta-Enterprise/atalanta-core/commit/21313f1f6b37d8502c7e2d730e6fb6cb7b3bc999)

#### [1.3.16](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.3.15...1.3.16)

> 1 February 2024

- feat(TCH-20867) Updated endpoint for parent coa and changed axios instance [`#48`](https://github.com/Zeta-Enterprise/atalanta-core/pull/48)
- Release 1.3.16 [`de035e3`](https://github.com/Zeta-Enterprise/atalanta-core/commit/de035e3831a5cc4174b738f2879c80d3084699a4)

#### [1.3.15](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.3.14...1.3.15)

> 16 January 2024

- Feat/TCH-18209 Parent Coa creation  APIs updates [`#47`](https://github.com/Zeta-Enterprise/atalanta-core/pull/47)
- feat(TCH-18204) Added the endpoints for parent coa connection [`#45`](https://github.com/Zeta-Enterprise/atalanta-core/pull/45)
- Release 1.3.15 [`0f7b154`](https://github.com/Zeta-Enterprise/atalanta-core/commit/0f7b15413920c94bcbe25dd21aa33af0172dbb7a)

#### [1.3.14](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.3.13...1.3.14)

> 8 January 2024

- Constant  path update on cipher service [`#46`](https://github.com/Zeta-Enterprise/atalanta-core/pull/46)
- Release 1.3.14 [`e170e08`](https://github.com/Zeta-Enterprise/atalanta-core/commit/e170e086355d8ef5fbcb7b05244385cdd3b42310)

#### [1.3.13](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.3.12...1.3.13)

> 4 December 2023

- node-logger version bump up [`#44`](https://github.com/Zeta-Enterprise/atalanta-core/pull/44)
- Release 1.3.13 [`3bb9380`](https://github.com/Zeta-Enterprise/atalanta-core/commit/3bb9380777110112fbf0fa33f45056c828048f82)

#### [1.3.12](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.3.11...1.3.12)

> 29 November 2023

- fix(WIR-640): Changed the authorization header for the Cipher authenticate method. [`#43`](https://github.com/Zeta-Enterprise/atalanta-core/pull/43)
- Release 1.3.12 [`52e01aa`](https://github.com/Zeta-Enterprise/atalanta-core/commit/52e01aa563fb753dc89e93764414f301011ebdc0)

#### [1.3.11](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.3.10...1.3.11)

> 28 November 2023

- Bugfix/TCU-1620 node logger version bump up [`#42`](https://github.com/Zeta-Enterprise/atalanta-core/pull/42)
- Node logger version bump up [`#41`](https://github.com/Zeta-Enterprise/atalanta-core/pull/41)
- Release 1.3.11 [`d07d043`](https://github.com/Zeta-Enterprise/atalanta-core/commit/d07d043515bf4b10cb0007c395ac20602d954454)

#### [1.3.10](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.3.9...1.3.10)

> 28 November 2023

- Node logger version bump up [`f267c5a`](https://github.com/Zeta-Enterprise/atalanta-core/commit/f267c5a8de883e0ae67c381d7efb23a2e834fdaf)
- Release 1.3.10 [`14f166e`](https://github.com/Zeta-Enterprise/atalanta-core/commit/14f166ede40b04a84b0ad74daf4914b7d7eaa008)

#### [1.3.9](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.3.8...1.3.9)

> 23 November 2023

- feat(TCH-19004) Updated parent coa id to parent coa code in Argus service [`#40`](https://github.com/Zeta-Enterprise/atalanta-core/pull/40)
- Release 1.3.9 [`4fe4ee1`](https://github.com/Zeta-Enterprise/atalanta-core/commit/4fe4ee1a38e871c2be94870a89e43b160b1b2b28)

#### [1.3.8](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.3.7...1.3.8)

> 23 November 2023

- (TCH-19004) Added the parent coa ledger list [`#39`](https://github.com/Zeta-Enterprise/atalanta-core/pull/39)
- Release 1.3.8 [`603e5ee`](https://github.com/Zeta-Enterprise/atalanta-core/commit/603e5ee02443b891b40466033fc94ff3ddf5f55f)

#### [1.3.7](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.3.6...1.3.7)

> 20 November 2023

- Feat/USSM-2620 Added route logger and fixed error for Balance summary services [`#38`](https://github.com/Zeta-Enterprise/atalanta-core/pull/38)
- Release 1.3.7 [`23bb9e8`](https://github.com/Zeta-Enterprise/atalanta-core/commit/23bb9e8dac3e2d44d665525c336bfb96bd2460ff)

#### [1.3.6](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.3.5...1.3.6)

> 20 November 2023

- feat(TCH-19004) Updated The error message for  arguservice functions and removed argument from request as it is already avaliable in the class [`#37`](https://github.com/Zeta-Enterprise/atalanta-core/pull/37)
- Release 1.3.6 [`43955d7`](https://github.com/Zeta-Enterprise/atalanta-core/commit/43955d720c38c1011389c50b943fc02fe9c27a8d)

#### [1.3.5](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.3.4...1.3.5)

> 17 November 2023

- Release 1.3.5 [`bef025d`](https://github.com/Zeta-Enterprise/atalanta-core/commit/bef025d38d6df9220a54a24591cfc843e0fb508b)
- pseudo commit to trigger release as the last one  failed [`f17061b`](https://github.com/Zeta-Enterprise/atalanta-core/commit/f17061be68d8c9d20067b8ee1b29125ff5640e6d)

#### [1.3.4](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.3.3...1.3.4)

> 17 November 2023

- Added Argus service with functions for Parent COA [`#36`](https://github.com/Zeta-Enterprise/atalanta-core/pull/36)
- Release 1.3.4 [`5995d83`](https://github.com/Zeta-Enterprise/atalanta-core/commit/5995d830d14e1852b9eba27a2128e320ae7fab88)

#### [1.3.3](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.3.2...1.3.3)

> 17 November 2023

- removed aries api from fusion service [`#35`](https://github.com/Zeta-Enterprise/atalanta-core/pull/35)
- package lock updated [`669beff`](https://github.com/Zeta-Enterprise/atalanta-core/commit/669beff9bd3d1fed4fd196144bb6f96aaa1b53c0)
- Release 1.3.3 [`424a094`](https://github.com/Zeta-Enterprise/atalanta-core/commit/424a094b4f7edf1b1c03d282b48656119dd3f6b9)

#### [1.3.2](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.3.1...1.3.2)

> 15 November 2023

- feature(TCH-18457): Aries service individual api [`#34`](https://github.com/Zeta-Enterprise/atalanta-core/pull/34)
- Release 1.3.2 [`b969f38`](https://github.com/Zeta-Enterprise/atalanta-core/commit/b969f38c88f1cc5d3423a749d3f1a1c5128b72be)

#### [1.3.1](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.3.0...1.3.1)

> 14 November 2023

- feature(TCH-18457): Account holder V4 aries api added [`#32`](https://github.com/Zeta-Enterprise/atalanta-core/pull/32)
- Release 1.3.1 [`4f895f8`](https://github.com/Zeta-Enterprise/atalanta-core/commit/4f895f8604e1c9ec7664b500c69c3ba5be96c701)

#### [1.3.0](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.2.16-7f35f57.0...1.3.0)

> 16 October 2023

- remove authtoken from query params [`#29`](https://github.com/Zeta-Enterprise/atalanta-core/pull/29)
- fluentD migration version bump up [`#30`](https://github.com/Zeta-Enterprise/atalanta-core/pull/30)
- Release 1.3.0 [`8db2625`](https://github.com/Zeta-Enterprise/atalanta-core/commit/8db2625ecd05a62abf4ed3e90eb630a4691165f4)

#### [1.2.16-7f35f57.0](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.2.15...1.2.16-7f35f57.0)

> 17 June 2024

- Release 1.2.16-7f35f57.0 [`4897814`](https://github.com/Zeta-Enterprise/atalanta-core/commit/4897814fce79c9692a41de5a50700d53d09954bc)
- feat(TCH-27016): update the vulnerable dependencies [`7f35f57`](https://github.com/Zeta-Enterprise/atalanta-core/commit/7f35f575ca7732be6482f3d27fc5dfa7a7873991)

#### [1.2.15](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.2.15-3...1.2.15)

> 24 August 2023

#### [1.2.15-3](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.2.15-2...1.2.15-3)

> 14 November 2023

- Added middleware for router to log the route hit [`#33`](https://github.com/Zeta-Enterprise/atalanta-core/pull/33)
- Release 1.2.15-3 [`d157457`](https://github.com/Zeta-Enterprise/atalanta-core/commit/d157457fcc5d10c9ec93a23e281758ad5f282ebe)

#### [1.2.15-2](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.2.15-1...1.2.15-2)

> 14 November 2023

- Release 1.2.15-2 [`8cab359`](https://github.com/Zeta-Enterprise/atalanta-core/commit/8cab359c090011c07daa2f1809228141c76463b1)

#### [1.2.15-1](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.2.14...1.2.15-1)

> 13 November 2023

- feature(TCT-9839) add sortby and sortOrder param in orchestra run api [`#28`](https://github.com/Zeta-Enterprise/atalanta-core/pull/28)
- Updated the logs for balance summary in Auraservice [`388fdad`](https://github.com/Zeta-Enterprise/atalanta-core/commit/388fdada38826b61f557085be6732fd8027fbf29)
- Release 1.2.15-1 [`fdb265a`](https://github.com/Zeta-Enterprise/atalanta-core/commit/fdb265a1f81b57e4b33178ddc9c4c6c14516eb36)
- Release 1.2.15 [`db3a30c`](https://github.com/Zeta-Enterprise/atalanta-core/commit/db3a30c948cc3509ae70ef096099ad7c03c14006)

#### [1.2.14](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.2.13...1.2.14)

> 14 August 2023

- feat(TCH-15107) Added coa code to aura ganymede url [`#27`](https://github.com/Zeta-Enterprise/atalanta-core/pull/27)
- Release 1.2.14 [`5314de6`](https://github.com/Zeta-Enterprise/atalanta-core/commit/5314de63f26a78db66597afcfc7bf56dddde2a9a)

#### [1.2.13](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.2.12...1.2.13)

> 10 August 2023

- bugfix(TCT-9228) Update get ledger base url [`#26`](https://github.com/Zeta-Enterprise/atalanta-core/pull/26)
- Release 1.2.13 [`13708ad`](https://github.com/Zeta-Enterprise/atalanta-core/commit/13708ad0e169671d072ac33f3f063614c59f7a9d)

#### [1.2.12](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.2.11...1.2.12)

> 9 August 2023

- feature(TCT-9228) move failed ledger to ledger service [`#25`](https://github.com/Zeta-Enterprise/atalanta-core/pull/25)
- Release 1.2.12 [`376431d`](https://github.com/Zeta-Enterprise/atalanta-core/commit/376431da5d6d67c9a6b41eb0a6dea68fe27ed9f0)

#### [1.2.11](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.2.11-atalanta-crux-aura...1.2.11)

> 27 July 2023

- Moved from LLT to crux token for aura ganymede [`#24`](https://github.com/Zeta-Enterprise/atalanta-core/pull/24)
- Release 1.2.11 [`72763cc`](https://github.com/Zeta-Enterprise/atalanta-core/commit/72763cc9f305af5dac99db5e0508ce92f13b2aed)

#### [1.2.11-atalanta-crux-aura](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.2.10...1.2.11-atalanta-crux-aura)

> 25 July 2023

- Release 1.2.11-atalanta-crux-aura [`96a232e`](https://github.com/Zeta-Enterprise/atalanta-core/commit/96a232e5ff752c8db05bd5f8370c20e8791e01e5)
- Moved from LLT to crux token for aura ganymede [`25eda7f`](https://github.com/Zeta-Enterprise/atalanta-core/commit/25eda7fa94bc3568416bf5145f8579f777ff305d)
- fixed the version in package json [`9792687`](https://github.com/Zeta-Enterprise/atalanta-core/commit/9792687a101fdff8817d245b1759ed153ba9fbf0)

#### [1.2.10](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.2.9...1.2.10)

> 18 July 2023

- feature(TCH-11171) reverted node logger version [`#23`](https://github.com/Zeta-Enterprise/atalanta-core/pull/23)
- Release 1.2.10 [`f98937b`](https://github.com/Zeta-Enterprise/atalanta-core/commit/f98937b9200eefd57518d1c56bc6dc55ae2bddea)

#### [1.2.9](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.2.8...1.2.9)

> 18 July 2023

- Feature(TCH-11171) eod center dashboard overview failed ledger - moved getLedger api [`#22`](https://github.com/Zeta-Enterprise/atalanta-core/pull/22)
- Release 1.2.9 [`a10086d`](https://github.com/Zeta-Enterprise/atalanta-core/commit/a10086d5b07bf44d72b914187b8c5e8f1115b28d)

#### [1.2.8](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.2.7...1.2.8)

> 17 July 2023

- Updating node-logger to v2.6.5 [`#21`](https://github.com/Zeta-Enterprise/atalanta-core/pull/21)
- Release 1.2.8 [`235e795`](https://github.com/Zeta-Enterprise/atalanta-core/commit/235e7952c467ba5803cff8dd4ab66d7176c7ccab)

#### [1.2.7](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.2.6...1.2.7)

> 17 July 2023

- feat(TCH-13062): kibana logs updated [`#20`](https://github.com/Zeta-Enterprise/atalanta-core/pull/20)
- Release 1.2.7 [`f8e1e53`](https://github.com/Zeta-Enterprise/atalanta-core/commit/f8e1e53c73ff7840354e86b0a82ca6971505127b)

#### [1.2.6](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.2.5...1.2.6)

> 17 July 2023

- feat(TCH-13062): Fixed error logging [`#19`](https://github.com/Zeta-Enterprise/atalanta-core/pull/19)
- Release 1.2.6 [`220a332`](https://github.com/Zeta-Enterprise/atalanta-core/commit/220a33209434ade05a1bee54f6bad913732317c3)

#### [1.2.5](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.2.4...1.2.5)

> 13 July 2023

- Feat/handle dynamic endpoints [`#18`](https://github.com/Zeta-Enterprise/atalanta-core/pull/18)
- Release 1.2.5 [`4f152a5`](https://github.com/Zeta-Enterprise/atalanta-core/commit/4f152a50d775606e74577363bfb64a4dab643501)

#### [1.2.4](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.2.3...1.2.4)

> 13 July 2023

- Feat/handle dynamic endpoints [`#17`](https://github.com/Zeta-Enterprise/atalanta-core/pull/17)
- Release 1.2.4 [`5a86cca`](https://github.com/Zeta-Enterprise/atalanta-core/commit/5a86ccaa83b5091f67a8527d13b319c96496ea9b)

#### [1.2.3](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.2.2...1.2.3)

> 11 July 2023

- Release 1.2.3 [`21125f5`](https://github.com/Zeta-Enterprise/atalanta-core/commit/21125f55facc0bc1016f823c8752084fd1053242)
- other(TCH-13212): fix axios post bug [`cc29407`](https://github.com/Zeta-Enterprise/atalanta-core/commit/cc294070fdafc37942414b9682ae3d4e6d165f1c)

#### [1.2.2](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.2.1...1.2.2)

> 11 July 2023

- fix(TCH-13212): fix form variables type [`#16`](https://github.com/Zeta-Enterprise/atalanta-core/pull/16)
- Release 1.2.2 [`f3fec71`](https://github.com/Zeta-Enterprise/atalanta-core/commit/f3fec71f1f247b3e429501eb0cd5662d80461f62)

#### [1.2.1](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.2.0...1.2.1)

> 11 July 2023

- bugfix(TCH-13212): change getUserTaskInfoList method request type [`#15`](https://github.com/Zeta-Enterprise/atalanta-core/pull/15)
- Release 1.2.1 [`b9810ad`](https://github.com/Zeta-Enterprise/atalanta-core/commit/b9810ad8b6632ce95d2c91839022d97976cae921)

#### [1.2.0](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.1.4...1.2.0)

> 11 July 2023

- feature(TCH-13212): add ops service methods for EOD center [`#14`](https://github.com/Zeta-Enterprise/atalanta-core/pull/14)
- Release 1.2.0 [`b45bad0`](https://github.com/Zeta-Enterprise/atalanta-core/commit/b45bad0a50e5e098a87d56c17667a90cec085ded)

#### [1.1.4](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.1.3...1.1.4)

> 9 July 2023

- feature(TCH-11171) making status as optional for entities api [`#13`](https://github.com/Zeta-Enterprise/atalanta-core/pull/13)
- Release 1.1.4 [`423b2d6`](https://github.com/Zeta-Enterprise/atalanta-core/commit/423b2d6bd1bae83ec41fbe1840cf5b67d32ab4a7)

#### [1.1.3](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.1.2...1.1.3)

> 4 July 2023

- Removed the harcoded aura url in Aura service [`#12`](https://github.com/Zeta-Enterprise/atalanta-core/pull/12)
- Release 1.1.3 [`b18ec13`](https://github.com/Zeta-Enterprise/atalanta-core/commit/b18ec1303694ed159a1c5116087c24f176ff2df9)

#### [1.1.2](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.1.1...1.1.2)

> 4 July 2023

- Feat/TCH-11988 Added cycle id to coa balance summary requet params [`#11`](https://github.com/Zeta-Enterprise/atalanta-core/pull/11)
- Release 1.1.2 [`f2d0ec4`](https://github.com/Zeta-Enterprise/atalanta-core/commit/f2d0ec4680e74c58293c2e3d1a5509855d6fa079)

#### [1.1.1](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.1.0...1.1.1)

> 26 June 2023

- Release 1.1.1 [`13978cb`](https://github.com/Zeta-Enterprise/atalanta-core/commit/13978cba4d97bab4689afca74aaed81ee92435ec)

#### [1.1.0](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.0.13...1.1.0)

> 26 June 2023

- fix(TCP-1863) : Added removed console statement for release fix [`#10`](https://github.com/Zeta-Enterprise/atalanta-core/pull/10)
- feat(TCH-11988) Added Authorization decorator and Aura balance endpoint [`#7`](https://github.com/Zeta-Enterprise/atalanta-core/pull/7)
- Release 1.1.0 [`d4b922d`](https://github.com/Zeta-Enterprise/atalanta-core/commit/d4b922d9fc1ef62377f1343917c8697294ccc01a)

#### [1.0.13](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.0.12...1.0.13)

> 26 June 2023

- fix(TCH-1863): Added logger to debug get object and action groups method [`#9`](https://github.com/Zeta-Enterprise/atalanta-core/pull/9)
- Release 1.0.13 [`d97638d`](https://github.com/Zeta-Enterprise/atalanta-core/commit/d97638d22203027258b75a93b787c416c186ca76)

#### [1.0.12](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.0.11...1.0.12)

> 25 June 2023

- fix(TCH-10906) - skip ledger api return type fix [`e9f9970`](https://github.com/Zeta-Enterprise/atalanta-core/commit/e9f99702b9f4e6aac3bf6a0eb55c0fd9be59e35d)
- Release 1.0.12 [`46ee3b3`](https://github.com/Zeta-Enterprise/atalanta-core/commit/46ee3b31b6f9c7063efda2e05433c714adddd885)

#### [1.0.11](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.0.10...1.0.11)

> 25 June 2023

- Release 1.0.11 [`139c01d`](https://github.com/Zeta-Enterprise/atalanta-core/commit/139c01dc73e43a1a0bf57fc30ccfebd6345484df)
- fix(TCH-10907) - Bearer getting appended twice fixed [`3f26a83`](https://github.com/Zeta-Enterprise/atalanta-core/commit/3f26a83ef94533445b05d8f32adb90bfa15d988c)

#### [1.0.10](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.0.9...1.0.10)

> 22 June 2023

- Enabling Schema validation [`#8`](https://github.com/Zeta-Enterprise/atalanta-core/pull/8)
- Release 1.0.10 [`3e7a412`](https://github.com/Zeta-Enterprise/atalanta-core/commit/3e7a412827a8654a09007c31f953afb36ba06cef)

#### [1.0.9](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.0.8...1.0.9)

> 19 June 2023

- feat(TCH-10906) - get entities at tracker level method added [`b0faa51`](https://github.com/Zeta-Enterprise/atalanta-core/commit/b0faa512b1156e515ba3bb9be93ee79caf3db57a)
- Release 1.0.9 [`e1589a5`](https://github.com/Zeta-Enterprise/atalanta-core/commit/e1589a571050c9e18b7cd3d6ea0ad351e3f69bae)

#### [1.0.8](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.0.7...1.0.8)

> 12 June 2023

- feature(TCH-8553): APIs base services added for failed ledgers [`#6`](https://github.com/Zeta-Enterprise/atalanta-core/pull/6)
- Release 1.0.8 [`9acd667`](https://github.com/Zeta-Enterprise/atalanta-core/commit/9acd667a8b0b2ffaae9469832375e10fdce185e3)

#### [1.0.7](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.0.6...1.0.7)

> 8 June 2023

- fix(TCH-10907) - Patch call not working fix [`2310b20`](https://github.com/Zeta-Enterprise/atalanta-core/commit/2310b203fbef3e72c909e570a6a8eeb36e8c3e54)
- Release 1.0.7 [`3faeb3f`](https://github.com/Zeta-Enterprise/atalanta-core/commit/3faeb3f9ad4993bca0952e7511779cf177a2a996)

#### [1.0.6](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.0.5...1.0.6)

> 7 June 2023

- feat(TCH-10907) - Skip ledgers API added [`#5`](https://github.com/Zeta-Enterprise/atalanta-core/pull/5)
- Release 1.0.6 [`fa058f3`](https://github.com/Zeta-Enterprise/atalanta-core/commit/fa058f33a04e2d08f7d257b2a96113e8f78a5e03)

#### [1.0.5](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.0.4...1.0.5)

> 25 May 2023

- other(TCH-9458): Added missing params [`#4`](https://github.com/Zeta-Enterprise/atalanta-core/pull/4)
- Release 1.0.5 [`31e7ca3`](https://github.com/Zeta-Enterprise/atalanta-core/commit/31e7ca306faf8a213c73e5437af229c11dc746a0)

#### [1.0.4](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.0.3...1.0.4)

> 25 May 2023

- Release 1.0.4 [`4720303`](https://github.com/Zeta-Enterprise/atalanta-core/commit/472030351857e7bf7889b5b170deedd75309da7e)
- other(TCH-9458): fixed interface for Periods List promise [`cfad204`](https://github.com/Zeta-Enterprise/atalanta-core/commit/cfad2044d35676b70b973b6481d0ddc57bd2a54f)

#### [1.0.3](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.0.2...1.0.3)

> 25 May 2023

- feat(TCH-9458): added tachyon service methods for calendar APIs [`#3`](https://github.com/Zeta-Enterprise/atalanta-core/pull/3)
- Release 1.0.3 [`592f941`](https://github.com/Zeta-Enterprise/atalanta-core/commit/592f941dba47ff87d485eab95f8c6fc2cfc1d30b)

#### [1.0.2](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.0.1...1.0.2)

> 17 May 2023

- Feature(TCH-10658) Added new "not executed" status [`#2`](https://github.com/Zeta-Enterprise/atalanta-core/pull/2)
- Release 1.0.2 [`d3d03c7`](https://github.com/Zeta-Enterprise/atalanta-core/commit/d3d03c757a9ed032809591c8bbf6ea1af6a3e961)

#### [1.0.1](https://github.com/Zeta-Enterprise/atalanta-core/compare/1.0.0...1.0.1)

> 12 May 2023

- feature(TCH-10370): add workerSuccessExecutionID type to WorkerObject [`#1`](https://github.com/Zeta-Enterprise/atalanta-core/pull/1)
- Release 1.0.1 [`3427f19`](https://github.com/Zeta-Enterprise/atalanta-core/commit/3427f19d9d0fb97dabbdc2e6804bae758d6bb6b1)

### [1.0.0](https://github.com/Zeta-Enterprise/atalanta-core/compare/0.0.25...1.0.0)

> 24 February 2023

- Release 1.0.0 [`76da633`](https://github.com/Zeta-Enterprise/atalanta-core/commit/76da633e07e899921c8067ce260640e6db4cc998)
- Release 0.0.25-snapshot [`b2925dc`](https://github.com/Zeta-Enterprise/atalanta-core/commit/b2925dc220bd0fa08782a843c66b878755e7dc4e)
- refersh commit [`7f439d6`](https://github.com/Zeta-Enterprise/atalanta-core/commit/7f439d6b8ad6f890ae3640b6e541359e487cfa45)

#### [0.0.25](https://github.com/Zeta-Enterprise/atalanta-core/compare/0.0.25-snapshot...0.0.25)

> 24 February 2023

#### [0.0.25-snapshot](https://github.com/Zeta-Enterprise/atalanta-core/compare/0.0.24...0.0.25-snapshot)

> 24 February 2023

- Release 0.0.25 [`5bfbbcb`](https://github.com/Zeta-Enterprise/atalanta-core/commit/5bfbbcb1cbfb4b9956b3ae17a2f74abf41341034)
- Release 0.0.25-snapshot [`b2925dc`](https://github.com/Zeta-Enterprise/atalanta-core/commit/b2925dc220bd0fa08782a843c66b878755e7dc4e)
- refersh commit [`7f439d6`](https://github.com/Zeta-Enterprise/atalanta-core/commit/7f439d6b8ad6f890ae3640b6e541359e487cfa45)

#### [0.0.24](https://github.com/Zeta-Enterprise/atalanta-core/compare/0.0.23...0.0.24)

> 24 February 2023

- Release 0.0.24 [`ede246c`](https://github.com/Zeta-Enterprise/atalanta-core/commit/ede246c161d16b6d7e9f7e9fb12833a564fa9eb7)
- refresh commit [`db40497`](https://github.com/Zeta-Enterprise/atalanta-core/commit/db404975e435458dd96cc75266a84b1fef9559d5)

#### [0.0.23](https://github.com/Zeta-Enterprise/atalanta-core/compare/0.0.22...0.0.23)

> 24 February 2023

- Release 0.0.23 [`863f375`](https://github.com/Zeta-Enterprise/atalanta-core/commit/863f3755682c31565ab1334241a6bfbbd797ac80)
- jenkins debug job [`399b874`](https://github.com/Zeta-Enterprise/atalanta-core/commit/399b8749524e5f1e8f532d53f9318b1259e6ed35)

#### [0.0.22](https://github.com/Zeta-Enterprise/atalanta-core/compare/0.0.20...0.0.22)

> 23 February 2023

- Release 0.0.22 [`216af1f`](https://github.com/Zeta-Enterprise/atalanta-core/commit/216af1fac54ac870ff1d56dbf50590e88ebc6dea)
- updated version [`74b37fd`](https://github.com/Zeta-Enterprise/atalanta-core/commit/74b37fd6ab0f2cd59fb60513070ff8c8788aaad4)

#### [0.0.20](https://github.com/Zeta-Enterprise/atalanta-core/compare/0.0.17...0.0.20)

> 23 February 2023

- Release 0.0.20 [`95920a1`](https://github.com/Zeta-Enterprise/atalanta-core/commit/95920a1b81b4053af33d3fe1ae8834dc55243d9e)
- latest changes sync [`6318612`](https://github.com/Zeta-Enterprise/atalanta-core/commit/6318612f81ac5a4b3c3aff23820641605dbd1390)
- added coverage folder in package.json [`b83bae2`](https://github.com/Zeta-Enterprise/atalanta-core/commit/b83bae2e98cb77e545ec41e0a8d020c5e9e89fb1)

#### [0.0.17](https://github.com/Zeta-Enterprise/atalanta-core/compare/0.0.16...0.0.17)

> 22 February 2023

- unit test coverage added [`7a54ebd`](https://github.com/Zeta-Enterprise/atalanta-core/commit/7a54ebd8adc3132ca4d5f3698be73015bc7f6c65)
- Release 0.0.17 [`b0aa5ee`](https://github.com/Zeta-Enterprise/atalanta-core/commit/b0aa5ee9fa413d35a4a0520308163fc06a073e28)

#### [0.0.16](https://github.com/Zeta-Enterprise/atalanta-core/compare/0.0.15...0.0.16)

> 9 February 2023

- app depdency removed from child modules [`4939c4a`](https://github.com/Zeta-Enterprise/atalanta-core/commit/4939c4a767902233d2aceb79cf5f1f2e8c4cb2a7)
- Release 0.0.16 [`ddb6997`](https://github.com/Zeta-Enterprise/atalanta-core/commit/ddb699740efcd1b0032ed5a57b70874379a04b28)

#### [0.0.15](https://github.com/Zeta-Enterprise/atalanta-core/compare/0.0.14...0.0.15)

> 24 January 2023

- Release 0.0.15 [`bb25a27`](https://github.com/Zeta-Enterprise/atalanta-core/commit/bb25a2785adec6008573868ab9772aa7a7eb3ef8)
- request interceptor order changed [`b644b89`](https://github.com/Zeta-Enterprise/atalanta-core/commit/b644b89c19db810d02575b0bf207657fccb8fd01)

#### [0.0.14](https://github.com/Zeta-Enterprise/atalanta-core/compare/0.0.13...0.0.14)

> 24 January 2023

- Release 0.0.14 [`86cf6be`](https://github.com/Zeta-Enterprise/atalanta-core/commit/86cf6beb27b0416ec21003c0f9e38cff422104e7)
- removed subpath export [`1c18b4e`](https://github.com/Zeta-Enterprise/atalanta-core/commit/1c18b4e278091ec23fb14664cf24acc03be8ffec)
- added request interceptor [`28d6790`](https://github.com/Zeta-Enterprise/atalanta-core/commit/28d679047b239519adf9810a595fd728a3e987d8)

#### [0.0.13](https://github.com/Zeta-Enterprise/atalanta-core/compare/0.0.12...0.0.13)

> 24 January 2023

- latest pull [`bb79dd9`](https://github.com/Zeta-Enterprise/atalanta-core/commit/bb79dd90be532992cd96de55b164a28bb7a58c96)
- Release 0.0.13 [`336f2f3`](https://github.com/Zeta-Enterprise/atalanta-core/commit/336f2f39a95ff8a6c705cace2c9cdf2e37c9c928)

#### [0.0.12](https://github.com/Zeta-Enterprise/atalanta-core/compare/0.0.11...0.0.12)

> 23 January 2023

- Release 0.0.12 [`286f68e`](https://github.com/Zeta-Enterprise/atalanta-core/commit/286f68e51f4e08fc07868af1f05c8433b9abc6f7)
- added subpath exports [`d835375`](https://github.com/Zeta-Enterprise/atalanta-core/commit/d83537518ce5af8ee3d8e21218770f4d4866678a)

#### [0.0.11](https://github.com/Zeta-Enterprise/atalanta-core/compare/0.0.10...0.0.11)

> 17 January 2023

- Release 0.0.11 [`2047655`](https://github.com/Zeta-Enterprise/atalanta-core/commit/2047655f8e4aaf65acf853c8043eff96adfb205e)

#### [0.0.10](https://github.com/Zeta-Enterprise/atalanta-core/compare/0.0.9...0.0.10)

> 17 January 2023

- Release 0.0.10 [`d669fe6`](https://github.com/Zeta-Enterprise/atalanta-core/commit/d669fe6daf74a53be8a47f0e0f7ea1536c3543ee)
- testing prebuild script [`f2ef9cb`](https://github.com/Zeta-Enterprise/atalanta-core/commit/f2ef9cb6425c3ff4b8b92feb0ad3eb80e38c501c)

#### [0.0.9](https://github.com/Zeta-Enterprise/atalanta-core/compare/0.0.8...0.0.9)

> 16 January 2023

- Release 0.0.9 [`2bd9e19`](https://github.com/Zeta-Enterprise/atalanta-core/commit/2bd9e193eb7789fb700c5b52a2039520deae3e34)
- removed npm i from build command [`588ed61`](https://github.com/Zeta-Enterprise/atalanta-core/commit/588ed6172113f5218e086e0dfa6dd89522c90317)

#### [0.0.8](https://github.com/Zeta-Enterprise/atalanta-core/compare/0.0.7...0.0.8)

> 16 January 2023

- dynamic env parameter added [`3be39c9`](https://github.com/Zeta-Enterprise/atalanta-core/commit/3be39c9ab97492ee4f6e895480910149c64d5e28)
- Release 0.0.8 [`8f2f659`](https://github.com/Zeta-Enterprise/atalanta-core/commit/8f2f6598f81337bde61fd846828d2cc08deebd3d)

#### [0.0.7](https://github.com/Zeta-Enterprise/atalanta-core/compare/0.0.6...0.0.7)

> 16 January 2023

- Release 0.0.7 [`ec403bc`](https://github.com/Zeta-Enterprise/atalanta-core/commit/ec403bc9aac5fd317ad11c61667b89d9da975040)
- postbuild script added [`86b26d0`](https://github.com/Zeta-Enterprise/atalanta-core/commit/86b26d0e7adca63492502b545afd6f7f912ca538)

#### [0.0.6](https://github.com/Zeta-Enterprise/atalanta-core/compare/0.0.5...0.0.6)

> 16 January 2023

- Release 0.0.6 [`7b55530`](https://github.com/Zeta-Enterprise/atalanta-core/commit/7b555304f5fe3ddd54637069f95313f23ee67f5a)
- testing build command [`fe91414`](https://github.com/Zeta-Enterprise/atalanta-core/commit/fe91414bede478b7e2e24b03fd3eaf9a639ba277)

#### [0.0.5](https://github.com/Zeta-Enterprise/atalanta-core/compare/0.0.4...0.0.5)

> 16 January 2023

- deleted dockerfile [`2becdf3`](https://github.com/Zeta-Enterprise/atalanta-core/commit/2becdf387b32111259a055cac22d1b93199712bf)
- Release 0.0.5 [`22a53a1`](https://github.com/Zeta-Enterprise/atalanta-core/commit/22a53a15eec0b84591d1a657e3199534806f64b4)

#### [0.0.4](https://github.com/Zeta-Enterprise/atalanta-core/compare/0.0.3...0.0.4)

> 16 January 2023

- added lock file [`42a7689`](https://github.com/Zeta-Enterprise/atalanta-core/commit/42a768922b6d5321e71db0c8817a2c5c0dfa9867)
- Release 0.0.4 [`735855e`](https://github.com/Zeta-Enterprise/atalanta-core/commit/735855e6de733a48f3410cc425a67c993ec45c73)

#### [0.0.3](https://github.com/Zeta-Enterprise/atalanta-core/compare/0.0.2...0.0.3)

> 16 January 2023

- deleted lock [`cf763c0`](https://github.com/Zeta-Enterprise/atalanta-core/commit/cf763c031b8a27761b48b7e616733d92b9e8e031)
- Release 0.0.3 [`ae7e015`](https://github.com/Zeta-Enterprise/atalanta-core/commit/ae7e015a8734419d33b8bb94999eed1457db9a29)

#### 0.0.2

> 16 January 2023

- first commit [`31d6d13`](https://github.com/Zeta-Enterprise/atalanta-core/commit/31d6d1397030d07df41c74cfb8721ba39122c766)
- Release 0.0.2 [`81ae02c`](https://github.com/Zeta-Enterprise/atalanta-core/commit/81ae02c4de5bf2265ff6e050f54a4ddaffeeb1ba)
- Initial commit [`b88615d`](https://github.com/Zeta-Enterprise/atalanta-core/commit/b88615d1e3439ea3b1ca20aa2ec9e2d10e665d68)
