{"total": {"lines": {"total": 108, "covered": 105, "skipped": 0, "pct": 97.22}, "statements": {"total": 117, "covered": 114, "skipped": 0, "pct": 97.43}, "functions": {"total": 15, "covered": 14, "skipped": 0, "pct": 93.33}, "branches": {"total": 17, "covered": 14, "skipped": 0, "pct": 82.35}, "branchesTrue": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "/Users/<USER>/atalanta-core/src/App.ts": {"lines": {"total": 24, "covered": 23, "skipped": 0, "pct": 95.83}, "functions": {"total": 3, "covered": 3, "skipped": 0, "pct": 100}, "statements": {"total": 26, "covered": 25, "skipped": 0, "pct": 96.15}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "/Users/<USER>/atalanta-core/src/Constants.ts": {"lines": {"total": 17, "covered": 17, "skipped": 0, "pct": 100}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 17, "covered": 17, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "/Users/<USER>/atalanta-core/src/Env.ts": {"lines": {"total": 11, "covered": 11, "skipped": 0, "pct": 100}, "functions": {"total": 1, "covered": 1, "skipped": 0, "pct": 100}, "statements": {"total": 12, "covered": 12, "skipped": 0, "pct": 100}, "branches": {"total": 6, "covered": 5, "skipped": 0, "pct": 83.33}}, "/Users/<USER>/atalanta-core/src/ErrorResponseHandler.ts": {"lines": {"total": 14, "covered": 14, "skipped": 0, "pct": 100}, "functions": {"total": 2, "covered": 2, "skipped": 0, "pct": 100}, "statements": {"total": 16, "covered": 16, "skipped": 0, "pct": 100}, "branches": {"total": 4, "covered": 3, "skipped": 0, "pct": 75}}, "/Users/<USER>/atalanta-core/src/HerculesHealthChcek.ts": {"lines": {"total": 7, "covered": 7, "skipped": 0, "pct": 100}, "functions": {"total": 2, "covered": 2, "skipped": 0, "pct": 100}, "statements": {"total": 8, "covered": 8, "skipped": 0, "pct": 100}, "branches": {"total": 2, "covered": 2, "skipped": 0, "pct": 100}}, "/Users/<USER>/atalanta-core/src/Ping.ts": {"lines": {"total": 4, "covered": 4, "skipped": 0, "pct": 100}, "functions": {"total": 2, "covered": 2, "skipped": 0, "pct": 100}, "statements": {"total": 5, "covered": 5, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "/Users/<USER>/atalanta-core/src/Server.ts": {"lines": {"total": 3, "covered": 2, "skipped": 0, "pct": 66.66}, "functions": {"total": 2, "covered": 1, "skipped": 0, "pct": 50}, "statements": {"total": 4, "covered": 3, "skipped": 0, "pct": 75}, "branches": {"total": 2, "covered": 2, "skipped": 0, "pct": 100}}, "/Users/<USER>/atalanta-core/src/System.ts": {"lines": {"total": 18, "covered": 18, "skipped": 0, "pct": 100}, "functions": {"total": 2, "covered": 2, "skipped": 0, "pct": 100}, "statements": {"total": 19, "covered": 19, "skipped": 0, "pct": 100}, "branches": {"total": 2, "covered": 1, "skipped": 0, "pct": 50}}, "/Users/<USER>/atalanta-core/src/exceptions/BaseServiceException.ts": {"lines": {"total": 10, "covered": 9, "skipped": 0, "pct": 90}, "functions": {"total": 1, "covered": 1, "skipped": 0, "pct": 100}, "statements": {"total": 10, "covered": 9, "skipped": 0, "pct": 90}, "branches": {"total": 1, "covered": 1, "skipped": 0, "pct": 100}}}