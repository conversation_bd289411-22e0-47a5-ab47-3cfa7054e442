{"name": "@zeta-atalanta/core", "version": "1.6.6", "description": "Atalanta core library for all modules", "prettier": "@zeta/prettier-config", "main": "dist/App.js", "types": "dist/App.d.ts", "sideEffects": false, "files": ["dist", ".env", ".env.local", "coverage"], "scripts": {"prebuild": "rm -rf dist", "build": "tsc -p tsconfig.json", "postbuild": "tsc --declaration --emitDeclarationOnly", "format": "prettier './**/*.(ts|js|json|html)' --write", "release": "release-it", "prepare": "husky install", "test": "jest"}, "dependencies": {"@zeta/node-logger": "^2.8.8", "ajv": "^8.11.0", "axios": "^1.4.0", "body-parser": "^1.19.0", "compression": "^1.7.4", "cors": "^2.8.5", "dayjs": "^1.11.5", "dot-prop": "^6.0.1", "dotenv-extended": "^2.9.0", "ejs": "^3.1.10", "email-validator": "^2.0.4", "express": "^4.19.2", "express-sse": "0.5.3", "express-validator": "^7.2.0", "helmet": "^8.0.0", "http-status-codes": "^2.1.4", "js-yaml": "^4.1.0", "json-bigint": "1.0.0", "lodash": "^4.17.21", "lru-cache": "^7.14.1", "xss": "^1.0.15"}, "devDependencies": {"@types/compression": "^1.7.2", "@types/cors": "^2.8.12", "@types/ejs": "^3.0.5", "@types/express": "^4.17.11", "@types/jest": "^29.2.6", "@types/js-yaml": "^4.0.5", "@types/json-bigint": "^1.0.1", "@types/module-alias": "^2.0.0", "@types/node": "^14.14.22", "@zeta/prettier-config": "^1.0.0", "husky": "^8.0.1", "jest": "^29.4.0", "jest-express": "^1.12.0", "lint-staged": "^12.4.2", "module-alias": "^2.2.2", "nodemon": "^2.0.7", "prettier": "^2.6.2", "release-it": "^15.5.0", "ts-jest": "^29.0.5", "typescript": "^4.1.3"}, "release-it": {"npm": {"publish": false}, "git": {"changelog": "npx auto-changelog --stdout --commit-limit false -u --template https://raw.githubusercontent.com/release-it/release-it/master/templates/changelog-compact.hbs"}, "hooks": {"after:bump": "npx auto-changelog -p"}}, "lint-staged": {"*.*(ts|js|json|html)": "npm run format"}}