<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <link rel="stylesheet" href="https://banking-assets.s3.ap-south-1.amazonaws.com/lib/latest/zeta.min.css">
    <style>
        html, body {
            height: 100%;
        }
        .right, .left {
            height: 100vh;
            vertical-align: middle;
            justify-content: center;
            display: flex;
            flex-direction: column;
        }

        .left {
            text-align: center;
            color: white;
            text-transform: capitalize;
        }

        .right {
            background-color: white;
        }

        .left::after {
            content: '';
            position: absolute;
            margin-left: 48%;
            /* height: 30px; */
            /* width: 30px; */
            /* background: red; */
            border-top: 25px solid transparent;
            border-left: 50px solid #633ea5;
            border-bottom: 25px solid transparent;
        }

        .right form {
            max-width: 450px;
            min-width: 400px;
            margin: 0px auto;
            background: white;
            padding: 140px 40px;
            border-radius: 5px;
            box-shadow: 0px 0px 10px lightgrey;
        }

        body {
            background-color: #633ea5;
        }

        .padding-less {
            padding: 0 !important;
        }

        .margin-less {
            margin: 0 !important;
        }

    </style>
</head>
<body> 
    <div class="">
        <div class="columns margin-less">
            <div class="column is-vcentered left padding-less">
                <span class="is-size-2">Sign Up</span>
                <span class="is-size-3"><br></span>
                <span class="is-size-2"><%= toolName %></span>
            </div>
            <div class="column is-vcentered right padding-less">