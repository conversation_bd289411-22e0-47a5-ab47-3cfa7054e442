import { Express, Router } from 'express';

export interface AppModule {
    name: string;
    setup: (router: Router) => Router;
}


export interface InitOptions {
    envFilePath: string;
}

export interface TemplateData {
    [k: string]: string;
}

export interface GetAuthResponse {
    userResourceJid: string;
    sessionId: string;
    validTill: number;
    isSensitiveSession: boolean;
    scopes: string[];
    sandbox: string;
    tenant: string;
}