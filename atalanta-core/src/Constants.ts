export const PORT = 'port';
export const DEFAULT_PORT = 3000;

export const CACHE_ACTIONS_SIZE = 150;
export const CACHE_ACTIONS_TTL = 1000 * 60 * 60 * 24;
export const CACHE_LAST_STORED_SIZE = 15;
export const CACHE_LAST_STORED_TTL = 1000 * 60 * 60 * 24;

export const ALLOWED_ORIGINS = '*';
export const ALLOWED_METHODS = ['GET', 'PUT', 'PATCH', 'POST', 'DELETE'];
export const ALLOWED_HEADERS = [
    'Origin',
    'X-Requested-With',
    'Content-Type',
    'Accept',
    'x-zeta-authtoken',
    'x-access-token',
    'authorization',
];

export const STATUS = {
    SUCCEEDED: 'SUCCEEDED',
    FAILED: 'FAILED',
    FAILED_RETRIED: 'FAILED_RETRIED',
    COMPLETED: 'COMPLETED',
    SUCCESS: 'SUCCESS',
    PROGRESS: 'In Progress',
    SUCCESSFUL: 'Successful',
    NOT_EXECUTED: 'Not Executed',
};

export const TRIGGERVALUEMAP: any = {
    initiateBOP: 'BOPI',
    initiateBOF: 'BOFI',
    activate: 'ACTIVE',
    initiateEOP: 'EOPI',
    initiateEOF: 'EOFI',
    close: 'CLOSED',
};

export const PHASESTATUSMAP: any = {
    BOPI: 'initiateBOP',
    BOFI: 'initiateBOF',
    ACTIVE: 'activate',
    EOPI: 'initiateEOP',
    EOFI: 'initiateEOF',
    CLOSED: 'close',
};

export const COA_TRIGGER_STATUS_RESPONSE = {
    status: '-',
    startTime: '-',
    transitionMap: {
        INITIATED: {
            startTime: '-',
        },
        '-': {
            startTime: '-',
        },
    },
    entityTrackersProgress: [
        {
            status: '-',
            startTime: '-',
            transitionMap: {
                INITIATED: {
                    startTime: '-',
                },
                '-': {
                    startTime: '-',
                },
            },
        },
    ],
};

// DeclinedTransactions
export const VOUCHER_CODE_KEY = 'journal.voucherCode';
export const DATETIME_FORMAT = 'DD MMM YYYY, HH:mm:ss';
export const VALUE_TIME_KEY = 'super-card.valueTime';
export const POSTING_CODE_KEY = 'metaPosting.postingCode';

export const X_ZETA_AUTHTOKEN = 'x-zeta-authtoken';
export const AUTHORIZATION_HEADER = 'Authorization';

export const CONNECTION_REQUEST_TYPE = {
    statusChange: 'STATUS_CHANGE',
    publish: 'PUBLISH',
    updateConnections: 'UPDATE_CONNECTIONS'
}