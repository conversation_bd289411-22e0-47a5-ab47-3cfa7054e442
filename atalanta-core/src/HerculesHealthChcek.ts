import { Express, Request, Response } from 'express';

export const setupHealthCheck = (app: Express, path?: string) => {
    app.get(`${path || '/health'}`, (_req: Request, res: Response) => {
        const used: any = process.memoryUsage();
        const memory: any = {};

        for (const key in used) {
            memory[key] = `${Math.round((used[key] / 1024 / 1024) * 100) / 100} MB`;
        }

        res.status(200).json({
            uptime: process.uptime(),
            status: 'ok',
            mem: memory,
            version: process.env.npm_package_version,
            cpu: process.cpuUsage(),
        });
    });
};
