import { Express } from 'express';
import http from 'http';

/**
 * Starts the Express application inside an HTTP server.
 *
 * Why we use `http.createServer(app)` instead of `app.listen()`:
 * Express' `app.listen()` is a shortcut that creates an internal HTTP server
 * and starts listening, but it doesn't expose the server instance.
 *
 * To enable graceful shutdown (important for handling SIGTERM in Kubernetes),
 * we need access to the underlying HTTP server instance so that we can call
 * `server.close()` to:
 *  - Stop accepting new connections
 *  - Wait for ongoing connections/requests to complete before shutting down
 *
 * A `POST /shutdown` endpoint is also exposed to allow Kubernetes to trigger
 * readiness=false using a preStop hook, preventing race conditions between
 * readiness probes and SIGTERM.
 */

export const start = (app: Express) => {
    const port = process.env.PORT || 3000;
    const server = http.createServer(app);
    let inFlightRequests = 0;
    let shuttingDown = false;

    // Default logger used if request logger not available
    const getLogger = (req: any) => req?.getLogger?.('ServerLifecycle') ?? console;

    // --- Readiness probe endpoint ---
    app.get('/ready', (req, res) => {
        const logger = getLogger(req);
        if (shuttingDown) {
            logger.info?.('Readiness probe failed - shutting down');
            return res.status(500).send('Shutting down');
        }
        logger.info?.('Readiness probe passed');
        res.status(200).send('OK');
    });

    // --- Shutdown signal from preStop hook ---
    app.post('/shutdown', (req, res) => {
        const logger = getLogger(req);
        const remoteAddress = req.socket.remoteAddress;
    
        logger.info?.(`Shutdown requested from ${remoteAddress}`);
    
        if (remoteAddress !== '127.0.0.1' && remoteAddress !== '::1') {
            logger.warn?.('Unauthorized shutdown attempt rejected.');
            return res.status(403).send('Forbidden');
        }
    
        if (!shuttingDown) {
            logger.info?.('Shutdown signal received from preStop hook.');
            shuttingDown = true;
        }
    
        res.status(200).send('Marked as shutting down');
    });

    // Track in-flight requests
    app.use((req, res, next) => {
        const logger = getLogger(req);
        if (shuttingDown) {
            // Graceful shutdown in progress — reject new incoming requests
            // Instruct client/proxies not to reuse this connection (disable keep-alive)
            // This helps prevent new requests from being routed to this pod
            res.setHeader('Connection', 'close');
            logger.warn?.('Rejecting request during shutdown', {
                method: req.method,
                url: req.originalUrl,
            });
            // Respond with 503 Service Unavailable to signal that we're going down
            // Many load balancers or clients will retry the request on another pod
            return res.status(503).send('Server is shutting down');
        }

        inFlightRequests++;
        logger.info?.('Request started', {
            method: req.method,
            url: req.originalUrl,
            inFlightRequests,
        });

        const decrement = (() => {
            // Avoid double-decrementing inFlightRequests (edge case):
            // 1. Both 'finish' and 'close' may fire in certain environments (though rare).
            // 2. Guard against multiple calls to decrement.
            let called = false;
            return () => {
                if (!called) {
                    called = true;
                    inFlightRequests--;
                    logger.info?.('Request ended', {
                        method: req.method,
                        url: req.originalUrl,
                        inFlightRequests,
                    });
                }
            };
        })();

        res.on('finish', decrement); // normal case
        res.on('close', decrement); // aborted case

        next();
    });

    server.listen(port, () => {
        const logger = console;
        logger.info?.(`App is running at http://localhost:${port} in ${app.get('env')} mode`);
    });

    // Shared shutdown logic
    const initiateShutdown = (signal: string) => {
        const logger = console;
        logger.info?.(`${signal} received. Shutting down gracefully...`);
        shuttingDown = true;

        server.close(() => {
            logger.info?.('Closed server. No longer accepting new connections.');
        });

        const shutdownGracefully = () => {
            if (inFlightRequests === 0) {
                logger.info?.('All in-flight requests finished. Exiting.');
                process.exit(0);
            } else {
                logger.info?.(`${inFlightRequests} request(s) still in-flight. Waiting...`);
                setTimeout(shutdownGracefully, 1000);
            }
        };

        shutdownGracefully();

        const timeout = Number(process.env.SHUTDOWN_TIMEOUT_MS || '25000');
        setTimeout(() => {
            logger.error?.('Forcing shutdown after timeout.');
            process.exit(1);
        }, timeout);
    };

    process.on('SIGTERM', () => initiateShutdown('SIGTERM'));
    process.on('SIGINT', () => initiateShutdown('SIGINT'));
};
