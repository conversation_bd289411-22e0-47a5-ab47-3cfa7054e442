import {
    OrchestraRunResponse,
    SkipLedgersPayload,
    SkipLegdersRequest,
    OrchestraRunRequestObj,
    EntitiesRequestParams,
    EntitiesQueryParams,
    EntitiesResponse,
    SkipEntityResponse,
} from './Interfaces/Aura';
import J<PERSON><PERSON>BigIntMod from 'json-bigint';
import { Request } from 'express';
import { BaseService } from './BaseService';
import { InternalAPIException } from '../exceptions/InternalAPIException';
import axios from 'axios';
import {
    WorkerMasterListQueryParams,
    WorkerMasterListRequestParams,
    WorkerMasterListResponse,
} from './Interfaces/Orchestra';
import { createErrorObj } from './../utils';

const JSONBigInt = JSONBigIntMod({ storeAsString: true });

export class OrchestraService extends BaseService {
    constructor(req: Request) {
        super(req, {
            // to support custom orchestra url needed for backrub
            baseURL:
                req?.query?.isBackrub && process.env.ORCHESTRA_BACKRUB_URL
                    ? process.env.ORCHESTRA_BACKRUB_URL
                    : process.env.ORCHESTRA_URL,
            headers: {
                'Content-Type': 'application/json',
            },
            transformRequest: (data: any) => (data ? JSONBigInt.parse(data) : data),
            transformResponse: (data: any) => (data ? JSONBigInt.parse(data) : data),
        });
    }

    public async getOrchestraRunData(requestParams: OrchestraRunRequestObj): Promise<OrchestraRunResponse> {
        const { tenantId, coaId, phaseId, pageId, includeWorkerRuns, cbuId, pageSize, jobId, sortBy, sortOrder } =
            requestParams;
        try {
            const token = this.req.headers.authorization.split(' ')[1];
            this.req.getLogger(__filename).info('Fetching orchestra run data', requestParams);
            const response = await this.axios.get(`/v1/tenants/${tenantId}/coas/${coaId}/orchestraRuns`, {
                headers: { Authorization: `Bearer ${token}` },
                params: {
                    phaseID: phaseId,
                    includeWorkerRuns: includeWorkerRuns,
                    pageID: pageId,
                    pageSize: pageSize,
                    jobID: jobId,
                    cbuID: cbuId,
                    sortBy,
                    sortOrder,
                },
            });
            return response.data as OrchestraRunResponse;
        } catch (error) {
            this.req.getLogger(__filename).error(error);
            throw new InternalAPIException({
                api: 'getOrchestraRunData',
                error: createErrorObj(error),
            });
        }
    }

    async skipLedgersForAllWorkers(
        requestParams: SkipLegdersRequest,
        payload: SkipLedgersPayload,
    ): Promise<SkipEntityResponse> {
        const { tenantId, coaId, periodId, phase, ledgerId } = requestParams;
        try {
            const token = this.req.headers.authorization.split(' ')[1];
            this.req.getLogger(__filename).info('Skipping ledgers for all workers', requestParams);
            const response = await this.axios.request({
                url: `/v1/tenants/${tenantId}/coas/${coaId}/periods/${periodId}/phases/${phase}/entityTypes/ledger/entities/${ledgerId}`,
                method: 'PATCH',
                headers: { Authorization: `Bearer ${token}`, 'Content-Type': 'application/json' },
                data: JSON.stringify(payload),
                transformRequest: axios.defaults.transformRequest,
                transformResponse: axios.defaults.transformResponse,
            });

            return response.data as SkipEntityResponse;
        } catch (error) {
            this.req.getLogger(__filename).error(error);
            throw new InternalAPIException({
                api: 'skipLedgersForAllWorkers',
                error: createErrorObj(error),
            });
        }
    }

    async skipLedgersForSingleWorker(
        requestParams: SkipLegdersRequest,
        payload: SkipLedgersPayload,
    ): Promise<SkipEntityResponse> {
        const { tenantId, coaId, periodId, phase, ledgerId, worker } = requestParams;
        try {
            const token = this.req.headers.authorization.split(' ')[1];
            this.req.getLogger(__filename).info('Skipping ledger for single worker', requestParams);
            const response = await this.axios.request({
                url: `/v1/tenants/${tenantId}/coas/${coaId}/periods/${periodId}/phases/${phase}/entityTypes/ledger/workers/${worker}/entities/${ledgerId}`,
                method: 'PATCH',
                headers: { Authorization: `Bearer ${token}`, 'Content-Type': 'application/json' },
                data: JSON.stringify(payload),
                transformRequest: axios.defaults.transformRequest,
                transformResponse: axios.defaults.transformResponse,
            });

            return response.data as SkipEntityResponse;
        } catch (error) {
            this.req.getLogger(__filename).error(error);
            throw new InternalAPIException({
                api: 'skipLedgersForSingleWorker',
                error: createErrorObj(error),
            });
        }
    }

    public async getEntities(
        requestParams: EntitiesRequestParams,
        pageQueryParams: EntitiesQueryParams,
    ): Promise<EntitiesResponse> {
        const { tenantId, coaId, periodId, phaseId, entityType, workerId } = requestParams;
        try {
            const token = this.req.headers.authorization.split(' ')[1];
            this.req.getLogger(__filename).info('Fetching entities', requestParams);
            const response = await this.axios.get(
                `v1/tenants/${tenantId}/coas/${coaId}/periods/${periodId}/phases/${phaseId}/entityTypes/${entityType}/workers/${workerId}/entities`,
                {
                    headers: { Authorization: `Bearer ${token}` },
                    ...(pageQueryParams && { params: pageQueryParams }),
                },
            );
            return response.data as EntitiesResponse;
        } catch (error) {
            this.req.getLogger(__filename).error(error);
            throw new InternalAPIException({
                api: 'getEntities',
                error: createErrorObj(error),
            });
        }
    }

    public async getEntitiesAtTrackerLevel(
        requestParams: EntitiesRequestParams,
        pageQueryParams: EntitiesQueryParams,
    ): Promise<EntitiesResponse> {
        const { tenantId, coaId, periodId, phaseId, entityType } = requestParams;
        try {
            const token = this.req.headers.authorization.split(' ')[1];
            this.req.getLogger(__filename).info('Fetching entities at tracker level', requestParams);
            const response = await this.axios.get(
                `v1/tenants/${tenantId}/coas/${coaId}/periods/${periodId}/phases/${phaseId}/entityTypes/${entityType}/entities`,
                {
                    headers: { Authorization: `Bearer ${token}` },
                    ...(pageQueryParams && { params: pageQueryParams }),
                },
            );
            return response.data as EntitiesResponse;
        } catch (error) {
            this.req.getLogger(__filename).error(error);
            throw new InternalAPIException({
                api: 'getEntitiesAtTrackerLevel',
                error: createErrorObj(error),
            });
        }
    }

    public async getCoaWorkersMasterList(
        requestParams: WorkerMasterListRequestParams,
        pageQueryParams: WorkerMasterListQueryParams,
    ): Promise<WorkerMasterListResponse> {
        const { tenantId, coaId } = requestParams;
        try {
            const token = this.req.headers.authorization.split(' ')[1];
            this.req.getLogger(__filename).info('Fetching workers master list', requestParams);
            const response = await this.axios.get(`v1/tenants/${tenantId}/coas/${coaId}/workers`, {
                headers: { Authorization: `Bearer ${token}` },
                ...(pageQueryParams && { params: pageQueryParams }),
            });
            return response.data as WorkerMasterListResponse;
        } catch (error) {
            this.req.getLogger(__filename).error(error);
            throw new InternalAPIException({
                api: 'getWorkerMasterList',
                error: createErrorObj(error),
            });
        }
    }
}
