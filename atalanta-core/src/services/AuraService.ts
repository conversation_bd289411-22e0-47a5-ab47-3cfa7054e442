import JSONBigIntMod from 'json-bigint';
import { Request } from 'express';
import { BaseService } from './BaseService';
import { InternalAPIException } from '../exceptions/InternalAPIException';
import {
    CoaBalanceSummary,
    CoaPostingRecordTypeSummary,
    CoaSummaryRequestParams,
    CoaVoucherCodeSummary,
    GetPhaseTriggerStatusResponse,
    PolciesRequestParams,
    FeeProgramsRequestParams,
} from './Interfaces/Aura';
import { TriggerStatusRequestObj } from './Interfaces/Aura';
import { createErrorObj, renderTemplate } from '../utils';
import TokenService from '../services/Token.service';
import { TemplateData } from '../types';
const JSONBigInt = JSONBigIntMod({ storeAsString: true });

export class AuraService extends BaseService {
    constructor(req: Request) {
        super(req, {
            baseURL: process.env.AURA_URL,
            headers: {
                'Content-Type': 'application/json',
            },
            transformRequest: (data: any) => (data ? JSONBigInt.parse(data) : data),
            transformResponse: (data: any) => (data ? JSONBigInt.parse(data) : data),
        });
    }

    public async getPhaseTriggerStatus(requestParams: TriggerStatusRequestObj): Promise<GetPhaseTriggerStatusResponse> {
        const { tenantId, coaId, periodId, triggerValue, token } = requestParams;
        try {
            this.req.getLogger(__filename).info('Fetching trigger status', requestParams);
            const response = await this.axios.get(
                `bookkeeper/tenants/${tenantId}/coas/${coaId}/periods/${periodId}/${triggerValue}/status`,
                {
                    headers: { ['authorization']: token },
                },
            );
            return response.data as GetPhaseTriggerStatusResponse;
        } catch (error) {
            this.req.getLogger(__filename).error(error);
            throw new InternalAPIException({
                api: 'getPhaseTriggerStatus',
                error: createErrorObj(error)
            });
        }
    }
    public async getCoaBalanceSummary(requestParams: CoaSummaryRequestParams) {
        try {
            const { tenantId = '', coaCode = '' } = requestParams || {};
            const auraUrl = this.getAuraUrl({ tenantId, coaCode });
            const authToken = await TokenService.getToken(requestParams.tenantId);
            const response = await this.axios.get(`${auraUrl}/coa_balance_derived_summary`, {
                params: {
                    coa_id: `eq.${requestParams.coaId}`,
                    start_date: `eq.${requestParams.startDate}`,
                    cycle_id: `eq.${requestParams.cycleId}`,
                    type: 'eq.BALANCE_CODE',
                    code: 'eq.BALANCE',
                    periodicity: 'eq.DAILY',
                },
                headers: { ['Gan-AuthTOken']: authToken },
            });
            return response.data as CoaBalanceSummary[];
        } catch (error) {
            this.req.getLogger(__filename).error('Coa Balance Summary - ', error);
            throw new InternalAPIException({
                api: 'getCoaBalanceSummary',
                error: createErrorObj(error),
            });
        }
    }

    public async getCoaPostingRecordTypeSummary(requestParams: CoaSummaryRequestParams) {
        try {
            const { tenantId = '', coaCode = '' } = requestParams || {};
            const auraUrl = this.getAuraUrl({ tenantId, coaCode });
            const authToken = await TokenService.getToken(requestParams.tenantId);
            const response = await this.axios.get(`${auraUrl}/coa_posting_record_type_summary`, {
                params: {
                    coa_id: `eq.${requestParams.coaId}`,
                    start_date: `eq.${requestParams.startDate}`,
                    periodicity: 'eq.DAILY',
                    cycle_id: `eq.${requestParams.cycleId}`,
                },
                headers: { ['Gan-AuthTOken']: authToken },
            });
            return response.data as CoaPostingRecordTypeSummary[];
        } catch (error) {
            this.req.getLogger(__filename).error('Coa Posting Record Summary - ', error);
            throw new InternalAPIException({
                api: 'getCoaPostingRecordTypeSummary',
                error: createErrorObj(error),
            });
        }
    }

    public async getPolicies(requestParams: PolciesRequestParams) {
        try {
            const { tenantId = '' , token, type, tagUri, pageNo, pageSize} = requestParams;
            this.req.getLogger(__filename).info('Fetching get policies, params :', requestParams);
            const auraPolicyUrl = process.env.AURA_POLICY_URL;
            const response = await this.axios.get(`${auraPolicyUrl}/tenants/${tenantId}/policies`, {
                params: {
                    type,
                    tagUri,
                    pageNo,
                    pageSize
                },
                headers: { ['Authorization']: token },
            });
            return response.data;
        } catch(error) {
            this.req.getLogger(__filename).error('Error fetching policies - ', error);
            throw new InternalAPIException({
                api: 'getPolicies',
                error: createErrorObj(error),
            });
        }
    }

    public async getFeePromoPrograms(requestParams: FeeProgramsRequestParams) {
        try {
            const token = this.req.headers.authorization;
            const { tenantId = '', programCode } = requestParams;
            this.req.getLogger(__filename).info('Fetching get fee promo programs, params :', requestParams);
            const auraChargeUrl = process.env.AURA_CHARGE_URL;
            const response = await this.axios.get(`${auraChargeUrl}/tenants/${tenantId}/fee/promoPrograms/${programCode}`, {
                headers: { Authorization: token },
            });
            return response.data;
        } catch (error) {
            this.req.getLogger(__filename).error('Error fetching fee promo programs - ', error);
            throw new InternalAPIException({
                api: 'getFeePromoPrograms',
                error: createErrorObj(error),
            });
        }
    }

    public async getCoaVoucherCodeSummary(requestParams: CoaSummaryRequestParams) {
        try {
            const { tenantId = '', coaCode = '' } = requestParams || {};
            const auraUrl = this.getAuraUrl({ tenantId, coaCode });
            const authToken = await TokenService.getToken(requestParams.tenantId);
            const response = await this.axios.get(`${auraUrl}/coa_voucher_code_derived_summary`, {
                params: {
                    coa_id: `eq.${requestParams.coaId}`,
                    start_date: `eq.${requestParams.startDate}`,
                    periodicity: 'eq.DAILY',
                    cycle_id: `eq.${requestParams.cycleId}`,
                },
                headers: { ['Gan-AuthTOken']: authToken },
            });
            return response.data as CoaVoucherCodeSummary[];
        } catch (error) {
            this.req.getLogger(__filename).error('Coa Voucher Code Summary - ', error);
            throw new InternalAPIException({
                api: 'getCoaVoucherCodeSummary',
                error: createErrorObj(error),
            });
        }
    }

    /**
     *  To handle changes in url for different zones Sample URLs -
     *  Preprod - https://<%= tenantId %>-aura.internal.mum1-pp.zetaapps.in/ganymede/aads
     *  UAT - https://aura.zone.olympus.infra/ganymede/aads-<%= tenantId %>/coa_balance_summary
     */
    private getAuraUrl = (requestParams: TemplateData) => {
        return renderTemplate(process.env.AURA_GANYMEDE_URL, requestParams);
    };
}
