import axios, { AxiosPromise } from 'axios';
import {
    SandboxAddRoleToSubjectRequest,
    SandboxGetRolesOfSubjectRequest,
    SandboxGetRolesOfSubjectResponse,
    RoleEntryRes,
    ActionGroupRes,
    ActionRes,
    Action,
    RoleEntry,
    ActionGroup,
} from './Interfaces/Sandbox';
import { Request } from 'express';
import dayjs from 'dayjs';
import { LRUCache } from '../LRUCache';
import {
    CACHE_ACTIONS_SIZE,
    CACHE_ACTIONS_TTL,
    CACHE_LAST_STORED_TTL,
    CACHE_LAST_STORED_SIZE,
} from '../Constants';
import { InternalAPIException } from '../exceptions/InternalAPIException';
import { createErrorObj } from './../utils';

const axiosInstance = (authToken?: string) => {
    const instance = axios.create({
        baseURL: `${process.env.SANDBOX_SERVICE_BASE_URL}/sandbox`,
        headers: {
            Authorization: authToken ? `Bearer ${authToken}` : `Bearer ${process.env.LTT}`,
            'Content-Type': 'application/json',
        },
    });

    instance.interceptors.response.use((res) => {
        return res;
    });
    return instance;
};

// function to get role entries for each page where each page has 100 items of role entries
export const getRoleEntriesPage = async (
    tenantId: string,
    sandboxId: string,
    role: string,
    authToken: string,
    offset: number = 0,
): Promise<RoleEntryRes> => {
    const limit = 100;
    return await axiosInstance(authToken).get(
        `/tenants/${tenantId}/sandboxes/${sandboxId}/roles/${role}/role_entries?offset=${offset}&limit=${limit}`,
    );
};

// function to get all the role entries for a particular role assigned to a user
export const getRoleEntries = async (
    tenantId: string,
    sandboxId: string,
    role: string,
    authToken: string,
    offset: number = 0,
): Promise<RoleEntry[]> => {
    const limit = 100;
    let roleEntries: RoleEntry[] = [];
    for (let lastResEntriesLength = limit; lastResEntriesLength == limit; ) {
        const roleEntriesRes: RoleEntryRes = await getRoleEntriesPage(tenantId, sandboxId, role, authToken, offset);
        if (roleEntriesRes?.data?.roleEntries.length === limit) {
            offset = offset + 100;
        }
        roleEntries.push(...roleEntriesRes?.data?.roleEntries);
        lastResEntriesLength = roleEntriesRes?.data?.roleEntries.length;
    }
    return roleEntries;
};

export const getAllActionGroups = async (
    tenantId: string,
    sandboxId: string,
    objectType: string,
    roleEntry: string,
    authToken: string,
): Promise<ActionGroupRes> => {
    let response = await axiosInstance(authToken).get(
        `/tenants/${tenantId}/sandboxes/${sandboxId}/object_types/${objectType}/role_entries/${roleEntry}/action_groups`,
    );

    return { objectType, data: response?.data };
};

export const getAllActions = async (
    tenantId: string,
    sandboxId: string,
    objectType: string,
    actionGroup: string,
    authToken: string,
): Promise<ActionRes> => {
    let response = await axiosInstance(authToken).get(
        `/tenants/${tenantId}/sandboxes/${sandboxId}/object_types/${objectType}/action_groups/${actionGroup}/actions`,
    );

    return { objectType, actionGroup, data: response?.data };
};

// function to return all the actions within the actions grps
export const getAllObjectAndActionGroups = async (request: Request, authToken: string) => {
    const cache = LRUCache.getCache();
    const sandboxId = request.params.sandboxId;
    const tenantId = request.params.tenantId;
    const roles: string[] = (request.query.roles as string).split(',') || [];
    let roleNames: string = '';
    let roleEntries: RoleEntry[] = [];
    for (let roleEntryIndex = 0; roleEntryIndex < roles.length; roleEntryIndex++) {
        const roleName: string = (roles[roleEntryIndex] as string)?.replace(/"/g, '').trim();
        roleNames = roleNames + roleName;
        try {
            roleEntries.push(...(await getRoleEntries(tenantId, sandboxId, roleName, authToken)));
        } catch (error) {
            request.getLogger(__filename).error('Failed to get the role entries', { error });
            throw new InternalAPIException({
                api: 'getAllObjectAndActionGroups',
                error: createErrorObj(error)
            });
        }
    }
    /**
     * For fetching all actions associated with a role in Support Center,
     * we had to first fetch all the role entries to get the object types and
     * then all the action groups from all the object types and then for each action group,
     * we had to fetch all the actions. This lead to around ~50 API calls being made and
     * led to a waiting time of around 1 min - 2 mins to get the list of all actions.
     * Since this is very non performant, we have used memory cache to store the actions once fetched and
     * would refetch the actions only if actions were modified after the last response was cached.
     * FYI we have also asked Cipher team to work on a new API that returns the list of all actions
     * associated with a role in a single call when given list of all objectTypes and action groups.
     * This would lead to reduction in response time by reducing the number of http calls made.
     * Refer: DQ-326.
     * We would remove cache implementation once this is done.
     */

    // by default get data from cache if date && objectActionsArray is stored
    let getActionsFromCache =
        !!cache.get(`sandbox_objectActionsArray:${tenantId}:${sandboxId}:${roleNames}`) &&
        !!cache.get(`sandbox_objectActionsArraylastStored:${tenantId}:${sandboxId}:${roleNames}`);
    let objectRoleEntryMap = [];
    for (let roleEntryIndex = 0; roleEntryIndex < roleEntries.length; roleEntryIndex++) {
        // get data from API if any action grp is modified after last stored date
        if (
            getActionsFromCache &&
            dayjs(roleEntries[roleEntryIndex].modifiedAt).isAfter(
                dayjs(cache.get(`sandbox_objectActionsArraylastStored:${tenantId}:${sandboxId}:${roleNames}`)),
            )
        ) {
            getActionsFromCache = false;
        }
        objectRoleEntryMap.push({
            objectType: roleEntries[roleEntryIndex].objectType,
            roleEntry: roleEntries[roleEntryIndex].roleEntryID,
        });
    }

    if (getActionsFromCache) {
        return cache.get(`sandbox_objectActionsArray:${tenantId}:${sandboxId}:${roleNames}`);
    }

    return Promise.all(
        objectRoleEntryMap.map((objectRoleEntry) =>
            getAllActionGroups(tenantId, sandboxId, objectRoleEntry.objectType, objectRoleEntry.roleEntry, authToken),
        ),
    )
        .then((actionGroups: ActionGroupRes[]) => {
            let currentActionGrp: ActionGroup[];
            let currentObjectType = '';
            const actionPromises = [];
            for (const actionGroup of actionGroups) {
                currentActionGrp = actionGroup?.data?.actionGroups || [];
                currentObjectType = actionGroup?.objectType;
                const getActionPromises = currentActionGrp.map((actionGrp) =>
                    getAllActions(tenantId, sandboxId, currentObjectType, actionGrp.actionGroup, authToken),
                );
                actionPromises.push(...getActionPromises);
            }
            return Promise.all(actionPromises);
        })
        .then((actionResponses: ActionRes[]) => {
            let objectActionsArray: object[] = [];
            let currentObjectType = '';
            for (const actionRes of actionResponses) {
                const actionsName = actionRes?.data?.actions?.map((item: Action) => item.action);
                currentObjectType = actionRes?.objectType;
                objectActionsArray.push({
                    objectType: currentObjectType,
                    actionGroup: actionRes?.actionGroup,
                    actions: actionsName,
                });
            }
            cache.set(`sandbox_objectActionsArray:${tenantId}:${sandboxId}:${roleNames}`, objectActionsArray, {
                size: CACHE_ACTIONS_SIZE,
                ttl: CACHE_ACTIONS_TTL,
            });
            cache.set(
                `sandbox_objectActionsArraylastStored:${tenantId}:${sandboxId}:${roleNames}`,
                new Date().toISOString(),
                {
                    size: CACHE_LAST_STORED_SIZE,
                    ttl: CACHE_LAST_STORED_TTL,
                },
            );
            return objectActionsArray;
        })
        .catch((error) => {
            request.getLogger(__filename).error('Failed to get all the actions and action groups', { error });
            throw new InternalAPIException({
                api: 'getAllObjectAndActionGroups',
                error: createErrorObj(error)
            });
        });
};

export const addRoleToSubject = async (request: SandboxAddRoleToSubjectRequest) => {
    const { realmID, roleName, sandboxID, subjectID, tenantID } = request;
    return await axiosInstance().post(
        `/tenants/${tenantID}/sandboxes/${sandboxID}/realms/${realmID}/subjects/${subjectID}/roles/${roleName}`,
    );
};
export const getRolesToSubject = async (
    request: SandboxGetRolesOfSubjectRequest,
): Promise<AxiosPromise<SandboxGetRolesOfSubjectResponse>> => {
    const { realmID, sandboxID, subjectID, tenantID } = request;
    return await axiosInstance().get(
        `/tenants/${tenantID}/sandboxes/${sandboxID}/realms/${realmID}/subjects/${subjectID}/roles`,
    );
};
