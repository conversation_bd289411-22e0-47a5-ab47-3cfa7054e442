import axios, { AxiosPromise } from 'axios';
import {
    CardDetailsResponse,
    GetAccountHolderRequest,
    GetAccountHolderResponse,
    GetAllPPAccountsRequest,
    GetCardDetailsByFormFactorIDRequest,
    GetResourceByVectorRequest,
    GetResourceByVectorResponse,
} from './Interfaces/Fusion';
import { Request } from 'express';
import { BaseService } from './BaseService';
import { InternalAPIException } from '../exceptions/InternalAPIException';
import { createErrorObj } from './../utils';

const servicePathV1 = (): string => {
    return `/api/v1`;
};

const servicePathV2 = (): string => {
    return `/api/v2`;
};

export class FusionService extends BaseService {
    constructor(req: Request) {
        super(req, {
            baseURL: process.env.FUSION_BASE_URL,
            headers: {
                'Content-Type': 'application/json',
            },
        })
    };

    public async getAccountHolder(params: GetAccountHolderRequest): Promise<GetAccountHolderResponse> {
        const { tenantId, individualID } = params;
        try {
            const response = await this.axios.get(`${servicePathV1()}/ifi/${tenantId}/individuals/${individualID}`, {
                headers: { ['x-zeta-authtoken']: params.token },
            });
            return response.data as GetAccountHolderResponse;
        } catch (error) {
            this.req.getLogger(__filename).error('API getAccountHolder Error:', error);
            throw new InternalAPIException({
                api: 'getAccountHolder',
                error: createErrorObj(error)
            });
        }
    };

    public async getResourceByVector(params: GetResourceByVectorRequest): Promise<GetResourceByVectorResponse[]> {
        try {
            const requestParams = {...params};
            delete requestParams.token;
            const response = await this.axios.get(`${servicePathV2()}/ifi/${params.ifiID}/resourceByVector`, {
                headers: { ['x-zeta-authtoken']: params.token },
                params: requestParams
            });
            return response.data as GetResourceByVectorResponse[];
        } catch (error) {
            this.req.getLogger(__filename).error('API getResourceByVector Error:', error);
            throw new InternalAPIException({
                api: 'getResourceByVector',
                error: createErrorObj(error)
            });
        }
    };

    public async getCardDetailsByFormFactorID(params: GetCardDetailsByFormFactorIDRequest): Promise<CardDetailsResponse> {
        try {
            const response = await this.axios.get(`${servicePathV1()}/ifi/${params.ifiID}/cards/${params.formFactorID}`, {
                headers: { ['x-zeta-authtoken']: params.token },
            });
            return response.data as CardDetailsResponse;
        } catch (error) {
            this.req.getLogger(__filename).error('API getCardDetailsByFormFactorID Error:', error);
            throw new InternalAPIException({
                api: 'getCardDetailsByFormFactorID',
                error: createErrorObj(error)
            });
        }
    };

    public async getAllPPAccounts(params: GetAllPPAccountsRequest) {
        try {
            const requestParams = {...params};
            delete requestParams.token;
            const response = await this.axios.get(`${servicePathV1()}/ifi/${params.ifiID}/individuals/${params.individualID}/accounts`, {
                headers: { ['x-zeta-authtoken']: params.token },
                params: requestParams
            });
            return response.data;
        } catch (error) {
            this.req.getLogger(__filename).error('API getAllPPAccounts Error:', error);
            throw new InternalAPIException({
                api: 'getAllPPAccounts',
                error: createErrorObj(error)
            });
        }
    };
}