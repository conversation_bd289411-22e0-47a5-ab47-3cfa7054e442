import { Request } from 'express';
import { BaseService } from './BaseService';
import { InternalAPIException } from '../exceptions/InternalAPIException';
import { DeltaRequest, GetDeltaResponse } from './Interfaces/Delta';
import { createErrorObj } from '../utils';
export class DeltaService extends BaseService {
    constructor(req: Request) {
        super(req, {
            baseURL: process.env.DELTA_SERVICE_URL,
            headers: {
            },
        })
    };

    public async getTransactionErrorCodes(requestParams: DeltaRequest): Promise<GetDeltaResponse> {
        const { tenantID ,authorization} = requestParams;
        try {
            if (tenantID) {
                this.req.getLogger(__filename).info('Getting Delta', requestParams)
                const apiVersion = 'v2'
                const response = await this.axios.get(`/api/${apiVersion}/tenants/${tenantID}/resourceTypes/${process.env.RESOURCE_TYPE}/resources/${process.env.RESOURCES}`, {
                    headers: {
                        'Authorization': authorization
                    }
                });
                return response.data as GetDeltaResponse
            }
        } catch (error) {
            this.req.getLogger(__filename).error('Error', { error });
            throw new InternalAPIException({
                api: 'getDelta',
                error: createErrorObj(error)
            });
        }
    }
}