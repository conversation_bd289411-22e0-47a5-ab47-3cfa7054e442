import JSONBigIntMod from 'json-bigint';
import { Request } from 'express';
import { BaseService } from './BaseService';
import { InternalAPIException } from '../exceptions/InternalAPIException';
import { OperationsTypes } from './Interfaces/Operations';
import axios from 'axios';
import { createErrorObj } from './../utils';

const JSONBigInt = JSONBigIntMod({ storeAsString: true });

export class OperationsService extends BaseService {
    constructor(req: Request) {
        super(req, {
            baseURL: process.env.OPS_BASE_URL,
            headers: {
                'Content-Type': 'application/json',
            },
            transformRequest: (data: any) => (data ? JSONBigInt.parse(data) : data),
            transformResponse: (data: any) => (data ? JSONBigInt.parse(data) : data),
        });
    }

    /**
     * Function to fetch all the tasks for a given workbench based on different
     * filter parameters
     * @param requestParams request parameters
     * @returns WorkbenchTaskResponse
     */
    public async getUserTaskInformationList(
        requestParams: OperationsTypes.UserTaskRequest,
    ): Promise<OperationsTypes.UserTaskInformationResponse> {
        const { tenantId, token, ...params } = requestParams;
        try {
            this.req.getLogger(__filename).info('Fetching user task information list from rhea', requestParams);
            // TODO: FIXME: There is a bug on axiosconfig due to node-logger that does not work for axios.put or axios.post
            const response = await this.axios.request({
                url: `/tenants/${tenantId}/getUserTaskInfoList`,
                method: 'POST',
                headers: { ['authorization']: `${token}`, 'Content-Type': 'application/json' },
                data: JSON.stringify(params),
                transformRequest: axios.defaults.transformRequest,
                transformResponse: axios.defaults.transformResponse,
            });
            return response.data;
        } catch (error) {
            this.req.getLogger(__filename).error('Error', { error });
            throw new InternalAPIException({
                api: 'getUserTaskInformationList',
                error: createErrorObj(error)
            });
        }
    }

    /**
     * Function to get the form variables for a given task
     * @param requestParams request parameters
     * @returns variables
     */
    public async getFormVariables(
        requestParams: OperationsTypes.GetFormVariablesRequest,
    ): Promise<OperationsTypes.GetFormVariablesResponse> {
        const { tenantId, catalogId, workbenchId, taskId, token, getCompletedTasks, ...params } = requestParams;
        try {
            this.req.getLogger(__filename).info('Fetching form variables for task', requestParams);
            const response = await this.axios.get(
                `/tenants/${tenantId}/catalogs/${catalogId}/workbenches/${workbenchId}/${
                    getCompletedTasks ? 'history' : 'rhea'
                }/task/${taskId}/form-variables`,
                {
                    headers: { ['authorization']: `${token}`, 'Content-Type': 'application/json' },
                    params,
                },
            );
            return response.data;
        } catch (error) {
            this.req.getLogger(__filename).error('Error', { error });
            throw new InternalAPIException({
                api: 'getFormVariables',
                error: createErrorObj(error)
            });
        }
    }
}
