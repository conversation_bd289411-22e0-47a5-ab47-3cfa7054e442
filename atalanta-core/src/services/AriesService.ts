import { Request } from 'express';
import { BaseService } from './BaseService';
import { GetAccountHolderRequest, GetAccountHolderResponse, GetAccountHolderResponseV4 } from './Interfaces/Aries';
import { InternalAPIException } from '../exceptions/InternalAPIException';
import { createErrorObj } from './../utils';
export class AriesService extends BaseService {

    constructor(req: Request) {
        super(req, {
            baseURL: process.env.ARIES_BASE_URL,
            headers: {
                'Content-Type': 'application/json',
            },
        })
    };

    private get servicePathV1(): string {
        return  '/api/v1';
    }

    private get servicePathV4(): string {
        return '/tachyon/v4';
    }

    public async getAccountHolderV1(params: GetAccountHolderRequest): Promise<GetAccountHolderResponse> {
        const { tenantId, individualId } = params;
        try {
            const response = await this.axios.get(`${this.servicePathV1}/ifi/${tenantId}/individuals/${individualId}`, {
                headers: { ['x-zeta-authtoken']: params.token },
            });
            return response.data as GetAccountHolderResponse;
        } catch (error) {
            this.req.getLogger(__filename).error('API getAccountHolder Error:', error);
            throw new InternalAPIException({
                api: 'getAccountHolder',
                error: createErrorObj(error)
            });
        }
    };

    public async getAccountHolderV4(params: GetAccountHolderRequest): Promise<GetAccountHolderResponseV4> {
        const { tenantId, individualId, token } = params;
        try {
            const response = await this.axios.get(`${this.servicePathV4}/ifi/${tenantId}/accountholders/${individualId}`, {
                headers: { ['x-zeta-authtoken']: token },
            });
            return response.data as GetAccountHolderResponseV4;
        } catch (error) {
            this.req.getLogger(__filename).error('API getAccountHolderV4 Error:', error);
            throw new InternalAPIException({
                api: 'getAccountHolderV4',
                error: createErrorObj(error)
            });
        }
    };


}