import { CruxTokenException } from '../exceptions/CruxTokenException';
import axios, { AxiosInstance } from 'axios';

class TokenService {
    private httpClient: AxiosInstance;
    constructor() {
        this.httpClient = axios.create({
            baseURL: process.env.CRUX_SIDECAR_BASE_URL,
        });
    }

    async getToken(tenantId: string): Promise<string> {
        try {
            const { data } = await this.httpClient.get(`/api/v1/crux/tenants/${tenantId}/token`);
            return data.token;
        } catch (error) {
            throw new CruxTokenException({message:`Error while getting token from crux for tenant: ${tenantId}`, error:error});
        }
    }
}

export default new TokenService();
