import JSONBigIntMod from 'json-bigint';
import { Request } from 'express';
import { BaseService } from './BaseService';
import { InternalAPIException } from '../exceptions/InternalAPIException';
import axios from 'axios';
import { TemplateData } from '../types';
import {
    CoaConnection,
    CoaConnectionDetails,
    ParentCoa,
    ParentCoaLedger,
    CreateCoaConntectionRequest,
    UpdateCoaConntectionMapRequest,
    CoaSummaryGoldenSchemaPayload,
    CoaSummaryGoldenSchemaResponse,
    UpdateConnectorStatus,
    ConnectionDetails,
    GlHandoffRunsResponse,
    ChildCoaListResponse,
    ChildCoaListRequest,
} from './Interfaces/Argus';
import TokenService from '../services/Token.service';
import { createErrorObj, renderTemplate } from './../utils';
import { CONNECTION_REQUEST_TYPE } from './../Constants';
import { transformRequest, transformResponse } from '../utils';
export class ArgusService extends BaseService {
    constructor(req: Request) {
        super(req, {
            baseURL: process.env.ARGUS_URL,
            headers: {
                'Content-Type': 'application/json',
            },
            transformRequest,
            transformResponse,
        });
    }

    public async getParentCoaList(): Promise<ParentCoa[]> {
        const token = this.req.headers.authorization;
        const { tenantId } = this.req.params as { [key: string]: string };
        const pageQueryParams = this.req.query;
        try {
            this.req.getLogger(__filename).info(`Fetching Parent Coa List for ${tenantId}`);
            const response = await this.axios.get(
                `${process.env.PARENT_COA_URL}/api/v1/tenants/${tenantId}/parent-coas`,
                {
                    headers: { ['authorization']: token },
                    ...(pageQueryParams && { params: pageQueryParams }),
                },
            );
            return response.data as ParentCoa[];
        } catch (error) {
            this.req.getLogger(__filename).error(`Error while fetching Parent Coa List for ${tenantId}`, { error });
            throw new InternalAPIException({
                api: 'getParentCoaList',
                error: createErrorObj(error),
            });
        }
    }

    public async getChildCoaList(): Promise<ChildCoaListResponse[]> {
        const token = this.req.headers.authorization;
        const { tenantId, parentCoaCode } = this.req.params as unknown as ChildCoaListRequest;
        const pageQueryParams = this.req.query;
        try {
            this.req.getLogger(__filename).info(`Fetching ChildCoa List for ${parentCoaCode} of the tenant ${tenantId}`);
            const response = await this.axios.get(
                `${process.env.PARENT_COA_URL}/api/v1/tenants/${tenantId}/parent-coas/${parentCoaCode}/child-coas`,
                {
                    headers: { ['authorization']: token },
                    ...(pageQueryParams && { params: pageQueryParams }),
                },
            );
            return response.data as ChildCoaListResponse[];
        } catch (error) {
            this.req.getLogger(__filename).error(`Error while fetching ChildCoa List for ${parentCoaCode} of the tenant ${tenantId}`, error);
            throw new InternalAPIException({
                api: 'getChildCoaList',
                error: createErrorObj(error),
            });
        }
    }

    public async getParentCoaLedgerList(): Promise<ParentCoaLedger[]> {
        const token = this.req.headers.authorization;
        const { tenantId, parentCoaCode } = this.req.params as unknown as ChildCoaListRequest;
        const pageQueryParams = this.req.query;
        try {
            this.req
                .getLogger(__filename)
                .info(`Fetching Parent Coa Ledger List for ${parentCoaCode} of the tenant ${tenantId}`);
            const response = await this.axios.get(
                `${process.env.PARENT_COA_URL}/api/v1/tenants/${tenantId}/parent-coas/${parentCoaCode}/ledgers`,
                {
                    headers: { ['authorization']: token },
                    ...(pageQueryParams && { params: pageQueryParams }),
                },
            );
            return response.data as ParentCoaLedger[];
        } catch (error) {
            this.req
                .getLogger(__filename)
                .error(
                    `Error while fetching Parent Coa Ledger List for ${parentCoaCode} of the tenant ${tenantId}`,
                    error,
                );
            throw new InternalAPIException({
                api: 'getParentCoaLedgerList',
                error: createErrorObj(error),
            });
        }
    }

    public async getParentCoaDetails(): Promise<ParentCoa> {
        const token = this.req.headers.authorization;
        const { tenantId, parentCoaCode } = this.req.params as { [key: string]: string };
        const pageQueryParams = this.req.query;
        try {
            this.req
                .getLogger(__filename)
                .info(`Fetching Parent Coa Details for ${parentCoaCode} of the tenant ${tenantId}`);
            const response = await this.axios.get(
                `${process.env.PARENT_COA_URL}/api/v1/tenants/${tenantId}/parent-coas/${parentCoaCode}`,
                {
                    headers: { ['authorization']: token },
                    ...(pageQueryParams && { params: pageQueryParams }),
                },
            );
            return response.data as ParentCoa;
        } catch (error) {
            this.req.getLogger(__filename).error(`Error while fetching Parent Coa Details for ${tenantId}`, error);
            throw new InternalAPIException({
                api: 'getParentCoaDetails',
                error: createErrorObj(error),
            });
        }
    }

    public async getParentCoaLedgerDetails(): Promise<ParentCoaLedger> {
        const token = this.req.headers.authorization;
        const { tenantId, parentCoaCode, accountNo } = this.req.params as { [key: string]: string };
        const pageQueryParams = this.req.query;
        try {
            this.req
                .getLogger(__filename)
                .info(`Fetching Parent Coa Ledger Details for ${parentCoaCode} of the tenant ${tenantId}`);
            const response = await this.axios.get(
                `${process.env.PARENT_COA_URL}/api/v1/tenants/${tenantId}/parent-coas/${parentCoaCode}/ledgers/${accountNo}`,
                {
                    headers: { ['authorization']: token },
                    ...(pageQueryParams && { params: pageQueryParams }),
                },
            );
            return response.data as ParentCoaLedger;
        } catch (error) {
            this.req
                .getLogger(__filename)
                .error(
                    `Error while fetching Parent Coa Ledger Details for ${parentCoaCode} of the tenant ${tenantId}`,
                    error,
                );
            throw new InternalAPIException({
                api: 'getParentCoaLedgerDetails',
                error: createErrorObj(error),
            });
        }
    }

    public async postParentCoaDetails(payload: ParentCoa): Promise<ParentCoa> {
        const token = this.req.headers.authorization;
        const { tenantId } = this.req.params as { [key: string]: string };
        try {
            this.req.getLogger(__filename).info(`Creating a Parent Coa Details for ${tenantId}`);
            const response = await axios.post(
                `${process.env.PARENT_COA_URL}/api/v1/tenants/${tenantId}/parent-coas`,
                payload,
                {
                    headers: { ['authorization']: token },
                },
            );
            return response.data as ParentCoa;
        } catch (error) {
            this.req.getLogger(__filename).error(`Error while creating Parent Coa for ${tenantId}`, error);
            throw new InternalAPIException({
                api: 'postParentCoaDetails',
                error: createErrorObj(error),
            });
        }
    }

    public async getParentCoaConnectionList(): Promise<CoaConnection[]> {
        const token = this.req.headers.authorization;
        const { tenantId, parentCoaCode, childCoaCode } = this.req.params as { [key: string]: string };
        try {
            this.req
                .getLogger(__filename)
                .info(
                    `Fetching connections between Parent coa (${parentCoaCode}) and child coa (${childCoaCode}) for  of the tenant ${tenantId}`,
                );
            const response = await this.axios.get(
                `${process.env.PARENT_COA_URL}/api/v1/tenants/${tenantId}/child-coas/${childCoaCode}/parent-coas/${parentCoaCode}/connectors`,
                {
                    headers: { ['authorization']: token },
                },
            );
            return response.data as CoaConnection[];
        } catch (error) {
            this.req
                .getLogger(__filename)
                .error(
                    `Error while fetching connections between Parent coa (${parentCoaCode}) and child coa (${childCoaCode}) for ${tenantId}`,
                    error,
                );
            throw new InternalAPIException({
                api: 'getCoaConnectionList',
                error: createErrorObj(error),
            });
        }
    }

    public async getChildCoaPresentConnection(): Promise<CoaConnection[]> {
        const token = this.req.headers.authorization;
        const pageQueryParams = this.req.query;
        const { tenantId, childCoaId } = this.req.params as { [key: string]: string };
        try {
            this.req
                .getLogger(__filename)
                .info(`Fetching active connections for Child coa (${childCoaId}) for  of the tenant ${tenantId}`);
            const response = await this.axios.get(
                `${process.env.PARENT_COA_URL}/api/v1/tenants/${tenantId}/child-coas/${childCoaId}/parent-coa-connectors`,
                {
                    headers: {
                        ['authorization']: token,
                        ['Content-Type']: 'application/json',
                    },
                    ...(pageQueryParams && { params: pageQueryParams }),
                },
            );
            return response.data as CoaConnection[];
        } catch (error) {
            this.req
                .getLogger(__filename)
                .error(`Error while fetching active connections for  child coa (${childCoaId}) for ${tenantId}`, error);
            throw new InternalAPIException({
                api: 'getChildCoaActiveConnection',
                error: createErrorObj(error),
            });
        }
    }

    public async getIsOutOfSync(): Promise<boolean> {
        const token = this.req.headers.authorization;
        const { tenantId, childCoaCode, parentCoaCode, connectorId } = this.req.params as { [key: string]: string };
        try {
            this.req
                .getLogger(__filename)
                .info(
                    `Checking if connector ${connectorId} is out of sync for child coa ${childCoaCode} and parent coa ${parentCoaCode} of tenant ${tenantId}`,
                );
            const response = await this.axios.get(
                `${process.env.PARENT_COA_URL}/api/v1/tenants/${tenantId}/child-coas/${childCoaCode}/parent-coas/${parentCoaCode}/connectors/${connectorId}/outOfSync`,
                {
                    headers: { ['authorization']: token },
                },
            );
            return response.data.outOfSync as boolean;
        } catch (error) {
            this.req
                .getLogger(__filename)
                .error(
                    `Error while checking if connector ${connectorId} is out of sync for child coa ${childCoaCode} and parent coa ${parentCoaCode} of tenant ${tenantId}`,
                    error,
                );
            throw new InternalAPIException({
                api: 'isOutOfSync',
                error: createErrorObj(error),
            });
        }
    }

    public async createGlHandOffRequest(payload: { connectorId: string, periodId: string }): Promise<string> {
        const { connectorId, periodId } = payload;
        const token = this.req.headers.authorization;
        const { tenantId, childCoaId } = this.req.params as { [key: string]: string };
        try {
            this.req
                .getLogger(__filename)
                .info(
                    `Initiating GL Handoff for connector ${connectorId} and period ${periodId} of child coa ${childCoaId} and tenant ${tenantId}`,
                );
                const response = await this.axios.request({
                    url:`${process.env.PARENT_COA_URL}/api/v1/tenants/${tenantId}/child-coas/${childCoaId}/gl-handoff`,
                    method: 'POST',
                    headers: { ['authorization']: token, 'Content-Type': 'application/json' },
                    data: JSON.stringify(payload),
                });
            return response.data.runId as string;
        } catch (error) {
            this.req
                .getLogger(__filename)
                .error(
                    `Error while initiating GL Handoff for connector ${connectorId} and period ${periodId} of child coa ${childCoaId} and tenant ${tenantId}`,
                    error,
                );
            throw new InternalAPIException({
                api: 'createGlHandOffRequest',
                error: createErrorObj(error),
            });
        }
    }

    public async getGlHandoffRuns(): Promise<GlHandoffRunsResponse> {
        const token = this.req.headers.authorization;
        const { tenantId, childCoaId } = this.req.params as { [key: string]: string };
        const pageQueryParams = this.req.query;
        try {
            this.req.getLogger(__filename).info(`Fetching GL Handoff Runs for child coa ${childCoaId} of tenant ${tenantId}`);
            const response = await this.axios.get(
                `${process.env.PARENT_COA_URL}/api/v1/tenants/${tenantId}/child-coas/${childCoaId}/gl-handoff-runs`,
                {
                    headers: { ['authorization']: token },
                    ...(pageQueryParams && { params: pageQueryParams }),
                },
            );
            return response.data;
        } catch (error) {
            this.req.getLogger(__filename).error(`Error while fetching GL Handoff Runs for child coa ${childCoaId} of tenant ${tenantId}`, error);
            throw new InternalAPIException({
                api: 'getGlHandoffRuns',
                error: createErrorObj(error),
            });
        }
    }

    public async getParentCoaConectionDetails(): Promise<CoaConnectionDetails> {
        const token = this.req.headers.authorization;
        const { tenantId, parentCoaCode, childCoaCode, connectorId } = this.req.params as { [key: string]: string };
        try {
            this.req
                .getLogger(__filename)
                .info(
                    `Fetching connection details between Parent coa (${parentCoaCode}) and child coa (${childCoaCode}) for connector id ${connectorId} and tenant ${tenantId}`,
                );
            const response = await this.axios.get(
                `${process.env.PARENT_COA_URL}/api/v1/tenants/${tenantId}/child-coas/${childCoaCode}/parent-coas/${parentCoaCode}/connectors/${connectorId}`,
                {
                    headers: { ['authorization']: token },
                },
            );
            return response.data as CoaConnectionDetails;
        } catch (error) {
            this.req
                .getLogger(__filename)
                .error(
                    `Error while fetching connection details between Parent coa (${parentCoaCode}) and child coa (${childCoaCode}) for connector id ${connectorId} and tenant  ${tenantId}`,
                    error,
                );
            throw new InternalAPIException({
                api: 'getCoaConectionDetails',
                error: createErrorObj(error),
            });
        }
    }

    public async createParentCoaConnection(payload: CreateCoaConntectionRequest): Promise<[]> {
        const token = this.req.headers.authorization;
        const { tenantId, childCoaCode, parentCoaCode } = this.req.params as { [key: string]: string };
        try {
            this.req
                .getLogger(__filename)
                .info(
                    ` Creating a connection between Parent coa (${parentCoaCode}) and child coa (${childCoaCode})  for tenant ${tenantId}`,
                );
            const response = await axios.post(
                `${process.env.PARENT_COA_URL}/api/v1/tenants/${tenantId}/child-coas/${childCoaCode}/parent-coas/${parentCoaCode}/connectors`,
                payload,
                {
                    headers: { ['authorization']: token },
                },
            );
            return response.data;
        } catch (error) {
            this.req
                .getLogger(__filename)
                .error(
                    `Error while creating a connection between Parent coa (${parentCoaCode}) and child coa (${childCoaCode})  for tenant ${tenantId}`,
                    error,
                );
            throw new InternalAPIException({
                api: 'createParentCoaConnection',
                error: createErrorObj(error),
            });
        }
    }

    public async updateParentCoaConnectionMap(
        payload: UpdateCoaConntectionMapRequest | UpdateConnectorStatus,
    ): Promise<ConnectionDetails> {
        const token = this.req.headers.authorization;
        const { tenantId, parentCoaCode, childCoaCode, connectorId } = this.req.params as { [key: string]: string };
        try {
            if (payload && payload.requestType === CONNECTION_REQUEST_TYPE.statusChange) {
                this.req
                    .getLogger(__filename)
                    .info(
                        `Updating the status of connection for connector id ${connectorId}, coa id ${childCoaCode} and tenant ${tenantId}`,
                    );
            } else {
                this.req
                    .getLogger(__filename)
                    .info(
                        `Updating mappings between Parent coa (${parentCoaCode}) and child coa (${childCoaCode}) for connector id ${connectorId} and tenant ${tenantId}`,
                    );
            }
            const response = await axios.put(
                `${process.env.PARENT_COA_URL}/api/v1/tenants/${tenantId}/child-coas/${childCoaCode}/parent-coas/${parentCoaCode}/connectors/${connectorId}`,
                payload,
                {
                    headers: { ['authorization']: token },
                },
            );
            return response.data;
        } catch (error) {
            if (payload && payload.requestType === CONNECTION_REQUEST_TYPE.statusChange) {
                this.req
                    .getLogger(__filename)
                    .error(
                        `Error while updating connector status for (${connectorId}) , child coa (${childCoaCode}) and tenant  ${tenantId}`,
                        error,
                    );
            } else {
                this.req
                    .getLogger(__filename)
                    .error(
                        `Error while updating mappings connection details between Parent coa (${parentCoaCode}) and child coa (${childCoaCode}) for connector id ${connectorId} and tenant  ${tenantId}`,
                        error,
                    );
            }
            throw new InternalAPIException({
                api: 'updateParentCoaConnectionMap',
                error: createErrorObj(error),
            });
        }
    }
    /**
     * The function `getCoaSummaryGoldenSchema` retrieves the summary of a Chart of Accounts (COA)
     * using the Golden Schema API.
     * @param requestParams - The `requestParams` parameter is an object that contains the following
     * properties:
     * @param {CoaSummaryGoldenSchemaPayload} payload - The `payload` parameter is an object that
     * contains various properties used to construct the API request. Here are the properties of the
     * `payload` object:
     * @returns a Promise that resolves to an array of CoaSummaryGoldenSchemaResponse objects.
     */
    public async getCoaSummaryGoldenSchema(
        requestParams: { tenantId: string; coaId: string; coaCode: string },
        payload: CoaSummaryGoldenSchemaPayload,
        cruxToken?: string,
    ) {
        try {
            const { tenantId = '', coaId = '', coaCode = '' } = requestParams || {};
            const auraUrl = this.getArgusUrl({ tenantId, coaCode });
            const authToken = cruxToken || (await TokenService.getToken(requestParams.tenantId));
            // This is done to properly create the query as expected by the API
            const postingCategoriesQuery = payload?.rowsTabEventPayload?.postingCategories.length
                ? `posting_category_code.in.(${payload?.rowsTabEventPayload?.postingCategories.toString()})`
                : '';
            const voucherCodesQuery = payload?.rowsTabEventPayload?.voucherCodes.length
                ? `voucher_code.in.(${payload.rowsTabEventPayload?.voucherCodes.toString()})${
                      payload?.rowsTabEventPayload?.postingCategories.length > 0 ? ',' : ''
                  }`
                : '';
            const balanceCodesCols =
                payload?.columnsTabEventPayload?.balanceCodes &&
                Object.keys(payload?.columnsTabEventPayload?.balanceCodes).length > 0
                    ? Object.keys(payload?.columnsTabEventPayload?.balanceCodes)
                    : '';
            const balanceCodesRows =
                payload?.rowsTabEventPayload?.balanceCodes &&
                Object.keys(payload?.rowsTabEventPayload?.balanceCodes).length > 0
                    ? Object.keys(payload?.rowsTabEventPayload?.balanceCodes)
                    : '';
            const balanceCodesAggregatedArr = [];
            if (balanceCodesCols.length > 0) {
                balanceCodesAggregatedArr.push(...balanceCodesCols);
            }
            if (balanceCodesRows.length > 0) {
                balanceCodesAggregatedArr.push(...balanceCodesRows);
            }
            const balanceCodesQuery = balanceCodesAggregatedArr.length
                ? `balance_code.in.(${balanceCodesAggregatedArr.toString()})${
                      payload?.rowsTabEventPayload?.voucherCodes.length ||
                      payload?.rowsTabEventPayload?.postingCategories?.length > 0
                          ? ','
                          : ''
                  }`
                : '';

            const params = new URLSearchParams({
                // fetching all the aggregation as we can't fetch data with multiple aggregation in single request
                aggregation: `in.(SUM,AVG,MIN,MAX)`,
                coa_id: `eq.${coaId}`,
                ifi_id: `eq.${tenantId}`,
            });

            if (payload?.basicParametersEventPayload?.cycleList) {
                const cycleQueries = payload.basicParametersEventPayload.cycleList
                    .filter(
                        (cycle) =>
                            cycle && cycle.cycleId && Array.isArray(cycle.periodsData) && cycle.periodsData.length > 0,
                    )
                    .map((cycle) => {
                        const cycleId = cycle.cycleId;
                        const periodSequenceNumbers = cycle.periodsData
                            .map((period) => period.sequenceNumber)
                            .filter((sequenceNumber) => sequenceNumber !== undefined && sequenceNumber !== null)
                            .toString();

                        return periodSequenceNumbers
                            ? `and(cycle_id.eq.${cycleId} , period_sequence_number.in.(${periodSequenceNumbers}))`
                            : null;
                    })
                    .filter((query) => query !== null);

                if (cycleQueries.length > 0) {
                    params.append('or', `(${cycleQueries.join(',')})`);
                }
            } else {
                // Append cycle_id and period_sequence_number to params
                params.append('cycle_id', `eq.${payload.basicParametersEventPayload.cycleId}`);
                params.append(
                    'period_sequence_number',
                    `in.(${payload.basicParametersEventPayload.periodsData
                        .map((period) => period.sequenceNumber)
                        .toString()})`,
                );
            }

            if (voucherCodesQuery || balanceCodesQuery || postingCategoriesQuery) {
                params.append(
                    'or',
                    // And condition added to fetch aggregated data for the coa nodes
                    `(${balanceCodesQuery}${voucherCodesQuery}${postingCategoriesQuery},and(voucher_code.is.null,balance_code.is.null,posting_category_code.is.null))`,
                );
            }
            const nodeArrayQuery = payload?.rowsTabEventPayload?.coaNodes?.length
                ? `(node_id.in.(${payload.rowsTabEventPayload?.coaNodes.toString()}),system_ledger_id.is.null)`
                : '';
            const systemLedgerQuery = payload?.systemLedgers?.length
                ? `(system_ledger_id.in.(${payload.systemLedgers.toString()})${nodeArrayQuery ? ',and' : ''}`
                : '';
            if (systemLedgerQuery || nodeArrayQuery) {
                params.append('or', `${systemLedgerQuery}${nodeArrayQuery}${systemLedgerQuery ? ')' : ''}`);
            }
            const response = await this.axios.get(`${auraUrl}/coa_summary_golden_schema`, {
                params,
                headers: { ['Gan-AuthTOken']: authToken },
            });
            return response.data as CoaSummaryGoldenSchemaResponse[];
        } catch (error) {
            this.req.getLogger(__filename).error('Coa summary golden schema - ', error);
            throw new InternalAPIException({
                api: 'getCoaSummaryGoldenSchema',
                error: createErrorObj(error),
            });
        }
    }

    /**
     *  To handle changes in url for different zones Sample URLs -
     *  Preprod - https://<%= tenantId %>-argus.internal.mum1-pp.zetaapps.in/ganymede/aads
     *  UAT - https://argus.zone.olympus.infra/ganymede/aads-<%= tenantId %>/coa_summary_golden_schema
     */
    private getArgusUrl = (requestParams: TemplateData) => {
        return renderTemplate(process.env.ARGUS_GANYMEDE_URL, requestParams);
    };
}
