import JSON<PERSON>igIntMod from 'json-bigint';
import { Request } from 'express';
import { BaseService } from './BaseService';
import { InternalAPIException } from '../exceptions/InternalAPIException';
import {
    GetDeclinedTransactionsParams,
    GetDeclinedTransactionsResponse,
} from './Interfaces/Aura';
import { createErrorObj } from './../utils';

const JSONBigInt = JSONBigIntMod({ storeAsString: true });

export class LedgerManagerService extends BaseService {
    constructor(req: Request) {
        super(req, {
            baseURL: process.env.LEDGER_MANAGER_URL,
            headers: {
                'Content-Type': 'application/json',
            },
            transformRequest: (data: any) => (data ? JSONBigInt.parse(data) : data),
            transformResponse: (data: any) => (data ? JSONBigInt.parse(data) : data),
        });
    }

    public async getDeclinedTransactionsData(
        requestParams: GetDeclinedTransactionsParams,
        token: string,
    ): Promise<GetDeclinedTransactionsResponse> {
        const { tenantId, coaId, ...params } = requestParams;
        try {
            this.req.getLogger(__filename).info('Fetching declined transaction data', requestParams);
            const response = await this.axios.get(
                `/ledger-manager/tenants/${tenantId}/coas/${coaId}/policyViolations`,
                {
                    headers: { ['authorization']: token },
                    params,
                },
            );
            return response.data as GetDeclinedTransactionsResponse;
        } catch (error) {
            this.req.getLogger(__filename).error(error)
            throw new InternalAPIException({
                api: 'getDeclinedTransactionsData',
                error: createErrorObj(error)
            });
        }
    }
}
