import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { Request } from 'express';
import { attachAxiosRequestLogger } from '@zeta/node-logger';

export class BaseService {
    axios: AxiosInstance;
    req: Request;
    constructor(
        req: Request,
        axiosConfig: AxiosRequestConfig,
        logRequestBodyTransformer?: Parameters<typeof attachAxiosRequestLogger>[0]['requestBodyTransformer'],
    ) {
        axiosConfig.timeout = axiosConfig.timeout || parseInt(process.env.AXIOS_REQUEST_TIMEOUT, 10);
        this.axios = axios.create(axiosConfig);
        // initializing axios-curlirize with your axios instance
        this.req = req;
        attachAxiosRequestLogger({
            axiosInstance: this.axios,
            label: this.constructor.name,
            req: req,
            requestBodyTransformer: logRequestBodyTransformer,
        });
    }
}
