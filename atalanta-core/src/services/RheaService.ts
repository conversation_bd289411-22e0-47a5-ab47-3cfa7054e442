import axios, { AxiosPromise } from 'axios';
import {
    GetSignedUrlRequest,
    GetSignedUrlResponse,
    FormVariables,
    GetTaskFormVariablesRequest,
} from './Interfaces/Rhea';

const axiosInstance = () => {
    const instance = axios.create({
        baseURL: `${process.env.CAMUNDA_SERVICE_BASE_URL}`,
        headers: {
            'Content-Type': 'application/json',
        },
    });

    instance.interceptors.response.use((res) => {
        return res;
    });
    return instance;
};

export const getSignedUrl = async (
    request: GetSignedUrlRequest,
    token: string,
): Promise<AxiosPromise<GetSignedUrlResponse>> => {
    const { tenantId } = request;
    const rheaAxiosInstance = axiosInstance();
    rheaAxiosInstance.defaults.headers['authorization'] = token;
    return await rheaAxiosInstance.post(
        `/tenants/${tenantId}/process-definition/key/GetSignedUrl/tenant-id/${tenantId}/start`,
        request,
    );
};

export const getTaskFormVariables = async (
    request: GetTaskFormVariablesRequest,
    token: string,
): Promise<AxiosPromise<FormVariables>> => {
    const { tenantId, taskId } = request;
    const rheaAxiosInstance = axiosInstance();
    rheaAxiosInstance.defaults.headers['authorization'] = token;
    return await rheaAxiosInstance.get(`tenants/${tenantId}/task/${taskId}/form-variables`);
};
