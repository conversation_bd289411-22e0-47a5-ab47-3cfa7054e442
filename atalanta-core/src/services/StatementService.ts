import { Request } from 'express';
import { BaseService } from './BaseService';
import { InternalAPIException } from '../exceptions/InternalAPIException';
import { GenerateStatementsRequest, GenerateStatementsRequestByStatementId, GenerateStatementsResponse } from './Interfaces/Aura';
import { createErrorObj } from './../utils';
export class StatementService extends BaseService {
    constructor(req: Request) {
        super(req, {
            baseURL: process.env.STATEMENT_URL,
            headers: {
                'Content-Type': 'application/json',
            },
        })
    };

    servicePath(): string {
        return `statement-service/statements`;
    }

    public async generateStatements(requestParams: GenerateStatementsRequest): Promise<GenerateStatementsResponse> {
        const { tenantID, coaID, ledgerID, cycleID, periodID } = requestParams;
        try {
            if (tenantID && coaID && ledgerID && cycleID && periodID) {
                this.req.getLogger(__filename).info('Generating statements', requestParams)
                const response = await this.axios.get(`/${this.servicePath()}/tenants/${tenantID}/coas/${coaID}/ledgers/${ledgerID}/cycles/${cycleID}/periods/${periodID}`, {
                });
                return response.data as GenerateStatementsResponse
            }
        } catch (error) {
            this.req.getLogger(__filename).error('Error', { error });
            throw new InternalAPIException({
                api: 'generateStatements',
                error: createErrorObj(error)
            });
        }
    }

    public async generateStatementsByStatementId(requestParams: GenerateStatementsRequestByStatementId): Promise<GenerateStatementsResponse> {
        const { tenantID, statementID } = requestParams;
        try {
            if (tenantID && statementID) {
                this.req.getLogger(__filename).info('Generating statements by statementID', requestParams)
                const response = await this.axios.get(`/${this.servicePath()}/tenants/${tenantID}/statements/${statementID}`, {
                });
                return response.data as GenerateStatementsResponse
            }
        } catch (error) {
            this.req.getLogger(__filename).error('Error while fetching statement by statementID', { error });
            throw new InternalAPIException({
                api: 'generateStatementsByStatementId',
                error: createErrorObj(error)
            });
        }
    }
}