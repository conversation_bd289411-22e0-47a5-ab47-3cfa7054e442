import { CreditAccountDetails, GetAllCreditAccountRequest } from './Interfaces/Ruby';
import { BaseService } from './BaseService';
import { Request } from 'express';
import { InternalAPIException } from '../exceptions/InternalAPIException';
import { createErrorObj } from './../utils';
export class RubyService extends BaseService {
    constructor(req: Request) {
        super(req, {
            baseURL: process.env.RUBY_BASE_URL,
            headers: {
                'Content-Type': 'application/json',
            },
        })
    };

    servicePath(): string {
        return `/ruby`;
    }

    public async getAllCreditAccounts(filterParams: GetAllCreditAccountRequest): Promise<CreditAccountDetails[]> {
        const { tenantID, ...params } = filterParams;
        const requestParams = {...params};
        delete requestParams.token;
        try {
            const response = await this.axios.get(`${this.servicePath()}/tenants/${tenantID}/accounts`, {
                headers: { ['x-zeta-authtoken']: params.token },
                params: requestParams
            });
            return response.data as CreditAccountDetails[];
        } catch (error) {
            this.req.getLogger(__filename).error('Error', { error });
            throw new InternalAPIException({
                api: 'getAllCreditAccounts',
                error: createErrorObj(error)
            });        
        }
    }
}