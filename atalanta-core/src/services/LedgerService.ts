import JSONBigIntMod from 'json-bigint';
import { Request } from 'express';
import { BaseService } from './BaseService';
import { InternalAPIException } from '../exceptions/InternalAPIException';
import {
    GetLedgerRequest,
    GetLedgerResponse,
} from './Interfaces/Tachyon';
import { createErrorObj } from './../utils';

const JSONBigInt = JSONBigIntMod({ storeAsString: true });

export class LedgerService extends BaseService {
    constructor(req: Request) {
      super(req, {
            baseURL: process.env.LEDGER_URL,
            headers: {
                'Content-Type': 'application/json',
            },
            transformRequest: (data: any) => (data ? JSONBigInt.parse(data) : data),
            transformResponse: (data: any) => (data ? JSONBigInt.parse(data) : data),
        });
    }

    public async getLedger(requestParams: GetLedgerRequest): Promise<GetLedgerResponse> {
        const { tenantId, coaId, ledgerId, token } = requestParams;
        try {
            this.req.getLogger(__filename).info(`Fetching ledger ${ledgerId} details`, requestParams);
            const response = await this.axios.get(`tenants/${tenantId}/coas/${coaId}/ledgers/${ledgerId}`, {
                headers: { ['authorization']: token },
            });
            return response.data as GetLedgerResponse;
        } catch (error) {
            this.req.getLogger(__filename).error(error);
            throw new InternalAPIException({
                api: 'getLedger',
                error: createErrorObj(error)
            });
        }
    }
}
