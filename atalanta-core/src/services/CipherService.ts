import qs from 'qs';
import { InternalAPIException } from '../exceptions/InternalAPIException';
import { BaseService } from './BaseService';
import { Request } from 'express';
import { GetAuthorizationResponse, UserAuthRequest, GetAuthResponse, SessionLoginActivityRequest, GetSessionLoginActivityResponse } from './Interfaces/Cipher';
import { AUTHORIZATION_HEADER } from '../Constants';
import { createErrorObj, extractToken } from './../utils';
export class CipherService extends BaseService {
    constructor(req: Request) {
        super(req, {
            baseURL: process.env.CERBERUS_SERVICE_BASE_URL,
            headers: {
                'Content-Type': 'application/json',
            },
        });
    }

    public async authenticate(authToken: string): Promise<GetAuthResponse> {
        try {
            const url = `/zeta.in/sessions/1.0/authenticate`;
            const response = await this.axios.get(url, { headers: { [AUTHORIZATION_HEADER]: `Bearer ${authToken}` } });
            return response.data;
        } catch (error) {
            this.req.getLogger(__filename).error('Authentication Error:', error);
            throw new InternalAPIException({
                api: 'authenticate',
                error: createErrorObj(error)
            });
        }
    }

    public async authorize(request: UserAuthRequest): Promise<GetAuthorizationResponse> {
        const { objectJID, action, token } = request;
        try {
            const url = `/sandbox/tenants/getContext?${qs.stringify({
                objectJID,
                action,
            })}`;
            const response = await this.axios.get(url, {
                headers: { [AUTHORIZATION_HEADER]: `Bearer ${token}` },
            });
            return response.data;
        } catch (error) {
            this.req.getLogger(__filename).error('Authorization Error:', error);
            throw new InternalAPIException({
                api: 'authorization',
                error: createErrorObj(error)
            });
        }
    }
    public async getEnrichedContext(objectJID: string): Promise<any> {
        const token = extractToken(this.req);
        try {
            const url = `/sandbox/tenants/getEnrichedContext?${qs.stringify({
                objectJID
            })}`;
            const response = await this.axios.get(url, {
                headers: { [AUTHORIZATION_HEADER]: `Bearer ${token}` },
            });
            this.req.getLogger(__filename).info(`Enriched Context retreived successfully for ${objectJID}`);
            return response.data;
        } catch (error) {
            this.req.getLogger(__filename).error('Get Enriched Context Error:', error);
            throw new InternalAPIException({
                api: 'enrichedContext',
                error: createErrorObj(error)
            });
        }
    }
    public async getSessionLoginActivity(requestParams: SessionLoginActivityRequest): Promise<GetSessionLoginActivityResponse>{
        const {tenantId,domainId,authProfileId, resourceId } = requestParams;
        const token = extractToken(this.req);
        try {
            const url = `${process.env.CIPHER_SSO_BASE_URL}/tenants/${tenantId}/domains/${domainId}/authProfiles/${authProfileId}/resources/${resourceId}/loginActivity`;
            const response = await this.axios.get(url, {
                headers: { [AUTHORIZATION_HEADER]: `Bearer ${token}` },
            });
            this.req.getLogger(__filename).info(`Login Activity retreived successfully for ${authProfileId}`);
            return response.data;
        } catch (error) {
            this.req.getLogger(__filename).error('Get Login Activiy details error Error:', error);
            throw new InternalAPIException({
                api: 'sessionLoginActivity',
                error: createErrorObj(error)
            });
        }
    };
}
