import { Request } from 'express';
import { BaseService } from './BaseService';
import { GetAuditEntityDefinitionListResponse, GetAuditEventDefinitionListResponse } from './Interfaces/Audit';
import { InternalAPIException } from '../exceptions/InternalAPIException';
import { createErrorObj } from './../utils';
export class AuditService extends BaseService {

    constructor(req: Request) {
        super(req, {
            baseURL: process.env.AUDIT_LOG_SERVICE_URL,
            headers: {
                'Content-Type': 'application/json',
            },
        })
    };

    public async getAuditEventDefinitionList(): Promise<GetAuditEventDefinitionListResponse> {
        const token = this.req.headers.authorization;
        const { tenantId } = this.req.params as { [key: string]: string };
        const pageQueryParams = this.req.query;
        try {
            const response = await this.axios.get(`v1/tenants/${tenantId}/event-definitions`, {
                headers: { ['authorization']: token },
                ...(pageQueryParams && { params: pageQueryParams }),
            });
            return response.data as GetAuditEventDefinitionListResponse;
        } catch (error) {
            this.req.getLogger(__filename).error('API getAuditEventDefinitionList Error:', error);
            throw new InternalAPIException({
                api: 'getAuditEventDefinitionList',
                error: createErrorObj(error)
            });
        }
    };
    public async getAuditEntityDefinitionList(): Promise<GetAuditEntityDefinitionListResponse> {
        const token = this.req.headers.authorization;
        const { tenantId } = this.req.params as { [key: string]: string };
        const pageQueryParams = this.req.query;
        try {
            const response = await this.axios.get(`v1/tenants/${tenantId}/entity-definitions`, {
                headers: { ['authorization']: token },
                ...(pageQueryParams && { params: pageQueryParams }),
            });
            return response.data as GetAuditEntityDefinitionListResponse;
        } catch (error) {
            this.req.getLogger(__filename).error('API getAuditDefinitionList Error:', error);
            throw new InternalAPIException({
                api: 'getAuditEntityDefinitionList',
                error: createErrorObj(error)
            });
        }
    };
}