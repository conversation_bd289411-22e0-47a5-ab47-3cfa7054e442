import axios, { AxiosPromise } from 'axios';
import {
    SendIdentityChallengeRequest,
    VerifyIdentityChallengeRequest,
    VerifyIdentityChallengeResponse,
} from './Interfaces/IdentityChallenge';

const axiosInstance = () =>
    axios.create({
        baseURL: `${process.env.ANONUSER_SERVICE_BASE_URL}/anonuser/1.0`,
    });

export const sendIdentityChallenge = async (request: SendIdentityChallengeRequest) => {
    return await axiosInstance().post(`/sendIdentityChallenge`, request);
};

export const verifyIdentityChallenge = async (
    request: VerifyIdentityChallengeRequest,
): Promise<AxiosPromise<VerifyIdentityChallengeResponse>> => {
    console.log(`VerifyIdentityChallengeRequest`, request);
    return await axiosInstance().post(`/verifyIdentityChallenge`, request);
};
