import axios, { AxiosPromise } from 'axios';
import {
    DocumentRequestParams,
    DocumentRequestBody,
    DocumentResponse,
    CheckStatusRequestParams,
    CheckStatusResponse,
} from './Interfaces/GitDb';

const gitDbAxiosInstance = () => {
    const instance = axios.create({
        baseURL: `${process.env.GITDB_BASE_URL}`,
        headers: {
            'Content-Type': 'application/json',
        },
    });
    return instance;
};

const subOrchestratorInstance = () => {
    const instance = axios.create({
        baseURL: `${process.env.SUBSCRIPTION_ORCHESTRATOR_URL}`,
    });
    return instance;
};

export const getDocument = (params: DocumentRequestParams): AxiosPromise<DocumentResponse> => {
    return gitDbAxiosInstance().get(
        `/1.0/tenants/${params.tenantId}/db/${params.dbName}/changesets/master/schemas/${params.schemaName}/documentByName/${params.documentName}`,
        { params: { path: params.parentDir } },
    );
};

export const addMultipleDocument = (
    params: DocumentRequestParams,
    body: DocumentRequestBody,
): AxiosPromise<DocumentResponse> => {
    return gitDbAxiosInstance().post(
        `/1.0/tenants/${params.tenantId}/db/${params.dbName}/changesets/master/multi-documents`,
        body,
    );
};

export const checkSubStatus = (params: CheckStatusRequestParams): AxiosPromise<CheckStatusResponse> => {
    return subOrchestratorInstance().get(
        `/1.0/tenants/${params.tenantId}/subscriptions/${params.subscriptionId}/provisions/${params.commitId}/status`,
    );
};

export const getProducts = (params: any): AxiosPromise<CheckStatusResponse> => {
    return gitDbAxiosInstance().get(
        `/1.0/tenants/${params.tenantId}/db/${params.dbName}/changesets/master/documents?outputType=Nested`,
    );
};

export const getAllCommitByProduct = (params: any): AxiosPromise<CheckStatusResponse> => {
    return gitDbAxiosInstance().get(
        `/1.0/tenants/${params.tenantId}/db/${params.dbName}/changesets/master/commits?path=product_configurations/${params.productCode}`,
    );
};

export const getProductDocuments = (params: any): AxiosPromise<CheckStatusResponse> => {
    return gitDbAxiosInstance().get(
        `/1.0/tenants/${params.tenantId}/db/${params.dbName}/changesets/master/documents?folderName=product_configurations/${params.productCode}&outputType=Nested&responseType=Full&commitId=${params.productVersion}`,
    );
};
