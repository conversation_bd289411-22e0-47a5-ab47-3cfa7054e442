import axios, { AxiosPromise } from 'axios';
import { BaseService } from './BaseService';
import {
    SetCollectionItemPaylod,
    SetPermissionsPayload,
    DeleteCollectionItemPaylod,
    GetPermissionsPayload,
    GetPermissionsResponse,
    PERM_TYPE,
    SaveViewPayload,
    DeleteViewPayload,
    GetViewsPayload,
    GetLatestCollectionItemsPayload,
    GetViewsCollectionResponse,
    GetCollectionItemPayload,
    GetSingleViewPayload,
    CollectionItem,
    GetViewsCollectionFragmentResponse,
} from './Interfaces/Collections';
import crypto from 'crypto';
import { Request } from 'express';
import TokenService from './Token.service';
import { CipherService } from './CipherService';
import { getAuthDetails } from '../utils';

export const PERMISSIONS = {
    WRITE: 'w',
    MANAGER: 'm',
    READ: 'r',
};

const generateUniqueId = () => {
    // Get current timestamp in milliseconds
    const timestamp = Date.now();

    // Generate a random component using crypto module
    const randomBytes = crypto.randomBytes(8);
    const randomComponent = parseInt(randomBytes.toString('hex'), 16);

    const uniqueId = `${timestamp}_${randomComponent}`;

    return uniqueId;
};

interface CollectionServiceMeta {
    collection: string;
    privateNameSpace: string;
    publicNameSpace: string;
    userJID: string;
    authProfileId: string;
    tenantId: string;
}

export class CollectionsService extends BaseService {
    private collection: string = '';
    private publicNameSpace: string = '';
    private privateNameSpace: string = '';
    private userJID: string = '';
    private tenantId: string = '';

    constructor(request: Request, collectionMeta: CollectionServiceMeta) {
        const { collection, privateNameSpace, publicNameSpace, userJID, tenantId } = collectionMeta;
        super(request, {
            baseURL: process.env.COLLECTION_BASE_URL,
            headers: {
                'Content-Type': 'application/json',
            },
        });
        if (collection && privateNameSpace && publicNameSpace && userJID && tenantId) {
            this.collection = collection;
            this.privateNameSpace = privateNameSpace;
            this.publicNameSpace = publicNameSpace;
            this.userJID = userJID;
            this.tenantId = tenantId;
        } else {
            throw new Error('CollectionServiceException');
        }
    }

    private generateCollectionID(namespaceID: string): string {
        return `${namespaceID}.${this.collection}@collections.zeta.in`;
    }
    private generateDeleteViewPayload({
        namespaceID,
        itemID
    }: {
        namespaceID: string;
        itemID: string;
    }): DeleteCollectionItemPaylod {
        return {
            itemID: itemID || generateUniqueId(),
            to: this.generateCollectionID(namespaceID),
        }
    }

    private generateSaveViewPayload({
        namespaceID,
        data: payloadData,
        itemID,
    }: {
        namespaceID: string;
        data: unknown;
        itemID: string;
    }): SetCollectionItemPaylod {
        return {
            // if itemID is present it will update the data instead of creating new collection item
            itemID: itemID || generateUniqueId(),
            collectionID: `${namespaceID}.${this.collection}`,
            data: JSON.stringify(payloadData),
            to: this.generateCollectionID(namespaceID),
        };
    }

    private generateGetViewsPayload({
        count,
        includeDeletedItems,
        namespaceID,
    }: {
        count: string | number;
        includeDeletedItems: boolean;
        namespaceID: string;
    }): GetLatestCollectionItemsPayload {
        const collectionID = this.generateCollectionID(namespaceID);
        return {
            count,
            includeDeletedItems,
            to: collectionID,
            collectionID: collectionID.split('@')[0],
        };
    }

    public async getViews(payload: GetViewsPayload, authToken: string) {
        try {
            const { includeDeletedItems = false, includePublicViews = false } = payload;
            const privateNamespaceID = this.privateNameSpace;
            const publicNamespaceID = this.publicNameSpace;
            const count = process.env.COLLECTION_PAGE_SIZE || '10';
            const privateViewsPayload = this.generateGetViewsPayload({
                namespaceID: privateNamespaceID,
                includeDeletedItems,
                count,
            });

            const promises = [
                this.fetchAllCollectionItems(authToken, privateViewsPayload).then(({ data }) => ({
                    data,
                    isPublic: false,
                })),
            ];
            if (includePublicViews) {
                const publicViewsPayload = this.generateGetViewsPayload({
                    namespaceID: publicNamespaceID,
                    includeDeletedItems,
                    count,
                });
                promises.push(
                    this.fetchAllCollectionItems(authToken, publicViewsPayload).then(({ data }) => ({
                        data,
                        isPublic: true,
                    })),
                );
            }

            const responses = await Promise.allSettled(promises);
            const allViews = [];

            for (const response of responses) {
                if (response.status === 'fulfilled') {
                    const viewsData = response.value.data as GetViewsCollectionResponse;
                    const isPublic = response.value.isPublic;
                    allViews.push(
                        ...(viewsData?.items.map(({ itemID, data, createdAt, updatedAt }) => ({
                            itemID,
                            data: JSON.parse(data),
                            createdAt,
                            updatedAt,
                            isPublic,
                        })) || []),
                    );
                }
            }

            return allViews;
        } catch (error) {
            throw error;
        }
    }

    private getRegexForAuthDomain(domain: string) {
        return `*@${domain}`;
    }
    
    private deleteCollectionItem = async (payload: DeleteCollectionItemPaylod, token: string) => {
        const { itemID, to } = payload;
        try {
            this.req.getLogger(__filename).info('Deleting collection item', { itemID, to });
            const response = await this.axios.post(
                '/deleteCollectionItem',
                payload,
                { headers: { ['x-zeta-authtoken']: token } },
            );
            this.req
                .getLogger(__filename)
                .info('Deleting collection item: Success', { itemID, to });
            return { ...response.data, message: 'Delete view successful', itemID, to };
        } catch (error) {
            this.req
                .getLogger(__filename)
                .error('Deleting collection item: Failed', { itemID, to, error });
            throw error;
        }
    };

    public async deleteView(payload: DeleteViewPayload, authToken: string) {
        const { itemID = '', isPublic = false } = payload;
        const namespaceID = isPublic ? this.publicNameSpace : this.privateNameSpace;
        const deleteViewPayload = this.generateDeleteViewPayload({
            namespaceID,
            itemID
        })
        return await this.deleteCollectionItem(deleteViewPayload, authToken);
    }

    public async saveView(payload: SaveViewPayload, authToken: string) {
        const setCollectionItem = async (payload: SetCollectionItemPaylod, token: string) => {
            const { itemID, collectionID, data, to } = payload;
            try {
                this.req.getLogger(__filename).info('Settings collection item', { itemID, collectionID, data, to });
                const response = await this.axios.post(
                    '/setCollectionItem2',
                    { itemID, collectionID, data, to },
                    { headers: { ['x-zeta-authtoken']: token } },
                );
                this.req
                    .getLogger(__filename)
                    .info('Settings collection item: Success', { itemID, collectionID, data, to });
                return { ...response.data, message: 'View save successful', itemID, collectionID, data, to };
            } catch (error) {
                this.req
                    .getLogger(__filename)
                    .error('Settings collection item: Failed', { itemID, collectionID, data, to, error });
                throw error;
            }
        };

        try {
            const { itemID = '', isPublic = false, isNewView = true } = payload;
            const namespaceID = isPublic ? this.publicNameSpace : this.privateNameSpace;
            const authDomain = this.userJID.split('@')[1];
            // Derive the userJID based on whether the view is public or private
            const derivedUserJID = isPublic ? this.getRegexForAuthDomain(authDomain) : this.userJID;

            try {
                const { permissions = [] } = await this.checkIfNamespaceExists(
                    {
                        namespaceID,
                        pattern: derivedUserJID,
                    },
                    authToken,
                );

                // check if the item already exists while creating a new view
                // we have to check this for new view only
                if (isNewView) {
                    await this.checkIfItemExists(itemID, namespaceID, authToken);
                }

                const saveViewPayload = this.generateSaveViewPayload({
                    namespaceID,
                    data: payload.data,
                    itemID,
                });

                if (
                    permissions.includes(PERMISSIONS.MANAGER as PERM_TYPE) ||
                    permissions.includes(PERMISSIONS.WRITE as PERM_TYPE)
                ) {
                    return await setCollectionItem(saveViewPayload, authToken);
                } else {
                    throw new Error("User doesn't have permissions to save the view");
                }
            } catch (error) {
                if (error?.response?.data?.type === 'NamespaceNotFoundException') {
                    // get crux token, this crux token will have access to every view
                    // this is needed to access view in case of schedule views
                    this.req.getLogger(__filename).info('Fetching crux token', { tenantId: this.tenantId });
                    const cruxToken = await TokenService.getToken(this.tenantId);
                    this.req.getLogger(__filename).info('Fetching crux token : Success', { tenantId: this.tenantId });
                    const cipherService = new CipherService(this.req);
                    // authenticate and as part of this authentication get auth details
                    this.req
                        .getLogger(__filename)
                        .info('Authenticating crux token for auth details', { tenantId: this.tenantId });
                    const cruxAuthResponse = await cipherService.authenticate(cruxToken);
                    const { userJID: cruxUserJID } = getAuthDetails(cruxAuthResponse);
                    this.req
                        .getLogger(__filename)
                        .info('Authenticating crux token for auth details: Success', { tenantId: this.tenantId });
                    // namespace doesn't exist, create namespace
                    const namespacePayload = {
                        namespaceID,
                        permissionRecords: [
                            {
                                patterns: [derivedUserJID, cruxUserJID],
                                perms: [PERMISSIONS.MANAGER as PERM_TYPE],
                            },
                        ],
                    };

                    // check if the item already exists while creating a new view
                    // we have to check this for new view only
                    if (isNewView) {
                        await this.checkIfItemExists(itemID, namespaceID, authToken);
                    }

                    await this.createNamespace(namespacePayload, authToken);
                    // namespace created successfully, go ahead save the item
                    const saveViewPayload = this.generateSaveViewPayload({
                        namespaceID,
                        data: payload.data,
                        itemID,
                    });

                    return await setCollectionItem(saveViewPayload, authToken);
                } else {
                    // return error as it is
                    this.req.getLogger(__filename).error('Save view failed', { namespaceID, derivedUserJID, error });
                    throw error;
                }
            }
        } catch (err) {
            this.req.getLogger(__filename).error('Save view failed', { payload, error: err });
            throw err;
        }
    }

    private async checkIfItemExists(itemID: string, namespaceID: string, authToken: string) {
        const itemPayload = { itemID, collectionID: `${namespaceID}.${this.collection}` };
        try {
            this.req.getLogger(__filename).info('Checking if collection item exists', {
                itemID,
                collectionID: `${namespaceID}.${this.collection}`,
            });
            const response = await this.getCollectionItem(authToken, itemPayload);
            if (response?.data?.itemID) {
                if(response?.data?.isDeleted) {
                    this.req.getLogger(__filename).info('Checking if collection item exists : ArchivedItemExistsException', {
                        itemID,
                        collectionID: `${namespaceID}.${this.collection}`,
                    });
                    throw new Error('ArchivedItemExistsException');
                } else {
                    this.req.getLogger(__filename).info('Checking if collection item exists : ItemAlreadyExistsException', {
                        itemID,
                        collectionID: `${namespaceID}.${this.collection}`,
                    });
                    throw new Error('ItemAlreadyExistsException');
                }
            }
        } catch (error) {
            this.req.getLogger(__filename).error('Checking if collection item exist : Failed', {
                itemID,
                collectionID: `${namespaceID}.${this.collection}`,
                error,
            });
            if (axios.isAxiosError(error)) {
                // log error only if it is not CollectionItemNotFoundException
                // as we are checking if the item exists
                // CollectionItemNotFoundException should fail silently as it is expected
                if (error?.response?.data?.type !== 'CollectionItemNotFoundException') {
                    this.req
                        .getLogger(__filename)
                        .error('Error while checking if item exists in collection', itemPayload, error);
                }
            } else {
                throw error;
            }
        }
    }

    async fetchAllCollectionItems(authToken: string, payload: GetLatestCollectionItemsPayload) {
        let hasMoreItems = true;
        let totalItemsFetched = 0;
        let fromCollectionVersion = null;
        const allViews = [];
        const COLLECTION_MAX_ITEMS = process.env.COLLECTION_MAX_ITEMS ? Number(process.env.COLLECTION_MAX_ITEMS) : 100;
        while (hasMoreItems && totalItemsFetched < COLLECTION_MAX_ITEMS) {
            try {
                const response: { data: GetViewsCollectionFragmentResponse } =
                    await this.getOldestCollectionItemsFragmented(authToken, {
                        ...payload,
                        ...(fromCollectionVersion ? { fromCollectionVersion } : {}),
                    });

                const items = response.data?.fragments || [];
                const { count } = payload;

                if (items.length < Number(count)) {
                    allViews.push(...items);
                    hasMoreItems = false;
                } else {
                    allViews.push(...items);
                    totalItemsFetched += items.length;
                    // Update fromCollectionVersion for the next request
                    fromCollectionVersion = items[items.length - 1].updatedAt;
                }
            } catch (error) {
                this.req.getLogger(__filename).error('Error fetching collection items:', error);
                hasMoreItems = false;
            }
        }

        return { data: { items: allViews } };
    }

    async getOldestCollectionItemsFragmented(
        authToken: string,
        payload: GetLatestCollectionItemsPayload,
    ): Promise<{ data: GetViewsCollectionFragmentResponse }> {
        return this.axios.get('/getOldestCollectionItemsFragmented', {
            headers: { ['x-zeta-authtoken']: authToken },
            data: payload,
        });
    }

    private async getLatestCollectionItems(authToken: string, payload: GetLatestCollectionItemsPayload) {
        return this.axios.get('/getLatestCollectionItems', {
            headers: { ['x-zeta-authtoken']: authToken },
            data: payload,
        });
    }

    private async getCollectionItem(authToken: string, payload: GetCollectionItemPayload) {
        return this.axios.post('/getCollectionItem', payload, { headers: { ['x-zeta-authtoken']: authToken } });
    }

    public async getView(payload: GetSingleViewPayload, authToken: string): Promise<CollectionItem> {
        const { itemID, isPublic = false } = payload;
        const namespaceID = isPublic ? this.publicNameSpace : this.privateNameSpace;
        const itemPayload = { itemID, collectionID: `${namespaceID}.${this.collection}` };
        const response = await this.getCollectionItem(authToken, itemPayload);
        return response.data;
    }

    public async checkIfNamespaceExists(
        payload: GetPermissionsPayload,
        token: string,
    ): Promise<GetPermissionsResponse> {
        const { namespaceID = '', pattern = '' } = payload;
        try {
            this.req.getLogger(__filename).info('Checking if namespace exists', { namespaceID, pattern });
            const response = await this.axios.post(
                '/getPermissions',
                { namespaceID, pattern },
                { headers: { ['x-zeta-authtoken']: token } },
            );
            return response.data;
        } catch (error) {
            this.req
                .getLogger(__filename)
                .error('Checking if namespace exists : Failed', { namespaceID, pattern, error });
            throw error;
        }
    }

    public async createNamespace(payload: SetPermissionsPayload, token: string) {
        const { namespaceID = '', permissionRecords = [] } = payload;
        try {
            this.req.getLogger(__filename).info('Creating a new namespace', { namespaceID, permissionRecords });
            const response = await this.axios.post(
                '/setPermissions',
                { namespaceID, permissionRecords },
                { headers: { ['x-zeta-authtoken']: token } },
            );
            return response.data;
        } catch (error) {
            this.req
                .getLogger(__filename)
                .info('Creating a new namespace : Failed', { namespaceID, permissionRecords, error });
            throw error;
        }
    }
}
