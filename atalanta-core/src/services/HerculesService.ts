import axios, { AxiosPromise } from 'axios';
import { ApplicationTenantConfig } from './Interfaces/Hercules';

const axiosInstance = () => {
    const instance = axios.create({
        baseURL: `${process.env.HERCULES_SERVICE_BASE_URL}/api/v1`,
        headers: {
            Authorization: `Bearer ${process.env.LTT}`,
        },
    });

    instance.interceptors.response.use((res) => {
        return res;
    });
    return instance;
};

export const getTenantMeta = async (hostname: string): Promise<AxiosPromise<ApplicationTenantConfig>> => {
    return await axiosInstance().get(`/tenants/${hostname}`);
};
