import { Headers } from './common';

export type IdentityType = 'EMAIL' | 'PHONE_NUMBER';

export interface Identity {
    type: IdentityType;
    value: string;
}

export interface IdentityData {
    corpID?: string;
    templateGroup?: string;
    templateData?: { [k: string]: any };
    webLinkKey?: string;
    redirectUrl?: string;
    purpose?: string;
}

export interface SendIdentityChallengeRequest {
    identity?: Identity;
    data?: IdentityData;
}

export interface VerifyIdentityChallengeRequest {
    response: string;
    sessionToken: string | null;
    sessionID: string | null;
    shouldIssueACert: boolean;
}

export interface VerifyIdentityChallengeResponse {
    identity: Identity;
    headers: Headers;
}
