export interface WorkerMasterListResponse {
    tenantID: string;
    coaID: string;
    status: string;
    workerList: WorkerMasterList[]
}

export interface WorkerMasterList {
    name: string;
    entityType: string;
    periodicity: string[];
    phase: string[];
}

export interface WorkerMasterListRequestParams {
    tenantId: string;
    coaId: string;
    token: string;
}
export interface WorkerMasterListQueryParams {
    worker?: string;
    periodicity?: string;
    phase?: string;
    status?: string;
}