export interface CalendarCommon {
    token: string;
    tenantId: string;
    calendarId: string;
}

export interface GetClocksByTypeForCalenderIdRequest extends CalendarCommon {
    type?: string;
}

export interface GetClocksByTypeForCalenderIdResponse {
    id: string;
    tenantID: string;
    code: string;
    name: string;
    type: string;
    cbuDuration: number;
    startTime: number;
    cbusInAdvance: number;
    status: string;
    calendarID: string;
    cbuCycleID: string;
}

export interface GetCyclesForCalendarRequest extends CalendarCommon {
    clockId: string;
    periodicity?: string;
}

export interface GetCyclesForCalendarResponse {
    id: string;
    tenantID: string;
    periodicity: string;
    code: string;
    name: string;
    description: string;
    startCbuSequenceNumber: number;
    status: string;
    attributes?: {
        [index: string]: any;
    };
    calendarID: string;
    clockID: string;
}

export interface GetPeriodsRequest extends GetCyclesForCalendarRequest {
    cycleId: string;
    timestamp?: string | number;
    cycleCode?: string;
    clockType?: string;
    pageNo?: number;
    pageSize?: number;
    startSequenceNumber?: number;
    endSequenceNumber?: number;
}

export interface GetPeriodsResponse {
    id: string;
    tenantID: string;
    startCbuSequenceNumber: number;
    status: string;
    calendarID: string;
    clockID: string;
    cycleID: string;
    endCbuSequenceNumber: number;
    sequenceNumber: number;
    startTime: number;
    nextPeriodStartTime: number;
    formattedStartTime: string;
    formattedNextPeriodStartTime: string;
    tags?: string;
}

export interface CoaType {
    id: number | string;
    tenantId: number | string;
    code: string;
    name: string;
    currency: string;
    description: string;
    status: string;
    calendarId: number | string;
    rootNodeID?: number | string;
    attributes?: {
        [index: string]: any;
    };
}

export interface NodeType {
    nodeID: number | string;
    nodeType: string;
    nodeValue: string;
    aggregatedBalance: null;
    children: NodeType[];
    status: string;
    depth: number;
}

export interface GetCoaTreeRequest {
    tenantId: string;
    coaId: string;
    startLevel: number;
    endLevel: number;
    asOnTimestamp: number;
    includeSummaries?: boolean;
    pageNo?: number;
    pageSize?: number;
    sortBy?: string;
    token: string;
}

export interface GetCoaTreeResponse {
    coa: CoaType;
    nodes: NodeType[];
}

export interface GetLedgerRequest {
    tenantId: string;
    coaId: string;
    ledgerId: string;
    token: string;
}

export interface GetLedgerResponse {
    requestID: string;
    payloadHash: string;
    tenantID: number;
    ledgerID: number;
    userID: number;
    ledgerType: string;
    accountingType: string;
    balance: number;
    lastTransactionID: string;
    lastReceiptHash: string;
    lastPostingID: number;
    currency: string;
    createdAt: number;
    lastUpdatedAt: number;
    creditLimit: number;
    overdraftLimit: number;
    attrs: Attrs;
    ledgerState: string;
    debitors?: unknown[] | null;
    accountHolderDebitors?: string[] | null;
    accountHolderId: string;
    coaId: number;
    timeZone: string;
    parentNodeID: number;
    tags?: string[] | null;
    vectors?: VectorsEntity[] | null;
    vectorURIs?: string[] | null;
    customFields?: CustomFieldsEntity[] | null;
}
export interface Attrs {
    currency: string;
    'aura.coa-id': string;
    creditLimit: string;
    'account.pd-id': string;
    'account.pf-id': string;
    'account.openDate': string;
    'account.provider': string;
    dontPostInterest: string;
    'issuance.location': string;
    dontAssessInterest: string;
    'aura.parent-node-id': string;
    'ruby.annual-fee.treatment': string;
}
export interface VectorsEntity {
    ledgerID: string;
    tenantID: number;
    type: string;
    value: string;
    status: string;
}
export interface CustomFieldsEntity {
    tenantID: number;
    coaID: number;
    ledgerID: number;
    code: string;
    id: string;
    type: string;
    value: number;
    provider: string;
    description: string;
    bookTime: number;
    createdAt: string;
    modifiedAt: string;
    headers: Headers;
}
export interface Headers {
    [k: string]: unknown;
}

export namespace StatusVerificationReportsTypes {
    export interface GetStatusVerifierReportsRequest {
        tenantId: string;
        coaCode: string;
        cbus: string;
    }

    export interface GetStatusVerifierReportsResponse {
        pageInfo: PageInfo;
        contents: ReportContent[];
    }

    export interface PageInfo {
        totalElements: number;
        number: number;
        size: number;
        totalPages: number;
    }

    export interface ReportContent {
        phase: string;
        report: Report;
    }

    export interface Report {
        version: number;
        name: string;
        description: string;
        status: Status;
        statusVerifierCode: string;
        provider: string;
        tasks: Task[];
        attributes: Record<string, any>;
        clientProvidedCreatedAt: string;
        clientProvidedModifiedAt: string;
        createdAt: string;
        modifiedAt: string;
    }

    export interface Status {
        value: string;
        reason: string | null;
        reasonCode: string | null;
    }

    export interface Task {
        code: string;
        name: string;
        description: string;
        status: Status;
        kpis: KPI[];
        additionalDetailsUri: string;
        attributes: Record<string, any>;
        clientProvidedCreatedAt: string;
        clientProvidedModifiedAt: string;
        createdAt: string;
        modifiedAt: string;
        displaySequenceNumber: number;
    }

    export interface KPI {
        code: string;
        name: string;
        description: string;
        metric: Metric;
    }

    export interface Metric {
        value: string;
        unit: string;
    }
}