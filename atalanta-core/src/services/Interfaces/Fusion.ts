export interface GetAccountHolderRequest {
    tenantId: string;
    token: string;
    individualID: string;
}

export interface GetAccountHolderResponse extends AccountHolder {}
export interface Time {
    hour: number;
    minute: number;
    second: number;
    nano: number;
}
export interface DateTime {
    date: Date;
    time: Time;
}
export interface KYCStatusAttributes {
    pan: string;
    aadhaar: string;
    kycType: string;
    authType: string;
    corpUser: string;
    [k: string]: string;
}

export interface AccountHolder {
    accountHolderProviderID: string;
    attributes: object;
    createdAt: DateTime;
    dob?: string;
    firstName: string;
    gender: string;
    headers: object;
    id: string;
    ifiID: number;
    vectors: Vector[];
    lastName: string;
    middleName: string;
    mothersMaidenName?: string;
    name?: string;
    profilePicURL: string;
    requestID: string;
    salutation: string;
    status: string;
    type: string;
    updatedAt?: string;
    userId?: number;
    aan?: string;
}
export interface Vector {
    attributes: any;
    beneficiaryID: string;
    id: string;
    ifiID: number;
    type: string;
    updatedAt: any;
    value: string;
}
export interface GetResourceByVectorRequest {
    ifiID: string;
    token: string;
    vectorValue: string;
    vectorType: string;
    view: string;
}
export interface GetResourceByVectorResponse {
    accountHolderId?: string;
    ifi?: number;
    id: string;
    resourceProductId: string;
    resourceProduct: ResourceProduct;
    targetURI: string;
    formFactors: FormFactors[];
    tags: string[];
    vectors: string[];
    policies: Policies;
    attributes: object;
    status: string;
    createdAt: string;
    modifiedAt: string;
    headers: object;
}

export interface ResourceProduct {
    ifi?: number;
    id: string;
    code: string;
    name: string;
    description: string;
    formFactorProducts: FormFactorProducts[];
    tags: string[];
    policies: Policies;
    status: string;
    createdAt: string;
    modifiedAt: string;
    headers: object;
}
export interface FormFactorProducts {
    ifi?: number;
    id: string;
    code: string;
    name: string;
    description: string;
    type: string;
    policies: Policies;
    provider: string;
    skuID: string;
    tags: string[];
    attributes?: object;
    issuanceStatus: string;
    paymentStatus: string;
    createdAt: string;
    modifiedAt: string;
    headers: Headers;
}

export interface Policies {
    issuancePolicies?: string[];
    paymentPolicies?: string[];
}

export interface FormFactors {
    id: string;
    ifi?: number;
    formFactorProductID: string;
    formFactorID: string;
    targetURI: string;
    tags: string[];
    attributes: object;
    policies: Policies;
    status: string;
    createdAt: string;
    modifiedAt: string;
    headers: object;
    defaultAccountName?: string;
}
export interface CardDetailsResponse {
    formFactorID?: string;
    accountHolder?: string;
    cardID?: string | null;
    crn: string;
    cardType: string;
    maskedPan: string;
    cardStatus: string;
    id?: string;
    status?: any;
    formFactorProductName?: string;
}
export interface GetCardDetailsByFormFactorIDRequest {
    ifiID: string;
    token: string;
    formFactorID: string;
}
export interface GetAllPPAccountsRequest {
    token: string;
    ifiID: number;
    individualID: string;
    pageNumber: number;
    pageSize: number;
}
export interface GetAllPPAccountsResponse {
    accounts: AccountInfo[];
}
export interface AccountInfo {
    accountingType?: string;
    createdAt?: string;
    currency?: string;
    id?: string;
    ifiID?: number;
    name?: string;
    ownerAccountHolderID?: string;
    productFamilyID?: number;
    productFamilyName?: string;
    productID?: number;
    productName?: string;
    programIDs?: Array<string>;
    status?: string;
    updatedAt?: string;
}
