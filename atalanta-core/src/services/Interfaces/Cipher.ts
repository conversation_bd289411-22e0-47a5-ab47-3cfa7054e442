export interface UserAuthRequest {
    objectJID: string;
    action: string;
    token: string;
}

export interface GetAuthResponse {
    userResourceJid: string;
    sessionId: string;
    validTill: number;
    isSensitiveSession: boolean;
    scopes: string[],
    sandbox: string;
    tenant: string;
}

export interface GetAuthorizationResponse {
    realm: string;
    objectGroups: ObjectGroups[];
    actions: Action[];
    headers: Object;
}

export interface Action {
    tenantID: number;
    sandboxID: number;
    objectType: string;
    action: string;
    headers: Object
}

export interface ObjectGroups {
    tenantID: number;
    sandboxID: number;
    objectType: string;
    objectGroupID: string;
    description: string;
    rule: {
        functionLanguage: string;
        function: string;
    },
    status: string;
    createdAt: string;
    modifiedAt: string;
    headers: Object;
}
export interface SessionLoginActivityRequest {
    tenantId: string;
    domainId: string;
    authProfileId: string;
    resourceId: string;
}

export interface GetSessionLoginActivityResponse {
    tenantId: string;
    domainId: string;
    sandboxId: string;
    authProfileId: string;
    clientId: string;
    clientName: string;
    clientAttributes: ClientAttributes;
    resourceJid: string;
    resourceInfo: ResourceInfo;
    webSessionId: string;
    profileIdentifierUsed: ProfileIdentifierUsed;
    userAgentInfo: UserAgentInfo;
    state: string;
    scopes: string[];
    activityTimeStamp: number;
}

interface ClientAttributes {
    PKCEEnabled: string;
    displayImageURL: string;
    isThirdPartyClient: string;
}

interface ResourceInfo {
    status: string;
    logoutReason: string;
    modifiedAt: number;
}

interface ProfileIdentifierUsed {
    identityType: string;
    identityValue: string;
}

interface UserAgentInfo {
    ipv4: string;
    userAgent: string;
    userAgentId: string;
}
