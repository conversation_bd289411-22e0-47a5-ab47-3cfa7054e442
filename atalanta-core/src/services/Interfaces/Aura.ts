export interface GetCoaListItem {
    calendarID: string;
    code: string;
    currency: string;
    description: string;
    id: string;
    name: string;
    rootNodeID: number;
    status: string;
    tenantID: string;
}

export interface GetCoaListResponse extends Array<GetCoaListItem> {}

export interface GetPhaseTriggerStatusResponse {
    coaID: string;
    periodID: string;
    status: STATUS;
    trigger: string;
    tenantID: string;
    entityTrackersProgress: TrackerProgress;
    transitionMap: TransitionMapObject;
}

export interface TrackerProgress
    extends Array<{
        coaID: string;
        periodID: string;
        status: STATUS;
        entityType: string;
        tenantID: string;
        trigger: string;
        transitionMap: TransitionMapObject;
    }> {}

export type STATUS = 'COMPLETED' | 'INITIATED' | 'TRACKERS_NOTIFIED' | 'TRACKERS_ACKED' | 'FAILED' | 'SUCCESS';

export type TransitionMapObject = {
    [key in STATUS]: {
        startTime: number;
        endTime?: number;
    };
};

export interface OrchestraRunResponse {
    pagination: PaginationObject;
    orchestraRuns: OrchestraRunsObject[];
}

export interface OrchestraRunsObject {
    id: string;
    executionID: number;
    phase: string;
    jobSpecID: string;
    dagName: string;
    status: string;
    createdAt: string;
    updatedAt: string;
    startedAt: string;
    finishedAt: string;
    timeTaken: string;
    workerRuns: WorkerObject[];
    sequenceNumber: number;
    hideActionColumn: boolean;
}

export type PaginationObject = {
    pageID: number;
    pageSize: number;
    pagesCount: number;
    totalCount: number;
};

export interface WorkerObject {
    runID: string;
    executionID: string;
    workerID: string;
    status: string;
    entityType: string;
    createdAt: string;
    updatedAt: string;
    entityCount: EntityObject;
    period: PeriodInfoObject;
    workerSuccessExecutionID: string;
}

export type EntityObject = {
    total: number;
    success: number;
    failure: number;
};

export type PeriodInfoObject = {
    periodID: string;
    periodStartTime: number;
    nextPeriodStartTime: number;
    endCbuSequenceNumber: number;
    periodSequenceNumber: number;
    startCbuSequenceNumber: number;
    startDate: string;
    endDate: string;
};

export type TriggerStatusRequestObj = {
    tenantId: string;
    coaId: string;
    periodId: string;
    triggerValue: string;
    token: string;
};

export type OrchestraRunRequestObj = {
    tenantId: string;
    coaId: string;
    token?: string;
    phaseId: string;
    includeWorkerRuns: string;
    cbuId: string;
    jobId?: string;
    pageId?: string;
    pageSize?: string;
    sortBy?: string;
    sortOrder?: string;
};

export type TrackerInfo = {
    startTime: number;
    endTime: number;
    status: string;
};

export interface GenerateStatementsRequest {
    tenantID: string | number;
    coaID: string;
    ledgerID: string;
    cycleID: string;
    periodID: string;
}

export interface GenerateStatementsRequestByStatementId {
    tenantID: string;
    statementID: string;
}

export interface GenerateStatementsResponse {
    id: string;
    state: string;
    stateCode: number;
    tenantId: string | number;
    coaID: string;
    ledgerID: string;
    cycleID: string;
    periodID: string;
    periodStartDateEpoch: number;
    periodEndDateEpoch: number;
    recepientInfo: recepientInfoModel;
    storeInfo: storeInfoModel;
    auditInfo: auditInfoModel;
    policyInfo: policyInfoModel;
}

export interface recepientInfoModel {
    recepientList: recepientListModel[];
}

export interface storeInfoModel {
    statementPDFStoredLocation: string;
    statementPDFPasswordLessStoredLocation?: string;
    statementJSONStoredLocation?: string;
}

export interface auditInfoModel {
    auditRecordList: auditRecordListModel[];
}

export interface policyInfoModel {
    productID: number;
    policyCode: string;
    policyType: string;
    templateInfo: templateInfoModel;
}

export interface recepientListModel {
    email: string;
    username: string;
    accountHolderID: string;
}

export interface auditRecordListModel {
    date: number;
    currentState: string;
    statementGenerationAsterActivationId?: string;
}

export interface recepientInfoModel {
    recepientList: recepientListModel[];
}
export interface templateInfoModel {
    templateStoredLocation: string;
}

export type SORT_BY_TYPE = 'created_at';
export type SORT_ORDER = 'desc' | 'asc';

export interface PageSortParams {
    sortBy?: SORT_BY_TYPE;
    sortOrder?: SORT_ORDER;
    pageNo?: number;
    pageSize?: number;
}

export interface GetDeclinedTransactionsParams extends PageSortParams {
    tenantId: string;
    coaId: string;
    before: string;
    after: string;
    transactionID?: string;
    ledgerID?: string;
    policyID?: string;
}

export interface GetDeclinedTransactionsResponse {
    policyViolations: PolicyViolation[];
    ifiID: string;
    coaID: string;
    count: number;
    headers?: {
        [k: string]: unknown;
    };
}

export interface PolicyViolation {
    ledgerID: string;
    transactionID: string;
    policyID: string;
    policyVersion: string;
    ruleViolationInfo: RuleViolationInfo[];
    policyViolationAction: string;
    posting: PolicyViolationPosting;
}

export interface RuleViolationInfo {
    version: string;
    limit_id: string | number;
    policy_id: string;
    rule_type: string;
    legacy_error_code: string;
    policy_violation_action: string;
    errorCode?: string;
    error_code?: string;
}

export interface PolicyViolationPosting {
    ledgerID: string;
    transactionID: string;
    ledgerRecordType: string;
    postingID: string;
    previousBalance: number;
    newBalance: number;
    timestamp: number;
    amount: number;
    currency: string;
    ledgerAttributes: {
        [k: string]: unknown;
    };
    postingAttributes: {
        [k: string]: string | number;
    };
    remarks?: string;
}

export interface DeclinedTransactionsModel {
    transactionId: string;
    ledgerId: string;
    transactionDate: string;
    amount: number;
    currency: string;
    bookDate?: string;
    valueDate?: string;
    voucherCode: string;
    errorCode?: string;
    postingType?: string;
    policyId?: string;
    policyCode?: string;
    postingCategories?: string[];
    balance?: number;
}

export interface CalendarItem {
    id: string;
    tenantID: string;
    code: string;
    name: string;
    timezone: string;
    status: string;
    weekStartOffset: number;
    yearStartDate: string;
}
export interface CalendarDetails extends CalendarItem {
    phase: string;
    cbuID?: string;
    currentBookDate: number;
    sequenceNumber: number;
    summary?: {
        coaCount: StatusSummaryObject;
        workerCount: StatusSummaryObject;
        entityCount: StatusSummaryObject;
    };
}

export interface CalendarSummary {
    calendarDetails: CalendarDetails;
    coaList: any;
}

export type calendarStatus = 'failed' | 'success';

export type StatusSummaryObject = {
    [key: string]: number;
};

export interface WorkerSummary {
    workerName: string;
    status: string;
    totalEntityCount: number;
    successEntityCount: number;
    failedEntityCount: number;
    executionID: string;
}
export type WorkerSummaryObject = {
    [key: string]: WorkerSummary[];
};
export interface COAItemWithWorker extends GetCoaListItem {
    phaseStatus: string;
    startTime: number;
    endTime: number;
    timeTaken: string;
    orchestraRunInfo: {
        createdAt: string;
        updatedAt: string;
        startedAt: string;
        finishedAt: string;
        jobID: string;
        executionID: number;
        timeTaken: string;
    };
    worker: WorkerSummaryObject;
}

export interface COAItemWithWorkerResponse extends Array<COAItemWithWorker> {}

export interface SkipLegdersRequest {
    tenantId: string;
    coaId: string;
    periodId: string;
    phase: string;
    ledgerId: string;
    worker?: string;
    token: string;
}

export interface SkipLedgersPayload {
    status?: string;
    reason?: string;
    reasonCode?: string;
}
export interface EntitiesRequestParams {
    tenantId: string;
    coaId: string;
    periodId: string;
    phaseId: string;
    entityType: string;
    workerId: string;
    token: string;
}

export interface EntitiesQueryParams {
    pageID: number;
    pageSize: number;
    sortOrder: string;
    sortBy: string;
    status?: string;
}

export interface EntitiesModel {
    entityID: string;
    workerID: string;
    entityType: string;
    status: string;
    createdAt: string;
    updatedAt: string;
}

export interface EntitiesResponse {
    jobID: string;
    workerID: string;
    periodID: string;
    entities: EntitiesModel[];
    pagination: {
        pageID: number;
        pageSize: number;
        pagesCount: number;
        totalCount: number;
    };
}

export interface EntitiesRequestParams {
    tenantId: string;
    coaId: string;
    periodId: string;
    phaseId: string;
    entityType: string;
    workerId: string;
    token: string;
}

export interface CoaSummaryRequestParams {
    tenantId: string;
    coaId: string;
    startDate: string;
    cycleId: string;
    coaCode: string;
}

export interface PolciesRequestParams {
    tenantId: string;
    type: string;
    tagUri: string;
    pageNo: number;
    pageSize: number;
    token: string;
}

export interface FeeProgramsRequestParams {
    tenantId: string;
    programCode: string;
}
export interface CoaVoucherCodeSummary {
    ifi_id: string;
    coa_id: number;
    node_id: number;
    parent_node_id: number;
    voucher_code: string;
    node_depth: number;
    cycle_id: number;
    cycle_code: string;
    start_date: string;
    end_date: string;
    periodicity: string;
    period_sequence_number: number;
    record_type: string;
    amount: string;
    created_at: string;
    updated_at: string;
}

export interface CoaBalanceSummary {
    ifi_id: string;
    coa_id: number;
    node_id: number;
    parent_node_id: number;
    type: string;
    code: string;
    node_depth: number;
    cycle_id: number;
    cycle_code: string;
    start_date: string;
    end_date: string;
    period_sequence_number: number;
    opening_balance: number;
    closing_balance: number;
    periodicity: string;
    created_at: string;
    updated_at: string;
}
export interface CoaPostingRecordTypeSummary {
    ifi_id: string;
    coa_id: number;
    node_id: number;
    parent_node_id: number;
    type: string;
    code: string;
    node_depth: number;
    cycle_id: number;
    cycle_code: string;
    start_date: string;
    end_date: string;
    period_sequence_number: number;
    opening_balance: number;
    closing_balance: number;
    periodicity: string;
    created_at: string;
    updated_at: string;
    credit_amount: number;
    credit_count: number;
    debit_amount: number;
    debit_count: number;
}
export interface SkipEntityResponse {
    jobID?: string;
    periodID?: string;
    workerID?: string;
    entityID?: string;
    entityType?: string;
    status?: string;
    reason?: string;
    reasonCode?: string;
    updatedAt?: string;
}
