export interface GetAuditEventDefinitionListResponse {
    auditEventDefinitions: AuditEventDefinitions[];
    pageMetadata: {
        number?: number;
        size?: number;
        totalElements: number;
        totalPages?: number;
        hasNext?: boolean;
    }
}
export interface AuditEventDefinitions {
    tenantId: string;
    businessEntityName: string;
    eventName: string;
    isAuditingEnabled: boolean;
    auditAvailableFrom: EpochTimeStamp;
    metadata: {
        key: {
            [k: string]: any;
        }
    },
    createdAt: EpochTimeStamp;
    updatedAt: EpochTimeStamp;
    updatedBy: string;
};

export interface GetAuditEntityDefinitionListResponse {
    auditEntityDefinitions: AuditEntityDefinitions[];
    pageMetadata: {
        number?: number;
        size?: number;
        totalElements: number;
        totalPages?: number;
        hasNext?: boolean;
    }
}
export interface AuditEntityDefinitions {
    id: string;
    tenantId: string;
    businessEntityName: string;
    displayBusinessEntityName: string;
    metadata: {
        key: {
            [k: string]: any;
        }
    },
    createdAt: EpochTimeStamp;
    updatedAt: EpochTimeStamp;
    updatedBy: string;
};
