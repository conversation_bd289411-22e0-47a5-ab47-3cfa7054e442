import { CerberusIdentityType } from './AddIdentityRequest';

export interface GetAuthProfileRequest {
    domainId: string;
    identityType: CerberusIdentityType;
    identityValue: string;
}
export interface GetAuthProfileParams {
    domainId: string;
    authProfileId: string;
}
export interface Identity {
    identityId: number;
    identityType: string;
    createdAt: number;
    identityValue: string;
    status: string;
}

export interface Attributes {}

export interface AuthProfile {
    authProfileId: string;
    name: string;
    identities: Identity[];
    roles: string[];
    scopes: string[];
    credentials: any[];
    createdAt: number;
    isDirty: boolean;
    status: string;
    attributes: Attributes;
}

export interface Page {
    number: number;
    size: number;
    totalElements: number;
    totalPages: number;
    hasNext: boolean;
}

export interface Headers {}

export interface GetAuthProfileResponse {
    authProfiles: AuthProfile[];
    page: Page;
    headers: Headers;
}
