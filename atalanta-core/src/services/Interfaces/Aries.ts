interface Address {
    city: string;
    line1: string;
    line2: string;
    line3: string;
    state: string;
    country: string;
    lastName: string;
    firstName: string;
    postalCode: string;
}

interface Addresses {
    type: string;
    value: Address | string;
    isCommunicationAllowed: boolean;
    attributes?: {
        [k: string]: string;
    };
    headers?: {
        [k: string]: string;
    };
}

interface AlternateContactDetails {
    firstName: string;
    lastName: string;
    addresses: Addresses[];
}
interface POPContent {
    id: string;
    ifiID: string;
    accountHolderId: string;
    addresses: Addresses[];
    alternateContactDetails: AlternateContactDetails[];
    label: string;
    isDefault: boolean;
    createdAt: string;
    updatedAt: string;
    createdBy: string;
    updatedBy: string;
}

interface KYCStatus {
    ifiID: string;
    accountHolderID: string;
    kycStatus: string;
    updateTime: string;
    expiryTime: string;
    standardUpdateTime: string;
    standardExpiryTime: string;
    attributes: {
        kycType: string;
        aadhaar?: string;
        authType?: string;
    };
    createdBy: string;
    updatedBy: string;
}

interface Vectors {
    id: string;
    accountHolderID: string;
    ifiID: string;
    status: string;
    type: string;
    value: string;
    verified: boolean;
    createdAt: string;
    updatedAt: string;
    createdBy: string;
    updatedBy: string;
}

interface Tags {
    type: string;
    value: string;
    attributes: {
        [k: string]: string;
    }
}

interface Contact {
    attributes: object;
    firstName: string;
    lastName: string;
    vectors: Vectors[];
}

interface POP {
    address: Address;
    attributes?: { [k: string]: unknown };
    contactList: Contact[];
    createdAt: string;
    isDefault: boolean;
    entityID: string;
    entityType: string;
    headers: object;
    id: string;
    ifiID: number;
    label: string;
    modifiedAt: string;
    status: string;
}

export interface GetAccountHolderResponseV4 {
    requestID: string;
    id: string;
    ifiID: string;
    accountHolderProviderID: string;
    vectors: Vectors[];
    type: string;
    status: string;
    KYCStatus: KYCStatus;
    salutation: string;
    firstName: string;
    middleName: string;
    lastName: string;
    dob: string;
    gender: string;
    pointsOfPresence: POPContent[];
    tags: Tags[];
    createdAt: string;
    updatedAt: string;
    createdBy: string;
    updatedBy: string;
};

export interface GetAccountHolderResponse {
    requestID: string;
    id: string;
    ifiID: string;
    accountHolderProviderID: string;
    vectors: Vectors[];
    type: string;
    status: string;
    KYCStatus: KYCStatus;
    salutation: string;
    firstName: string;
    middleName: string;
    lastName: string;
    dob: string;
    gender: string;
    pops: POP[];
    tags: Tags[];
    createdAt: string;
    updatedAt: string;
    createdBy: string;
    updatedBy: string;
};


export interface GetAccountHolderRequest {
    individualId: string;
    tenantId: string;
    token: string;
};
