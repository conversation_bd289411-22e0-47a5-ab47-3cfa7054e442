export interface EurusGetModelCodeRequest{
    tenantId:string;
    modelCode:string;
    token:string;
}


export interface EurusGetModleCodeResponse{
    code:string;
    name:string;
    description:string;
    attributes: Attributes;
    id:string;
    objectType:string;
}

export interface EurusGetModelUnitRequest{
    tenantId:string;
    classificationModelId:string;
    objectType:string;
    token:string;
}

export interface EurusGetModleUnitResponse{
    modelType:string;
    objectType:string;
    code:string;
    isRuleBased:boolean;
    attributes: Attributes;
}

export interface EurusGetModleUnitsResponse{
    classificationUnits : [EurusGetModleUnitResponse]
}

export interface Attributes{
    [key: string]: string;
}