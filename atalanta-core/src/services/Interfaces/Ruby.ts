export interface GetAllCreditAccountRequest {
    token: string;
    tenantID: string;
    accountHolderID?: string;
    accountNumber?: string;
}
export interface CreditAccountDetails {
    requestID: string;
    tenantID: string | number;
    productID: string;
    id: string;
    accountHolderID: string;
    name: string;
    currency: string;
    coaID?: string;
    ledgerID?: string;
    creditLimit?: number;
    sanctionedLimit?: number;
    openDate?: string;
    attributes?: {
        currency?: string;
        [index: string]: any;
    };
    accountNumber: string;
}
