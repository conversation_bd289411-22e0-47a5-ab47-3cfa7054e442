export interface GetRequiredPostingsRequest extends GetPostingsForRecordTypeRequest{
    recordTypes?: string[];
}


export interface GetPostingsForRecordTypeRequest{
    tenantID: string;
    coaID: string;
    ledgerID: string;
    fromDateTime?: number;
    toDateTime?: number;
    recordType?: string;
    clockType?: string;
    cycleID?: number;
    periodStartSequenceNumber?: number;
    periodEndSequenceNumber?: number;
    transactionIDs ?: string[];
    token: string;
}


