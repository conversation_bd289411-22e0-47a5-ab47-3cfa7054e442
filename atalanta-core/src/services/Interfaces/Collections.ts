export interface SetCollectionItemPaylod {
    itemID: string;
    collectionID: string;
    data: string;
    to: string;
}

export interface DeleteCollectionItemPaylod {
    itemID: string;
    to: string;
}

export type PERM_TYPE = 'm' | 'w' | 'r' | 'a';

export interface SetPermissionsPayload {
    namespaceID: string;
    permissionRecords: Array<{ patterns: string[]; perms: PERM_TYPE[] }>;
}

export interface GetPermissionsPayload {
    namespaceID: string;
    pattern: string;
}

export interface GetPermissionsResponse {
    permissions: PERM_TYPE[];
    headers: string[];
}

export interface AuthResponse {
    valid: boolean;
    tenantId?: string;
    userJID?: string;
    authProfileId?: string;
    error?: any;
    sandboxId?: string;
}

export interface DeleteViewPayload {
    itemID?: string;
    isPublic?: boolean;
}

export interface SaveViewPayload {
    data: any;
    itemID?: string;
    isPublic?: boolean;
    isNewView?: boolean;
}

export interface GetViewsPayload {
    count: string | number;
    includeDeletedItems: boolean;
    includePublicViews?: boolean;
}

export interface GetViewsCollectionResponse {
    items: CollectionItem[];
    headers?: { [key: string]: string };
}

export interface GetViewsCollectionFragmentResponse {
    fragments: CollectionItem[];
    headers?: { [key: string]: string };
}

export interface CollectionItem {
    isDeleted: boolean;
    itemID: string;
    data: string;
    createdAt: string;
    updatedAt: string;
    headers?: { [key: string]: string };
}

export interface GetLatestCollectionItemsPayload {
    count: string | number;
    includeDeletedItems: boolean;
    to: string;
    collectionID?: string;
    fromCollectionVersion?: null | string;
}

export interface GetCollectionItemPayload {
    itemID: string;
    collectionID: string;
}
export interface GetNameSpaceIDParameter {
    applicationName: string;
    tenantId: string;
    sandboxId: string;
    authProfileId: string;
    isPublic: boolean;
}

export interface GetSingleViewPayload {
    itemID: string;
    isPublic: boolean;
}
