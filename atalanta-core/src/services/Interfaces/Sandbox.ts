import { Headers } from './common';
export interface SandboxAddRoleToSubjectRequest {
    tenantID: string;
    sandboxID: string;
    realmID: string;
    subjectID: string;
    roleName: string;
}

export interface SandboxGetRolesOfSubjectRequest {
    tenantID: string;
    sandboxID: string;
    realmID: string;
    subjectID: string;
}

export interface Role {
    sandboxID: number;
    tenantID: number;
    roleName: string;
    description: string;
    status: string;
    modules: any[];
    createdAt: string;
    modifiedAt: string;
    headers: Headers;
}

export interface SandboxGetRolesOfSubjectResponse {
    roles: Role[];
    headers: Headers;
}

export interface RoleEntry {
    tenantId: number;
    sandboxId: number;
    objectType: string;
    roleEntryID: string;
    description: string;
    status: string;
    createdAt: string;
    modifiedAt: string;
    headers: Headers;
}
export interface RoleEntryRes {
    data: {
        roleEntries: RoleEntry[];
        headers: Headers;
    };
}
export interface Rule {
    functionLanguage: string;
    function: string;
}
export interface ActionGroup {
    tenantId: number;
    sandboxId: number;
    objectType: string;
    actionGroup: string;
    description: string;
    status: string;
    createdAt: string;
    modifiedAt: string;
    headers: Headers;
}
export interface ActionGroupRes {
    objectType: string;
    data: {
        actionGroups: ActionGroup[];
        headers: Headers;
    };
}
export interface Action {
    tenantId: number;
    sandboxId: number;
    objectType: string;
    action: string;
    description: string;
    status: string;
    createdAt: string;
    modifiedAt: string;
    headers: Headers;
}
export interface ActionRes {
    objectType: string;
    actionGroup: string;
    data: {
        actions: Action[];
        headers: Headers;
    };
}
