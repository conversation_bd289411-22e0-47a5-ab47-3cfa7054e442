export interface ParentCoa {
    name: string;
    code: string;
    description: string;
    status: string;
    ledgers: ParentCoaLedger[];
}
export interface ChildCoaListResponse {
    childCoAList: ChildCoa[];
    hasNextPage: boolean;
}
export interface ChildCoaListRequest {
    tenantId: string;
    parentCoaCode: string;
}
export interface ParentCoaLedger {
    accountName: string;
    accountNo: string;
    accountType: string;
    tags: string[];
}
export const metric = ['DEBIT', 'CREDIT', 'NET'] as const;
export const stage = ['DRAFT', 'PUBLISHED', 'ARCHIVED'] as const;
export const status = ['ACTIVE', 'INACTIVE', 'OUT_OF_SYNC'] as const;
export const nodeType = ['COA_NODE', 'PSEUDO_LEDGER', 'SYSTEM_LEDGER', 'POSTING_CATEGORY'] as const;
export interface CoaConnection {
    id: string;
    stage: (typeof stage)[number];
    status: (typeof status)[number];
    version: string;
}
export interface CoaConnectionDetails extends CoaConnection {
    connections: CoaConnectionsDetail[];
    createAt: string;
}
export interface GlHandoffRunsResponse {
    glHandoffRuns: GlHandoffRun[];
    hasNextPage: boolean;
}
interface ChildCoaComponent {
    id: string;
    name: string;
    nodeType: (typeof nodeType)[number];
}
interface CoaConnectionsDetail {
    childCoaComponent: ChildCoaComponent;
    connectionMap: ConnectionMap[];
    children: string[];
}
interface GlHandoffRun {
    glHandoffRunId: string;
    bookDate: string;
    generationStatus: string;
    deliveryStatus: string;
    parentCoaCode: string;
    parentCoaName: string;
    parentCoaConnectorVersion: number;
    createdAt: string;
    updatedAt: string;
}
interface ConnectionMap {
    metric: (typeof metric)[number];
    parentCoaLedger: {
        accountNo: string;
        accountName: string;
    };
}
interface ConnectionMap {
    nodeId: string;
    parentCoALedgerAccountNo: string;
    metric: (typeof metric)[number];
    metaData: string;
}

interface ChildCoa {
    connectorStage: string;
    status: string;
    connectorId: string;
    childCoaName: string;
    childCoaVersion?: string;
    childCoaId: string;
}
export interface CreateCoaConntectionRequest {
    connections: ConnectionMap[];
}
export interface UpdateCoaConntectionMapRequest extends CreateCoaConntectionRequest {
    stage: (typeof stage)[number];
    status: (typeof status)[number];
    requestType: string;
    communicationRecipients: {
        communicationIdentifierType: 'EMAIL';
        communicationIdentifierId: string;
    }[];
}

export interface CoaSummaryGoldenSchemaPayload {
    rowsTabEventPayload: RowsTabEventPayload;
    basicParametersEventPayload: BasicParametersEventPayload;
    columnsTabEventPayload: ColumnsTabEventPayload;
    systemLedgers?: string[];
}
export interface RowsTabEventPayload {
    coaNodes: string[];
    postingCategories: string[];
    voucherCodes: string[];
    balanceCodes: BalanceCodes;
}

export interface BasicParametersEventPayload {
    clockId: string;
    cycleId: string;
    periodsData: {
        sequenceNumber: number;
        startTime: number;
        nextPeriodStartTime: number;
    }[];
    cycleList?: {
        cycleId: string;
        periodsData: {
            sequenceNumber: number;
            startTime: number;
            nextPeriodStartTime: number;
        }[];
    }[];
}
export interface ColumnsTabEventPayload {
    balanceCodes: BalanceCodes;
}
export interface BalanceCodes {
    [key: string]: Partial<['min', 'max', 'avg', 'sum']>;
}
export interface CoaSummaryGoldenSchemaResponse {
    ifi_id: number;
    coa_id: string;
    node_id: string;
    parent_node_id: string;
    node_depth: number;
    cycle_id: string;
    period_sequence_number: number;
    start_time: string;
    next_period_start_time: string;
    periodicity: string;
    voucher_code: string | null;
    posting_category_code: string | null;
    balance_code: string | null;
    system_ledger_id: number | null;
    aggregation: string;
    opening_balance: number | null;
    closing_balance: number | null;
    minimum_balance: number | null;
    maximum_balance: number | null;
    average_balance: number | null;
    current_balance: number | null;
    debits_value: number | null;
    debits_count: number | null;
    debits_reversed_value: number | null;
    debits_reversed_count: number | null;
    debits_reversed_uncaptured_value: number | null;
    debits_reversed_uncaptured_count: number | null;
    credits_value: number | null;
    credits_count: number | null;
    credits_reversed_value: number | null;
    credits_reversed_count: number | null;
    credits_reversed_uncaptured_value: number | null;
    credits_reversed_uncaptured_count: number | null;
    forced_debits_value: number | null;
    forced_debits_count: number | null;
    forced_debits_reversed_value: number | null;
    forced_debits_reversed_count: number | null;
    forced_debits_reversed_uncaptured_value: number | null;
    forced_debits_reversed_uncaptured_count: number | null;
    forced_credits_value: number | null;
    forced_credits_count: number | null;
    forced_credits_reversed_value: number | null;
    forced_credits_reversed_count: number | null;
    forced_credits_reversed_uncaptured_value: number | null;
    forced_credits_reversed_uncaptured_count: number | null;
    created_at: string;
    updated_at: string;
    _sdc_extracted_at: string;
    _sdc_deleted_at: null;
    _sdc_batched_at: string;
    _sdc_received_at: string;
    _sdc_sequence: number;
    _sdc_table_version: null;
    _sdc_sync_started_at: number;
}
export interface UpdateConnectorStatus {
    requestType: string;
    status: string;
}

export interface ConnectionDetails {
    id: string;
    tenantId: string;
    parentCoaId: string;
    childCoaId: string;
    childCoaCode: string;
    connections: ParentCoaMap[];
    childCoaTree: ChildCoaNode[];
    connectorStage: string;
    status: string;
    activeDate: string;
    connectorNotification: {
        communicationIdentifierType: string;
        communicationIdentifierId: string;
    }[];
    version: string;
    createdAt: string;
    updatedAt: string;
}
export interface ParentCoaMap {
    childCoaComponentId: string;
    connections: {
        parentCoALedgerAccountNo: string;
        metric: (typeof metric)[number];
        metaData: string;
    }[];
}
export interface ChildCoaNode {
    id: string;
    nodeName: string;
    nodeType: string;
    parentNodeId: string;
    code: string;
}
