export interface DocumentRequestParams {
    tenantId?: string;
    dbName: string;
    schemaName?: string;
    documentName?: string;
    parentDir?: string;
    docId?: string;
}

export interface DocumentRequestBody {
    documentsInfoBase64: any;
    commitInfo: unknown;
    tags: [];
}

export interface DocumentResponse {
    documentContent: unknown;
    format: string;
}

export interface CheckStatusRequestParams {
    tenantId: string;
    subscriptionId: string;
    commitId: string;
}

export interface CheckStatusResponse {
    provisionId: string;
    stage: string;
    createdAt: number;
    updatedAt: number;
    subscriptionId: string;
    saasProductId: number;
    moduleIdToGodControllerStatusMap: unknown;
}
