export declare namespace OperationsTypes {

    interface BaseParams {
        token: string;
    }

    interface UserTaskRequest extends BaseParams {
        tenantId: string;
        catalogIdList?: Array<number | string>;
        workbenchIdList?: Array<number | string>;
        businessKeyList?: Array<string>;
        authProfileIdList?: Array<string>;
        includeUnassigned?: boolean;
        includeAssigned?: boolean;
        businessKeyStatusList?: Array<number | string>;
        taskStatusList?: Array<string>;
        taskCreationDateRange?: {
            start: string;
            end: string;
        };
        taskDueRange?: {
            start: string;
            end: string;
        };
        pageNumber?: number;
        pageSize?: number;
        orderBy?: string;
        orderType?: string;
    }

    interface UserTaskInformation {
        processInstanceId: string;
        businessKey: string;
        businessKeyStatus: number;
        catalogId: number;
        workbenchId: number;
        workflowId: string;
        workflowName: string;
        taskId: string;
        taskName: string;
        taskStatus: string;
        taskCreatedAt: number;
        taskCompletedAt: number;
        createdAt: number;
        updatedAt: number;
        assigneeName?: string;
        assigneePhone?: string;
        assigneeEmail?: string;
    }

    interface UserTaskInformationResponse {
        tenantId: string;
        pageNumber: number;
        pageSize: number;
        totalTaskCount: number;
        userTaskInformationList: Array<UserTaskInformation>;
    }

    interface GetFormVariablesRequest extends BaseParams {
        tenantId: string;
        taskId: string;
        catalogId: number;
        workbenchId: number;
        getCompletedTasks?: boolean;
        formFieldsId?: string;
    }

    interface GetFormVariablesResponse {
        [key: string]: {
            value: any;
            type: string;
            valueInfo?: Object;
        };
    }
}
