export interface GetSignedUrlResponse {
    links: Link[];
    id: string;
    definitionId: string;
    ended: boolean;
    suspended: boolean;
    tenantId: string;
    variables: FormVariables;
}

export interface GetSignedUrlRequest {
    tenantId: string;
    workflowKey: string;
    variables: FormVariables;
    withVariablesInReturn: boolean;
}

export interface FormVariables {
    [key: string]: VariableData;
}

interface VariableData {
    value: string;
    type: string;
}

interface Link {
    method: string;
    href: string;
    rel: string;
}

export interface GetTaskFormVariablesRequest {
    tenantId: string;
    taskId: string;
}
