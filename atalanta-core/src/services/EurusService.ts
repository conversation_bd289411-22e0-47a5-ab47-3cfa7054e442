import { Request } from "express"; 
import { BaseService } from './BaseService';
import { InternalAPIException } from '../exceptions/InternalAPIException';
import { createErrorObj } from '../utils';
import { EurusGetModelCodeRequest, EurusGetModelUnitRequest, 
    EurusGetModleCodeResponse, EurusGetModleUnitsResponse
 } from './Interfaces/EurusRequest';


export class EurusService extends BaseService {


    constructor(req: Request) {
        super(req, {
            baseURL: process.env.EURUS_SERVICE_URL,
            headers: {
            },
        });
    };


    public async getClassificationModel(requestParams : EurusGetModelCodeRequest):Promise<EurusGetModleCodeResponse> {
        const { tenantId, modelCode , token} = requestParams;
        try{
            this.req.getLogger(__filename).info('Getting Eurus Model Code', requestParams);
            const response = await this.axios.get(`/tenants/${tenantId}/classification-models/code/${modelCode}`, {
                headers: {
                    'Authorization':  `${token}`
                }
            });
            return response.data as EurusGetModleCodeResponse

        }catch(error){
            this.req.getLogger(__filename).error('Error', { error });
            throw new InternalAPIException({
                api: 'getClassificationModel',
                error: createErrorObj(error)
            });
        }
    }

    public async getClassficationUnits(requestParams : EurusGetModelUnitRequest) : Promise<EurusGetModleUnitsResponse>{
        const { tenantId, objectType , classificationModelId, token } = requestParams;
        try{
            this.req.getLogger(__filename).info('Getting Eurus Model Units', requestParams)
            const response = await this.axios.get(`/tenants/${tenantId}/classification-units?objectType=${objectType}&classificationModelId=${classificationModelId}`, {
                headers: {
                    'Authorization':  `${token}`
                }
            });
            return response.data as EurusGetModleUnitsResponse

        }catch(error){
            this.req.getLogger(__filename).error('Error', { error });
            throw new InternalAPIException({
                api: 'getClassficationUnits',
                error: createErrorObj(error)
            });
 
        }

    }


    


}
