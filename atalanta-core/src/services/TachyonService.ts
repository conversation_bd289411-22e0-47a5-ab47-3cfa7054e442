import { GetCoaListResponse } from './Interfaces/Aura';
import JSO<PERSON><PERSON>igIntMod from 'json-bigint';
import { Request } from 'express';
import { BaseService } from './BaseService';
import { InternalAPIException } from '../exceptions/InternalAPIException';
import {
    GetCyclesForCalendarRequest,
    GetClocksByTypeForCalenderIdRequest,
    GetClocksByTypeForCalenderIdResponse,
    GetCyclesForCalendarResponse,
    GetPeriodsRequest,
    GetPeriodsResponse,
    GetCoaTreeRequest,
    GetCoaTreeResponse,
    StatusVerificationReportsTypes,
} from './Interfaces/Tachyon';
import { createErrorObj } from './../utils';

const JSONBigInt = JSONBigIntMod({ storeAsString: true });

export class TachyonService extends BaseService {
    constructor(req: Request) {
        super(req, {
            baseURL: process.env.TACHYON_URL,
            headers: {
                'Content-Type': 'application/json',
            },
            transformRequest: (data: any) => (data ? JSONBigInt.parse(data) : data),
            transformResponse: (data: any) => (data ? JSONBigInt.parse(data) : data),
        });
    }

    public async getCoaList(tenantId: string, token: string): Promise<GetCoaListResponse> {
        try {
            this.req.getLogger(__filename).info('Fetching coa list');
            const response = await this.axios.get(`/coa/tenants/${tenantId}/coas`, {
                headers: { ['authorization']: token },
            });
            return response.data as GetCoaListResponse;
        } catch (error) {
            this.req.getLogger(__filename).error(error);
            throw new InternalAPIException({
                api: 'getCoaList',
                error: createErrorObj(error)
            });
        }
    }

    public async getCoaTree(requestParams: GetCoaTreeRequest): Promise<GetCoaTreeResponse> {
        try {
            const { tenantId, coaId, token, ...params } = requestParams;
            this.req.getLogger(__filename).info(`Fetching coaTree`);
            const response = await this.axios.get(`coa/tenants/${tenantId}/coas/${coaId}/coaTree`, {
                headers: { ['authorization']: token },
                params,
            });
            return response.data;
        } catch (error) {
            this.req.getLogger(__filename).error(error);
            throw new InternalAPIException({
                api: 'getCoaTree',
                error: createErrorObj(error)
            });
        }
    }

    public async getCurrentCBUData(requestParams: any): Promise<any> {
        try {
            const { tenantId, calendarId, token } = requestParams;
            this.req.getLogger(__filename).info('Fetching current cbu data');
            const response = await this.axios.get(
                '/calendar/tenants/' + tenantId + '/calendars/' + calendarId + '/currentCBU',
                {
                    headers: { ['authorization']: token },
                },
            );
            return response.data;
        } catch (error) {
            this.req.getLogger(__filename).error(error);
            throw new InternalAPIException({
                api: 'getCurrentCBUData',
                error: createErrorObj(error)
            });
        }
    }

    public async getCalendarListData(requestParams: any): Promise<any> {
        try {
            const { tenantId, token } = requestParams;
            this.req.getLogger(__filename).info('Fetching calendar list data');
            const response = await this.axios.get('/calendar/tenants/' + tenantId + '/calendars', {
                headers: { ['authorization']: token },
            });
            return response.data;
        } catch (error) {
            this.req.getLogger(__filename).error(error);
            throw new InternalAPIException({
                api: 'getCalendarListData',
                error: createErrorObj(error)
            });
        }
    }

    /**
     * Function to get all clocks belonging to a calender and type
     * @param requestParams payload containing tenantId and calendarId
     */
    public async getClockList(
        requestParams: GetClocksByTypeForCalenderIdRequest,
    ): Promise<GetClocksByTypeForCalenderIdResponse[]> {
        try {
            const { tenantId, calendarId, token, ...params } = requestParams;
            this.req.getLogger(__filename).info(`Fetching clocks for calendar: ${calendarId}`);
            const response = await this.axios.get(`/calendar/tenants/${tenantId}/calendars/${calendarId}/clocks`, {
                headers: { ['authorization']: token },
                params,
            });
            return response.data;
        } catch (error) {
            this.req.getLogger(__filename).error(error);
            throw new InternalAPIException({
                api: 'getClockList',
                error: createErrorObj(error)
            });
        }
    }

    /**
     * Function to get all cycles for a clock of a calendar
     * @param requestParams payload containing, tenantId and calendarId
     */
    public async getCycleList(requestParams: GetCyclesForCalendarRequest): Promise<GetCyclesForCalendarResponse[]> {
        try {
            const { tenantId, calendarId, clockId, token, ...params } = requestParams;
            this.req.getLogger(__filename).info(`Fetching cycles for clock: ${clockId}`);
            const response = await this.axios.get(
                `/calendar/tenants/${tenantId}/calendars/${calendarId}/clocks/${clockId}/cycles`,
                {
                    headers: { ['authorization']: token },
                    params,
                },
            );
            return response.data;
        } catch (error) {
            this.req.getLogger(__filename).error(error);
            throw new InternalAPIException({
                api: 'getCycleList',
                error: createErrorObj(error)
            });
        }
    }

    /**
     * Function to get periods for a clock and cycle combination
     * @param requestParams payload containing tenantId, calendarId, clockId, cycleId
     */
    public async getPeriodList(requestParams: GetPeriodsRequest): Promise<GetPeriodsResponse[]> {
        try {
            const { tenantId, calendarId, clockId, cycleId, token, ...params } = requestParams;
            this.req.getLogger(__filename).info(`Fetching periods for cycle: ${cycleId}`);
            const response = await this.axios.get(
                `/calendar/tenants/${tenantId}/calendars/${calendarId}/clocks/${clockId}/cycles/${cycleId}/periods`,
                {
                    headers: { ['authorization']: token },
                    params,
                },
            );
            return response.data;
        } catch (error) {
            this.req.getLogger(__filename).error(error);
            throw new InternalAPIException({
                api: 'getPeriodList',
                error: createErrorObj(error)
            });
        }
    }

    /**
     * Fetches the status verifier report for a given COA and CBU.
     * @param params - The parameters for the request.
     * @returns The status verifier report.
     */
    public async getStatusVerifierReport(requestParams: StatusVerificationReportsTypes.GetStatusVerifierReportsRequest): Promise<StatusVerificationReportsTypes.GetStatusVerifierReportsResponse> {
        try {
            const token = this.req.headers.authorization.split(' ')[1];
            const { tenantId, coaCode, cbus } = requestParams;
            const response = await this.axios.get(
                `/coa/tenants/${tenantId}/coas/${coaCode}/cbus/${cbus}/statusVerificationReports`, {
                    headers: { Authorization: `Bearer ${token}` },
                }
            );

            return response.data;
        } catch (error) {
            this.req.getLogger(__filename).error(error);
            throw new InternalAPIException({
                api: 'getStatusVerifierReport',
                error: createErrorObj(error)
            });
        }
    }
}
