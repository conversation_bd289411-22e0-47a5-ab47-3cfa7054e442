import { Request } from 'express';
import { BaseService } from './BaseService';
import { InternalAPIException } from '../exceptions/InternalAPIException';
import { createErrorObj } from './../utils';
export class IOService extends BaseService {
    constructor(req: Request) {
        super(req, {
            baseURL: process.env.IO_URL,
            headers: {
                'Content-Type': 'application/json',
            },
        })
    };

    public async getIOAccessToken()  {
        try {
            const response = await this.axios.get('/user/token', {
                headers: { Authorization: `Basic ${process.env.IO_TOKEN}` },
            });
            return response.data?.access_token;
        } catch (error) {
            this.req.getLogger(__filename).error('API getIOAccessToken Error:', error);
            throw new InternalAPIException({
                api: 'getIOAccessToken',
                error: createErrorObj(error)
            });
        }
    }
}