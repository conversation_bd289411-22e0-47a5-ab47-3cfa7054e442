import axios, { AxiosPromise } from 'axios';
import https from 'https';
import qs from 'qs';
import { Request } from 'express';
import { AddIdentityRequest } from './Interfaces/AddIdentityRequest';
import { AddRoleAuthProfile } from './Interfaces/AddRoleAuthProfile';
import { GetAuthProfileParams, GetAuthProfileRequest, GetAuthProfileResponse } from './Interfaces/Cerberus2';
import { CreateAuthProfileRequest, CreateAuthProfileResponse } from './Interfaces/CreateAuthProfile';
import { AUTHORIZATION_HEADER } from './../Constants';
import { InternalAPIException } from './../exceptions';
import { createErrorObj, extractToken } from './../utils';

const axiosInstance = () => {
    const instance = axios.create({
        baseURL: `${process.env.CERBERUS_SERVICE_BASE_URL}/cerberus2`,
        headers: {
            Authorization: `Bearer ${process.env.LTT}`,
        },
    });

    instance.interceptors.response.use((res) => {
        return res;
    });
    return instance;
};

// Create Auth Profile
export const createAuthProfile = async (
    request: CreateAuthProfileRequest,
): Promise<AxiosPromise<CreateAuthProfileResponse>> => {
    console.log('Create Auth Profile', request);
    return await axiosInstance().post(`/domains/${request.domainId}/auth_profiles/`, request);
};

export const getAuthProfile = async (request: GetAuthProfileRequest): Promise<AxiosPromise<GetAuthProfileResponse>> => {
    const { domainId, identityType, identityValue } = request;
    const url = `/domains/${domainId}/auth_profiles/?${qs.stringify({
        identityType,
        identityValue,
    })}`;
    console.log('get Auth Profile', request, url);
    return await axiosInstance().get(url);
};

/**
 * A function to return the auth profile details based on auth profile
 * Note - We are not using the getAuthProfile function (from above) as it uses an LLT  but here
 * we need to use the token from the request
 * @param request Request object from express
 * @param requestParams the params for making the endpoint
 * @returns Auth profile details
 */
export const getAuthProfileDetails = async (request: Request, requestParams: GetAuthProfileParams): Promise<AxiosPromise<GetAuthProfileResponse>> => {
    const { domainId, authProfileId } = requestParams;
    const token = extractToken(request);
    try {
        const url = `${process.env.CERBERUS_SERVICE_BASE_URL}/cerberus2/domains/${domainId}/auth_profiles/${authProfileId}`;
        const response = await axios.get(url, {
            headers: { [AUTHORIZATION_HEADER]: `Bearer ${token}` },
        });
        request.getLogger(__filename).info(`Auth profile retreived successfully for ${authProfileId}`);
        return response.data;
    } catch (error) {
        request.getLogger(__filename).error('Get Auth profile details error Error:', error);
        throw new InternalAPIException({
            api: 'authProfileDetails',
            error: createErrorObj(error)
        });
    }
};


// Add Identity to auth profile
export const addIdentity = async (request: AddIdentityRequest) => {
    console.log('Add Identity to auth profile');
    return await axiosInstance().post(
        `/domains/${request.domainId}/auth_profiles/${request.authProfileId}/identities`,
        request,
    );
};

// Add Role to auth profile
export const addRoleAuthProfile = async ({ authProfileId, domainId, role }: AddRoleAuthProfile) => {
    // @todo debug why axios failed
    const url = new URL(
        `${process.env.CERBERUS_SERVICE_BASE_URL}/cerberus2/domains/${domainId}/auth_profiles/${authProfileId}/roles/${role}`,
    );
    return new Promise((resolve, reject) => {
        const req = https.request(
            {
                method: 'PUT',
                hostname: url.hostname,
                path: url.pathname,
                headers: {
                    Authorization: `Bearer ${process.env.LTT}`,
                },
            },
            (res) => {
                let data = '';
                let err = false;
                res.on('data', (d) => {
                    data = d;
                });

                res.on('error', (e) => {
                    err = true;
                    reject(e);
                });

                res.on('end', () => {
                    if (err) reject('fail');
                    else resolve(data);
                });
            },
        );
        req.on('error', (e) => {
            reject(e);
        });
        req.write('');
        req.end();
    });
};
