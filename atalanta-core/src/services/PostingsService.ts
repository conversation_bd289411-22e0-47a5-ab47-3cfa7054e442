import { Request } from "express"; 
import { BaseService } from './BaseService';
import { InternalAPIException } from '../exceptions/InternalAPIException';
import { GetRequiredPostingsRequest , GetPostingsForRecordTypeRequest} from './Interfaces/Postings';
import { createErrorObj } from '../utils';


export class PostingsService extends BaseService {


    constructor(req: Request) {
        super(req, {
            baseURL: process.env.AURA_LEDGER_MANAGER_URL,
            headers: {
            },
        });
    };

    public async getRequiredPostingsForMerchantCredit(requestParams: GetRequiredPostingsRequest): Promise<any> {
       
       try{
        const creditPostingList = await this.getAllPostingsForRecordType({...requestParams, recordType : 'CREDIT'});
        const debitPostingList = await this.getAllPostingsForRecordType({...requestParams, recordType : 'DEBIT'});
     
        const modifiedCreditPostings = this.modifyPostingsDataForMerchantCredit(creditPostingList);
        const modifiedDebitPostings = this.modifyPostingsDataForMerchantCredit(debitPostingList);
 
        const { transactionIDs = [] } = requestParams;
         
        const reqPostingsOnFe = transactionIDs.reduce( (acc:any, txnId:string) => {
            const creditPosting = modifiedCreditPostings.filter( cp => cp.transactionID.includes(txnId) || cp.txnID.includes(txnId) )
            .filter( cp => cp.attributes["ruby.isBaseTransaction"] === "true" )[0];
            const debitPostings = modifiedDebitPostings.filter( dp =>   dp.txnID.includes(txnId) || dp.transactionID.includes(txnId))
            .filter( cp => cp.attributes["ruby.isBaseTransaction"] === "true"  )[0];
            acc[txnId] =  creditPosting ||  debitPostings;
             return acc;
         }, {});
 
         return reqPostingsOnFe;
       }catch(error){
            this.req.getLogger(__filename).error(`Error Fetching Posting for Merchant Credit`, error);
            throw new InternalAPIException({
                api: 'getClassficationUnits',
                error: createErrorObj(error)
            });
       }
       
    }

    public async getAllPostingsForRecordType(payload: GetPostingsForRecordTypeRequest): Promise<any> {
        const { tenantID, coaID, ledgerID, transactionIDs , token, ...params } = payload;      
        const pageSize = 30;
        let pageNo = 1;
        let fetchMore = true;     
        let postings:any= [];
        this.req.getLogger(__filename).info(`Fetching Posting for ${payload.recordType}`, payload);
        try{
            do {
                let response = await this.axios.get(`/tenants/${tenantID}/coas/${coaID}/ledgers/${ledgerID}/postings`, {
                    params : {
                        ...params,
                        pageNo,
                        pageSize
        
                    },headers : {
                        'Authorization':  `${token}`
                    },
                });
                if(response.data?.content?.length > 0){
                    postings.push(...response.data.content);
                    fetchMore = true;
                    pageNo++;
                }else{
                    fetchMore = false;
                }
            } while(fetchMore)
            this.req.getLogger(__filename).info(`Fetched All Postings for ${payload.recordType}`, payload);
    
        }
        catch(error){
            this.req.getLogger(__filename).error(`Error Fetching Posting for ${payload.recordType}`, payload);  
        }
        return postings;
    }

    modifyPostingsDataForMerchantCredit(postings:any[]){
        return postings.map( ele => {
             const attrKeys = Object.keys(ele.attributes);
             const merchantNameKey = attrKeys.filter( k => k.split(".").includes("merchantName"))[0];
             const merchantCityKey = attrKeys.filter( k => k.split(".").includes("merchantCity"))[0] || attrKeys.filter( k => k.split(".").includes("merchant-city"))[0];
             const mcc = attrKeys.filter( k => k.split(".").includes("mcc"))[0];
             const tid = attrKeys.filter( k => k.split(".").includes("tid"))[0];
             const txnTime = new Date(ele.transactionTime);
             return {
               ...ele,
               transactionAmount : ele.value.amount,
               transactionCurrency : ele.value.currency,
               voucherCode: ele.attributes["journal.voucherCode"],
               transactionDate : `${txnTime.getDate()}/${txnTime.getMonth() + 1}/${txnTime.getFullYear()}`,
               transactionType : ele.attributes["super-card.txn-type"] || "-",
               merchantName : ele.attributes[merchantNameKey] || "-",
               merchantCity : ele.attributes[merchantCityKey] || "-",
               mcc : ele.attributes[mcc] || "-",
               tid : ele.attributes[tid] || "-",
            } } );
     }

}
