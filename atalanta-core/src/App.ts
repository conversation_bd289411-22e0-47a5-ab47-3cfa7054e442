import express from 'express';
import helmet from 'helmet';
import { AppModule, InitOptions } from './types';
import { loadEnv } from './Env';
import { setup } from './System';
import { start } from './Server';
import { setupErrorHandler } from './ErrorResponseHandler';
import { createNodeLogger, requestInterceptor } from '@zeta/node-logger';
import { routerLogger } from './middlewares/routerLogger';
import { requestValidator } from './middlewares/requestValidator';
import { requestSanitizer } from './middlewares/requestSanitizer';

const modules: AppModule[] = [];
const logger = createNodeLogger(__filename);

export const register = (module: AppModule) => {
    modules.push(module);
};

export const init = (initOptions: InitOptions) => {
    const app = express();
    const { envFilePath } = initOptions;
    // Handle Envs
    loadEnv(envFilePath);

    // Use Helmet for security
    app.use(helmet());
    // common setup
    setup(app);
    const router = express.Router();
    routerLogger(router);
    const typeOfPayloadSecurity: 'SANITIZATION' | 'VALIDATION' = process.env.TYPE_OF_PAYLOAD_SECURITY as 'SANITIZATION' | 'VALIDATION' ?? 'SANITIZATION';
    if (typeOfPayloadSecurity === 'VALIDATION') {
        requestValidator(router);
    } else if (typeOfPayloadSecurity === 'SANITIZATION') {
        requestSanitizer(router);
    }
    
    // setup all respective modules
    modules.forEach((module) => {
        try {
            app.use(requestInterceptor);
            module.setup(router);
            app.use(router);
        } catch (e) {
            logger.info(`${module} setup could not be done`);
        }
    });

    setupErrorHandler(app);

    // start server
    start(app);
};
