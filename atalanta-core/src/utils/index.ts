import { GetAuthResponse, TemplateData } from '../types';
import { template } from 'lodash';
import { Request } from 'express';
import JSONBigIntMod from 'json-bigint';

const JSONBigInt = JSONBigIntMod({ storeAsString: true });

export const renderTemplate = (templateString: string, templateData: TemplateData) => {
    const lodashTemplate = template(templateString);
    return lodashTemplate(templateData);
};
export const createErrorObj = (
    error: any,
): {
    statusCode: number;
    response: {
        status: number;
        data: unknown;
    };
} => {
    let errorObj = null;
    if (error.response) {
        errorObj = error;
    } else {
        errorObj = {
            response: {
                status: error.statusCode,
                data: error?.response?.data || error.message,
            },
        };
    }
    return errorObj;
};

export const getAuthDetails = (authResponse: GetAuthResponse) => {
    const AUTH_IDENTIFIER = '@authProfile.';
    const getAuthProfileAndDomainFromUserResourceJID = (userResourceJID: string) => {
        if (userResourceJID?.length) {
            const userDetails = userResourceJID.split(AUTH_IDENTIFIER);
            if (userDetails.length === 2) {
                return {
                    authProfileId: userDetails[0],
                    userJID: userResourceJID.split('/')[0],
                };
            }
            return null;
        }
        return null;
    };
    const authDetails = getAuthProfileAndDomainFromUserResourceJID(authResponse.userResourceJid);
    const tenantId = authResponse.tenant,
        sandboxId = authResponse.sandbox,
        userJID = authDetails.userJID,
        authProfileId = authDetails.authProfileId;

    return { tenantId, sandboxId, userJID, authProfileId };
};

/**
 * A function to extract the token from the request headers
 * @param req Request object
 * @returns token
 */
export const extractToken = (req: Request) => {
    const authHeader = req.headers.authorization;

    if (!authHeader) {
        return null;
    }

    const parts = authHeader.split(' ');

    // If the header is in the format "Bearer <token>", return the token
    if (parts.length === 2 && parts[0].toLowerCase() === 'bearer') {
        return parts[1];
    }

    // If the header is not in the "Bearer <token>" format, assume the entire header is the token
    return authHeader;
};

/**
 * Transforms the request data to a JSON string.
 * If the data is a string, it tries to parse it as JSON and then stringify it.
 * If the data is not a string, it directly stringifies it.
 * This function is useful when you need to ensure that your data is in a stringified JSON format.
 * It uses JSONBigInt to handle large integers that JSON.parse() cannot.
 *
 * @param {string | object} data - The data to be transformed, can be a string or an object.
 * @returns {string} - The transformed data as a stringified JSON.
 */
export const transformRequest = (data: string | object): string => {
    if (typeof data === 'string') {
        try {
            return JSON.stringify(JSONBigInt.parse(data));
        } catch (error) {
            return data;
        }
    } else {
        return JSONBigInt.stringify(data);
    }
};

/**
 * Transforms the response data from a JSON string to a JavaScript value or object.
 * If the data is not null or undefined, it parses it as JSON.
 * This function is useful when you need to parse your data from a stringified JSON format to a JavaScript value or object.
 * It uses JSONBigInt to handle large integers that JSON.parse() cannot.
 *
 * @param {string} data - The data to be transformed, must be a string.
 * @returns {string | object} - The transformed data as a JavaScript value or object.
 */
export const transformResponse = (data: string): string | object => {
    try {
        return data ? JSONBigInt.parse(data) : data;
    } catch (error) {
        return data;
    }
};
