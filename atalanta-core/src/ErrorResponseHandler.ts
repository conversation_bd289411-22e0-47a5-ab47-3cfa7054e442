import { Express, ErrorRequestHand<PERSON> } from 'express';
import { StatusCodes, getReasonPhrase } from 'http-status-codes';
import dotProp from 'dot-prop';
import env from './Env';
import { BaseServiceException } from './exceptions/BaseServiceException';
import { errorInterceptor } from '@zeta/node-logger'

export const handler: ErrorRequestHandler = (err, req, res, next) => {
    const log = console;

    const baseServiceException = err instanceof BaseServiceException;

    const status = baseServiceException ? err.statusCode : StatusCodes.INTERNAL_SERVER_ERROR;

    const errorResponse = {
        timestamp: Date.now(),
        status: status,
        error: getReasonPhrase(status),
        type: err.name,
        message: dotProp.get(err, 'response.data', err.message),
        path: req.url,
        stack: err.stack,
    };

    log.error(`%s ${req.method.match('POST|PUT|PATCH') ? '%o' : ''}`, err, {
        request: req,
        response: errorResponse,
    });

    // Todo - improve it later
    res.render('sign-up/error.ejs', {
        toolName: '',
        ...errorResponse,
    });

    // return res.status(err.statusCode || StatusCodes.INTERNAL_SERVER_ERROR).send(errorResponse);
};

export const setupErrorHandler = (app: Express) => {
    app.use(handler);
    app.use(errorInterceptor);
};
