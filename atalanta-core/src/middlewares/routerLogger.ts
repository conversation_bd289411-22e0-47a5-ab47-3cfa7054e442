import { Router } from 'express';

export const routerLogger = (router: Router) => {
    router.use((req, res, next) => {
        const logger = req.getLogger('Atalanta');
        const startTime = Date.now();

        logger.info(`Router hit - ${req.method} ${req.originalUrl}`, {
            method: req.method,
            url: req.originalUrl,
            headers: req.headers,
            query: req.query,
            body: req.body,
            ip: req.ip,
            userAgent: req.get('User-Agent')
        });

        res.on('finish', () => {
            const duration = Date.now() - startTime;
            logger.info(`Request to ${req.method} ${req.originalUrl} completed with status ${res.statusCode} in ${duration}ms`, {
                method: req.method,
                url: req.originalUrl,
                statusCode: res.statusCode,
                duration,
                ip: req.ip,
                userAgent: req.get('User-Agent')
            });
        });

        res.on('error', (err) => {
            const duration = Date.now() - startTime;
            logger.error(`Request to ${req.method} ${req.originalUrl} failed with error: ${err.message} in ${duration}ms`, {
                method: req.method,
                url: req.originalUrl,
                duration,
                error: err.message,
                stack: err.stack,
                ip: req.ip,
                userAgent: req.get('User-Agent')
            });
        });

        next();
    });
};