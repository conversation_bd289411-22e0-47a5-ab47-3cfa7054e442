import { Request, Response, NextFunction, Router } from 'express';
import xss from 'xss';

const sanitize = (value: any) => {
    if (typeof value === 'string') {
        return xss(value);
    } else if (typeof value === 'object' && value !== null) {
        for (const key in value) {
            if (value.hasOwnProperty(key)) {
                value[key] = sanitize(value[key]);
            }
        }
    }
    return value;
};

export const requestSanitizer = (router: Router) => {
    router.use((req: Request, res: Response, next: NextFunction) => {
        req.body = sanitize(req.body);
        req.query = sanitize(req.query);
        req.params = sanitize(req.params);
        next();
    });
};