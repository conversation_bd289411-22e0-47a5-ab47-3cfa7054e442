import { Request, Response, NextFunction, Router } from 'express';
import { validationResult } from 'express-validator';
const xss = require('xss');

export const requestValidator = (router: Router) => {
    router.use((req: Request, res: Response, next: NextFunction) => {
        
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ errors: errors.array() });
        }

        // Check for XSS patterns using xss library
        const checkForXss = (input: any): boolean => {
            if (typeof input === 'string') {
                return input !== xss(input);
            }
            if (typeof input === 'object' && input !== null) {
                for (const key in input) {
                    if (checkForXss(input[key])) {
                        return true;
                    }
                }
            }
            return false;
        };

        if (checkForXss(req.body) || checkForXss(req.query) || checkForXss(req.params)) {
            return res.status(400).json({ error: 'Potential XSS attack detected' });
        }

        next();
    });
}

export default requestValidator;