import { BaseServiceException } from './BaseServiceException';
import { StatusCodes } from 'http-status-codes';

export class CruxTokenException extends BaseServiceException {
    public response: {
            message: string;
            error: {
                [k: string]: any;
            };
        };
        constructor(exceptionObject: {
            message: string;
            error: {
                statusCode?: number;
                response: {
                    status?: number;
                    data: unknown;
                };
            };
        }) {
            super(exceptionObject.message);
            this.statusCode =
                exceptionObject.error?.statusCode ||
                exceptionObject.error?.response?.status ||
                StatusCodes.INTERNAL_SERVER_ERROR;
            this.response = {
                message: exceptionObject.message,
                error: exceptionObject.error?.response?.data || {},
            };
        }
}
