import { BaseServiceException } from './BaseServiceException';
import { StatusCodes } from 'http-status-codes';

export class InternalAPIException extends BaseServiceException {
    // The format in which the internal API error should be displayed
    public response: {
        api: string;
        error: {
            [k: string]: any;
        };
    };
    constructor(exceptionObject: {
        api: string;
        error: {
            statusCode: number;
            response: {
                status: number;
                data: unknown;
            };
        };
    }) {
        super(exceptionObject.api);
        this.statusCode =
            exceptionObject.error?.statusCode ||
            exceptionObject.error?.response?.status ||
            StatusCodes.INTERNAL_SERVER_ERROR;
        this.response = {
            api: exceptionObject.api,
            error: exceptionObject.error?.response?.data || {},
        };
    }
}
