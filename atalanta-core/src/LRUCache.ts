const LRU = require('lru-cache');

const getLRUOptions = {
    max: parseInt(process.env.CACHE_MAX),

    // for use with tracking overall storage size
    maxSize: parseInt(process.env.CACHE_MAX_SIZE),
    sizeCalculation: (_value: unknown, _key: string) => {
        return 1;
    },

    // how long to live in ms
    ttl: parseInt(process.env.CACHE_TTL),

    // return stale items before removing from cache?
    allowStale: false,
    updateAgeOnGet: false,
    updateAgeOnHas: false,
};

// returns a single cache object
export const LRUCache = (() => {
    let cache: any;

    function initCache() {
        return new LRU(getLRUOptions);
    }

    return {
        getCache: function () {
            if (!cache) {
                cache = initCache();
            }
            return cache;
        },
    };
})();
