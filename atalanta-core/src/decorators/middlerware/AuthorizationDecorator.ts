import { CipherService } from '../../services/CipherService';
import { InternalAPIException } from '../../exceptions/InternalAPIException';
import { StatusCodes } from 'http-status-codes';
import { Request, Response } from 'express';
import { GetAuthResponse, GetAuthorizationResponse, Action } from '../../services/Interfaces/Cipher';
import { AuthorizationDecoratorParams } from '../Interfaces';
import { extractToken } from './../../utils';
interface AuthRequest extends Request {
    authResponse: GetAuthResponse;
}

export function Authorize({ object, action }: AuthorizationDecoratorParams) {
    return function (target: Object, propertyKey: Object, descriptor: PropertyDescriptor) {
        const originalMethod = descriptor.value;
        descriptor.value = async function (request: AuthRequest, response: Response) {
            // Authenticate
            const cipherService = new CipherService(request);
            const token = extractToken(request);
            let authResponse: GetAuthResponse = null;
            try {
                authResponse = await cipherService.authenticate(token);
            } catch (error) {
                // If Backend error
                if (error instanceof InternalAPIException) {
                    return response.status(error.statusCode).send(error.response);
                }
            }
            // If authResponse is null and no sessionId, then it is unauthorized
            if (authResponse?.sessionId) {
                // Prepare the objectJID
                const objectJID = `${authResponse.tenant}@${object}.zeta.in`;
                // Authorize
                let authorizationResponse: GetAuthorizationResponse = null;
                try {
                    authorizationResponse = await cipherService.authorize({
                        objectJID,
                        action,
                        token,
                    });
                } catch (error) {
                    // If Backend error
                    if (error instanceof InternalAPIException) {
                        return response.status(error.statusCode).send(error.response);
                    }
                }
                /**
                 * How to check if the authenticated user is authorized?
                 *  - The authroized response would the relevant actions. If not, then it is unauthorized
                 *  - Check if the tenant id , sandbox , object type and action are matching with any of the action
                 */
                const validAction = authorizationResponse?.actions?.find((actionObj: Action) => {
                    if (
                        actionObj.tenantID.toString() === authResponse.tenant &&
                        actionObj.sandboxID.toString() === authResponse.sandbox &&
                        actionObj.objectType === object &&
                        actionObj.action === action
                    ) {
                        return true;
                    }
                    return false;
                });
                if (validAction) {
                    request.authResponse = authResponse;
                    originalMethod.call(this, request, response);
                    return;
                }
            }
            return response.status(StatusCodes.UNAUTHORIZED).send({ error: 'Not Authorized' });
        };
    };
}
