import { Express } from 'express';
import bodyParser from 'body-parser';
import { DEFAULT_PORT, PORT } from './Constants';
import { setupHealthCheck } from './HerculesHealthChcek';
import { setupPing } from './Ping';
import compression from 'compression';
import cors from 'cors';
import { ALLOWED_METHODS, ALLOWED_ORIGINS, ALLOWED_HEADERS } from './Constants';

const CORS_OPTIONS: cors.CorsOptions = {
    origin: ALLOWED_ORIGINS,
    methods: ALLOWED_METHODS,
    allowedHeaders: ALLOWED_HEADERS,
    preflightContinue: false,
    optionsSuccessStatus: 204,
};

const setupRootMiddlewares = (app: Express) => {
    app.use(compression());
    app.use(bodyParser.urlencoded({ extended: false }));
    app.use(bodyParser.json());
    app.use(cors(CORS_OPTIONS));
};

export const setup = (app: Express) => {
    app.set(PORT, process.env.PORT || DEFAULT_PORT);
    setupPing(app);
    setupHealthCheck(app);
    setupRootMiddlewares(app);
};
