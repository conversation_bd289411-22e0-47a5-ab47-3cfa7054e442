import dotEnvExtended from 'dotenv-extended';
import path from 'path';

export const isDev = process.env.NODE_ENV === 'development';
const isTest = process.env.NODE_ENV === 'test';

export const loadEnv = (envFilePath: string) => {
    if (!envFilePath) {
        throw new Error('Please provide env file path');
    } else {
        if (isDev || isTest) {
            console.log('Using .env.local file to supply config environment variables');
            envFilePath = `${envFilePath}.local`;
        } else {
        }
        dotEnvExtended.load({
            encoding: 'utf8',
            silent: true,
            path: envFilePath,
            // defaults: '.env.defaults',
            schema: '.env.schema',
            errorOnMissing: true,
            errorOnExtra: false,
            errorOnRegex: false,
            includeProcessEnv: true,
            assignToProcessEnv: true,
            overrideProcessEnv: false,
        });
    }
};

export default {
    ENVIRONMENT: process.env.NODE_ENV,
    isDev,
    isProd: !isDev,
};
