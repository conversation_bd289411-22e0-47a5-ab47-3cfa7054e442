import { register, init } from '../src/App';
import path from 'path';

jest.mock('express', () => {
    return require('jest-express');
});

const mockModule = {
    name: 'atalanta-mock-module',
    setup: jest.fn(),
};

const envFilePath = path.join(__dirname, '..', '.env');
describe('App test suite', () => {
    test('it should start the server', () => {
        register(mockModule);
        init({ envFilePath });
        expect(mockModule.setup).toBeCalled();
    });

    test('it should throw an error if envFilePath is not provided', () => {
        register(mockModule);
        expect(() => {
            init({ envFilePath: '' });
        }).toThrow('Please provide env file path');
    });
});
