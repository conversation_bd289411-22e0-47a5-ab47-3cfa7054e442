import { handler } from '../src/ErrorResponseHandler';
import { Express } from 'jest-express/lib/express';
import { BaseServiceException } from '../src/exceptions/BaseServiceException';

let app: any;
describe('Error Response Handler test suite', () => {
    let req: any;
    let res: any;
    let next: any;
    let err: any;
    beforeEach(() => {
        req = { url: '/demo', method: 'GET' };
        res = { render: jest.fn() };
        next = jest.fn();
        err = {
            statusCode: 500,
        };
        app = new Express();
    });

    afterEach(() => {
        app.resetMocked();
    });
    test('it should properly initialise error response handler', () => {
        handler(err, req as any, res as any, next);
        expect(res.render).toHaveBeenCalledWith('sign-up/error.ejs', expect.anything());
    });

    test('it should properly initialise error response handler when err is an instance of base service', () => {
        err = new BaseServiceException('some error occurred');
        handler(err, req as any, res as any, next);
        expect(res.render).toHaveBeenCalledWith('sign-up/error.ejs', expect.anything());
    });
});
