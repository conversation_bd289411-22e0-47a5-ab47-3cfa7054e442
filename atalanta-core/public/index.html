<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>api playbook demo</title>
        <style>
            .notifier {
                position: absolute;
                bottom: 10px;
                text-align: center;
                background: yellowgreen;
                margin-left: 50%;
                transform: translateX(-50%);
                padding: 20px;
                border-radius: 5px;
                font-family: verdana;
                color: white;
                box-shadow: 0px 3px 10px #a9a6a6;
            }
        </style>
    </head>
    <body>
        <button id="signup">Sign Up</button>
        <button id="signin">Sign In</button>
        <script>
            const url = new URL(window.location.href);
            if (url.searchParams.get('signup') === 'true') {
                displaySuccessMessage('You are signedup successfully, please login again !', 5000);
            }

            document.getElementById('signup').addEventListener('click', function () {
                const form = `<form id="signup-form" method="post" action="http://localhost:3000/api/v1/signup">
                    <input name="redirectUrl" type="hidden" value="${window.location.href}">
                    <input name="toolName" type="hidden" value="${window.document.title}">
                    <input name="domainId" type="hidden" value="test.aphrodite">
                    <input name="role" type="hidden" value="admin">
                    <input name="roleName" type="hidden" value="read_apibook">
                    <input name="sandboxID" type="hidden" value="6">
                    <input name="tenantID" type="hidden" value="140827">
                </form>`;
                document.body.innerHTML += form;
                document.getElementById('signup-form').submit();
            });

            //     const domainId = request.params.domainId || request.body.domainId;
            // const role = request.params.role || request.body.role;
            // const redirectUrl = request.params.redirectUrl || request.body.redirectUrl;
            // const toolName = request.params.toolName || request.body.toolName;
            // const roleName = request.params.roleName || request.body.roleName;
            // const sandboxID = request.params.sandboxID || request.body.sandboxID;
            // const tenantID = request.params.tenantID || request.body.tenantID;
            document.getElementById('signin').addEventListener('click', function () {});

            function displaySuccessMessage(msg, timer = 1000) {
                const el = document.createElement('div');
                el.setAttribute('class', 'notifier');
                el.innerHTML = msg;
                document.body.appendChild(el);
                setTimeout(() => {
                    el.parentNode.removeChild(el);
                }, timer);
            }
        </script>
    </body>
</html>
