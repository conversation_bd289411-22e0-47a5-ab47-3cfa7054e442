### Changelog

All notable changes to this project will be documented in this file. Dates are displayed in UTC.

Generated by [`auto-changelog`](https://github.com/CookPete/auto-changelog).

#### [1.11.9](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.11.8...1.11.9)

- fix(spb-2972): removed active phase from mis report [`#34`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/34)
- fix(SPB-2864): monthly schedule API [`#33`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/33)
- fix: period time calculation for scheduling [`#32`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/32)
- fix(SPB-2972): removed active phase from mis reports [`65e53ba`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/65e53ba98d67e2eb1b361f8c708de3704822cb6c)
- fix(SPB-2972): removed active phase from schedule data API [`7131c03`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/7131c0387e37167be9d0bf80fd226e5726daf7fd)
- fix(SPB-2972): error handling for missing phase run info [`34281c4`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/34281c4ecd807208bef6e1e8b6dba05955d23efc)

#### [1.11.8](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.11.7...1.11.8)

> 26 June 2025

- feat: updated error messages [`#31`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/31)
- fix(spb-2622): added timezone in report api [`#30`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/30)
- Release 1.11.8 [`b25deca`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/b25deca2dcf0883d428033a0c4f44f9bd8b91a6d)

#### [1.11.7](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.11.6...1.11.7)

> 23 June 2025

- fix(spb-2506): missing orchestra runs for phases [`#29`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/29)
- fix(spb-2506): phase failed runs info [`2d587d8`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/2d587d859f87651ad125a346551a5fee1b548fa5)
- Release 1.11.7 [`23c4e61`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/23c4e616594c8d6fdb4e43e2106a9e5ec3b4795f)
- fix(spb-2632): tz issue for reporting [`3a080f3`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/3a080f3c1d24f5b8125ce6bf4fafc83964626fda)

#### [1.11.6](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.11.5...1.11.6)

> 19 June 2025

- fix(SPB-2527): reports generation issue [`#28`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/28)
- Release 1.11.6 [`731efcf`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/731efcf896a696546ac00d6baf5df08dec7e24d0)
- fix: reporting issue [`a246da9`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/a246da9fba377df0a7a8d1012c5f8879a5b82a42)

#### [1.11.5](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.11.4...1.11.5)

> 19 June 2025

- fix(spe-3175): report generation time in reports [`#27`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/27)
- fix: report generation time in reports [`17e4d8b`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/17e4d8b1e1775ffc8867a924ed2c2dacded87d41)
- Release 1.11.5 [`8e12cea`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/8e12cea27ee88f38eced3cfdefffec18026b0318)

#### [1.11.4](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.11.3...1.11.4)

> 17 June 2025

- fix(SPB-2503): added pagination for get orchestra run data API [`#26`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/26)
- Release 1.11.4 [`100a60e`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/100a60e3dce6fbbd335a8f55a8cc2f924a7354a1)

#### [1.11.3](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.11.2...1.11.3)

> 16 June 2025

- Release 1.11.3 [`57c6208`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/57c620858036ef8dd872fc342a4f0b3b6bfd4b81)
- feat(spe-3175): duration formatting for mis report [`aa207fd`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/aa207fd3aab97e12bfb6382242e93926b1bd342e)
- feat: duration formatting for mis report [`510793e`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/510793eb2de49211747fc5894055a31f2e9d33b7)

#### [1.11.2](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.11.1...1.11.2)

> 11 June 2025

- fix: phase not complete scenario [`#23`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/23)
- Release 1.11.2 [`a8c7d10`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/a8c7d101dea1d6630c310ac3bd16b82bc29336f9)

#### [1.11.1](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.11.0...1.11.1)

> 9 June 2025

- Release 1.11.1 [`b0a9251`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/b0a92518a1f166f9a952d05c3e9669deb73b9526)
- fix(SPE-3175): phase timeline end time [`f73b4fc`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/f73b4fcb83f1ce886d4a341a847124fc546e6973)
- fix: phase start and end time [`3ea0716`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/3ea07165e80d73b87eba12a85277baa320f668b8)

#### [1.11.0](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.10.3...1.11.0)

> 21 May 2025

- mis-scheduled-report-data-api-enhancement [`#20`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/20)
- fix(spe-3175): exporting report by decoding URL params [`#21`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/21)
- update contracts [`1480ec0`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/1480ec043554d7e2c20f16e7eb21ab85af7867f1)
- fix: reportType checks [`7b46633`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/7b4663394a3e7bba5830f6401020d5d7d5c28365)
- feat: updated name [`9c4611b`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/9c4611b01b39254efcb21ee6c011d997cd699119)

#### [1.10.3](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.10.2...1.10.3)

> 15 May 2025

- feat(SPE-560): show mdc.trace_Id [`3ca1888`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/3ca18886c5eae3db76b6e5176131d80ea6607c96)
- Release 1.10.3 [`8366e75`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/8366e759bf93d07fedebfcb1f76112e32143ec2c)

#### [1.10.2](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.10.1...1.10.2)

> 12 May 2025

- feat(spe-3175): added API for scheduling & exporting mis reports [`#19`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/19)
- feat: added API for scheduling & exporting mis reports [`d7c9d01`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/d7c9d014483164b8ce82e0dee14723ed61aadbdf)
- feat: phase mapping optimization [`d9db09a`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/d9db09a85aded7fbedf2053ad2025e1bdcb1b8fa)
- refactor: code quality [`5ae7380`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/5ae7380664612f9bff402bda987f384a7dae3a8c)

#### [1.10.1](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.10.0...1.10.1)

> 29 April 2025

- fix: build issue [`#18`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/18)
- Release 1.10.1 [`9450f2f`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/9450f2f013ca9b583d3e455b074f58f7cab9533b)

#### [1.10.0](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.9.8...1.10.0)

> 29 April 2025

- feat(spe-3175): APIs for mis reports [`#17`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/17)
- feat: removed service client dependencies [`f060bda`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/f060bda493b046bfe51b074376d9574c0aebc8d0)
- feat: created calendar service [`1a70db7`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/1a70db7d6657c50bec10edd409cd262c5031e8f5)
- feat: added service-client pkg [`a512390`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/a51239061d1d8465dccd734ab60d9afaa67f4017)

#### [1.9.8](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.9.7...1.9.8)

> 3 February 2025

- feat: added logs for phase run controller [`#14`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/14)
- Release 1.9.8 [`f53c88a`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/f53c88a721092e96cfca3a69b99565e257976d47)

#### [1.9.7](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.9.6...1.9.7)

> 17 January 2025

- feat: updated core version [`#13`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/13)
- Release 1.9.7 [`04264b8`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/04264b89e3f4b857d681fdb6e5eb46cb4878e903)

#### [1.9.6](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.9.6-0...1.9.6)

> 16 January 2025

- feat: atalanta core version update [`#12`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/12)
- Release 1.9.6 [`1347714`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/1347714be75edd338920fd6a369f19023abef8e6)

#### [1.9.6-0](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.9.5...1.9.6-0)

> 16 January 2025

- feat: atalanta core version update [`6902a1c`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/6902a1c8a358e9f0c0e96fa404226634bc6decac)
- Release 1.9.6-0 [`93c7696`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/93c7696113d3f18b4fedb2f8e20ab314369a34e5)
- fix: build issue [`924e569`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/924e5693a087c006705ed11911f924d7763fe305)

#### [1.9.5](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.9.4...1.9.5)

> 24 June 2024

- Release 1.9.5 [`aa73109`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/aa73109995b01d37ee45d1e367c6a8587b69fa62)
- Release 1.9.3 [`125aab4`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/125aab450f6fd1a84facb7931544894d65ffd52d)

#### [1.9.4](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.9.3...1.9.4)

> 24 June 2024

- fix(TCH-20915) : Removed balance view controller from orchestra module [`#60`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/60)
- feat(TCH-27016): fix vulnerabities [`#58`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/58)
- Release 1.9.4 [`9b83960`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/9b839608af085d18f38319c347dc97e6e17b22b4)

#### [1.9.3](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.9.3-0241a25.0...1.9.3)

> 24 June 2024

- Release 1.9.3 [`63f5853`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/63f585329a23265cf86a7f61d3f3add2cf89af20)

#### [1.9.3-0241a25.0](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.9.2...1.9.3-0241a25.0)

> 20 June 2024

- feat(TCH-27016): fix vulnerabities [`0241a25`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/0241a25dd926ae7ad101fda0b658ac78bd343371)
- Release 1.9.3-0241a25.0 [`56024bb`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/56024bb4d0fd5f1da05651d3947b70567bd29336)

#### [1.9.2](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.9.1...1.9.2)

> 29 May 2024

- chore: updated core dep [`477ee82`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/477ee82116c22366329fae8e9a98db2a8d8ca63e)
- Release 1.9.2 [`d66f691`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/d66f691fa273929edea60be52490217f02cecfde)

#### [1.9.1](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.9.0...1.9.1)

> 20 May 2024

- feat(TCH-20915) : added tenantId which is requied in collection service constructor call [`#57`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/57)
- Release 1.9.1 [`9219d3c`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/9219d3c18a68b937d8945b3fc4650c81acf9ea99)
- chore: updated core dependency [`9a7b625`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/9a7b625a62e6cb34fdd97e21bab66e73f4b17f9c)

#### [1.9.0](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.8.7...1.9.0)

> 16 April 2024

- Feat: backrub visualization atalanta apis [`#56`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/56)
- Release 1.9.0 [`32e20dc`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/32e20dcb2a468042d3cc8a5caeab9b55c75d1691)

#### [1.8.7](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.8.6...1.8.7)

> 15 April 2024

- Add new routes for Backrub visualization in Routes.ts [`862df57`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/862df572362bb27c8f6c18b0dd1b06c51e10bf4c)
- Release 1.8.7 [`e67a60a`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/e67a60a5a1b8227301329ae4f313ec1a979e37a5)

#### [1.8.6](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.8.5...1.8.6)

> 15 April 2024

- Release 1.8.6 [`5ddcc84`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/5ddcc842b4cf8ae0c3e5b2925f91cde55dfe48a0)
- feat(): fix build issues [`246d0c9`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/246d0c9da12a2c7866bbed9272ca694da2f1f2aa)
- feat(): fix build issues [`6685010`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/668501077d5521d0e56f2809c53d37ca1e12625d)

#### [1.8.5](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.8.4...1.8.5)

> 15 April 2024

- changes for daybookDelinquencyUseCase query [`#52`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/52)
- Added a genric vm ui query handler [`#51`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/51)
- modifed default to 15m from 1h [`#50`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/50)
- changes to fetch tps based on phase and type for span [`#49`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/49)
- changes for worker list [`#48`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/48)
- feat(): added Data preperation worker related data for visualisation [`#47`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/47)
- Release 1.8.5 [`6fcbac5`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/6fcbac583a4e4079dc075d92fcaf07d4d35b4705)
- feat(): merge with master [`9abeaf3`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/9abeaf3520f0bf7f15ba28c5866bad43728c5b0b)
- Add new routes for Backrub visualization, current CBU and type, and summary data [`a529653`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/a529653d5e511b450bd7499f13a0aac6e138e09e)

#### [1.8.4](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.8.3...1.8.4)

> 5 April 2024

- Updated core version for accepting orchestra url as input [`#55`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/55)
- Release 1.8.4 [`da1e9f9`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/da1e9f9bfafe2a1bf0204f190dfc1711d302786a)

#### [1.8.3](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.8.1...1.8.3)

> 4 April 2024

- Release 1.8.3 [`28e1ca1`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/28e1ca13c41f25281dda621a7704ed08090d8344)
- chore: updated package version [`2bb4de5`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/2bb4de57f03be7b3ce63b6d612df125c5f3e3743)

#### [1.8.1](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.8.0...1.8.1)

> 4 April 2024

- feat(TCU-3879): Balance view controller refactoring changes and sandboxing added for save and get views methods [`#54`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/54)
- Release 1.8.1 [`18a9e3d`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/****************************************)

#### [1.8.0](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.7.0...1.8.0)

> 4 April 2024

- chore: updated package lock file [`3693670`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/3693670a463219a1331dfab93c8f9e8f57406b1c)
- feat(TCU-3879): Balance view controller refactoring changes [`f1c76a4`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/f1c76a43b8ffc11b7e3fd38e36d6a69d811dd45c)
- Release 1.8.0 [`31adb36`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/31adb367f81fdcbee53421d487937bf60a6a148d)

#### [1.7.0](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.6.9...1.7.0)

> 19 March 2024

- fix(TCU-3879) : Updated core dependency and refactored the imports as per the latest core version [`#53`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/53)
- Release 1.7.0 [`524730a`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/524730ac1bd287afb568b75a7e6c260162e4d32e)

#### [1.6.9](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.6.8...1.6.9)

> 13 March 2024

- changes for daybookDelinquencyUseCase query [`#52`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/52)
- Release 1.6.9 [`8ac9b00`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/8ac9b000be6fb8977d3ce955b82a7383acfed3ed)

#### [1.6.8](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.6.7...1.6.8)

> 13 March 2024

- Release 1.6.8 [`68337d5`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/68337d5819d74158edbc4ce629213db3ddfce9ea)

#### [1.6.7](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.6.6...1.6.7)

> 13 March 2024

- Added a genric vm ui query handler [`#51`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/51)
- Release 1.6.7 [`f89c2f5`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/f89c2f5a4ea0a8b28769fc55f23960e0b902c720)

#### [1.6.6](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.6.5...1.6.6)

> 12 March 2024

- modifed default to 15m from 1h [`#50`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/50)
- Release 1.6.6 [`1320b1a`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/1320b1a0f6b064002805c6a03fb14072b08f9e09)

#### [1.6.5](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.6.4...1.6.5)

> 12 March 2024

- changes to fetch tps based on phase and type for span [`#49`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/49)
- Release 1.6.5 [`7b13eae`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/7b13eae6858b586f3a22ab9cdfa522b398fa818c)

#### [1.6.4](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.6.3...1.6.4)

> 11 March 2024

- changes for worker list [`#48`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/48)
- Release 1.6.4 [`03cbd25`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/03cbd259a93e80b5b8910ba4545bd986b769ce61)

#### [1.6.3](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.6.2...1.6.3)

> 11 March 2024

- Release 1.6.3 [`01d00b3`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/01d00b3cfcbaf2d8d59f8536661f0eeb4112d8fb)
- feat(): add the feat for getting data [`e61be5e`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/e61be5ea929e8c17d41627005c182d6138c61562)

#### [1.6.2](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.6.1...1.6.2)

> 11 March 2024

- Release 1.6.2 [`25e797b`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/25e797b7f7923d0532ea79973de225607e8c2830)
- feat(): add logger for reports [`68e4316`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/68e431669472eac0e272a94e93cb9e015717ab6e)

#### [1.6.1](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.6.0...1.6.1)

> 11 March 2024

- Release 1.6.1 [`d157614`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/d157614d1b563d8402ebf79a7f6e27aa995eeabd)
- feat(): add logger for orchestra report [`7a81e01`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/7a81e01ad7be801cdd56c4f449a009ca7593d823)

#### [1.6.0](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.5.8...1.6.0)

> 11 March 2024

- feat(): added Data preperation worker related data for visualisation [`#47`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/47)
- Release 1.6.0 [`1c97ad1`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/1c97ad1e062655aa7731ce9bfc32e2e647a57e44)

#### [1.5.8](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.5.7...1.5.8)

> 10 March 2024

- Release 1.5.7-8 [`0c721c2`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/0c721c26759b43a5c2b84056ae4fb8d4b28f87dc)
- Release 1.5.7-9 [`39c0e3c`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/39c0e3c61d3af6910b1c8ac3eeda55c434abfc21)
- Release 1.5.8 [`a721399`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/a721399bf1b73f92397aba5daa11e40433e77357)

#### [1.5.7](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.5.7-9...1.5.7)

> 9 March 2024

#### [1.5.7-9](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.5.7-8...1.5.7-9)

> 10 March 2024

- Release 1.5.7-9 [`39c0e3c`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/39c0e3c61d3af6910b1c8ac3eeda55c434abfc21)

#### [1.5.7-8](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.5.7-7...1.5.7-8)

> 10 March 2024

- Release 1.5.7-8 [`0c721c2`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/0c721c26759b43a5c2b84056ae4fb8d4b28f87dc)
- Update base URL for Victoria service [`a652b72`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/a652b72266e07091ac52967dcccc0e0eb65360fc)

#### [1.5.7-7](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.5.7-6...1.5.7-7)

> 10 March 2024

- Release 1.5.7-7 [`9bb4a32`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/9bb4a32cfbdb37bbf24d873a060b476134a18bc8)
- Update token in backrubConstants.ts [`5f8ec26`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/5f8ec264ff962119cff93bca75eeb0877690a669)

#### [1.5.7-6](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.5.7-5...1.5.7-6)

> 9 March 2024

- Fix phaseId validation and add null check for currentValue in getCurrentTransactions [`8fc1020`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/8fc1020637328172d2c8c30070915a8dba18dab6)
- Release 1.5.7-6 [`ca79ec9`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/ca79ec9fc35a87a58400776329104be7a0fc7f03)
- Release 1.5.7 [`778a2b8`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/778a2b83f958408a82b51dd67bea2e8575c34ab2)

#### [1.5.7-5](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.5.7-4...1.5.7-5)

> 9 March 2024

- Fix phaseId validation and update time duration in BackrubService [`452a9f5`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/452a9f59d77f9ad93493bb23852abe63d8066d1f)
- Release 1.5.7-5 [`8f0d0a3`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/8f0d0a3d112af807a473b9a7c7bc97bdb3a1d971)

#### [1.5.7-4](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.5.7-3...1.5.7-4)

> 9 March 2024

- feat: add daybook and orchestra phase differentiator using status verifier [`eef9f6a`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/eef9f6ae28d050b291f94eee4bf734f6d4d97564)
- Release 1.5.7-4 [`93d37fb`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/93d37fb8756a596e74c7ad38cb19de07200ac694)

#### [1.5.7-3](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.5.7-2...1.5.7-3)

> 8 March 2024

- chore: update for adding seperate query for delinquency and daybook max over time [`03ff24c`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/03ff24c6fd7e90a65dba74b68b8ea709d5cac747)
- Release 1.5.7-3 [`a12a637`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/a12a637762a03596199c274eed0753b42d242e65)
- Update currentTransactions value in BackrubVisualizationController [`40a9ad7`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/40a9ad7e5c6dbe6c19ff105cbbb689674e6855ac)

#### [1.5.7-2](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.5.7-1...1.5.7-2)

> 7 March 2024

- Refactor getCurrentTransactions to return currentTransactions object [`11564c9`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/11564c90d42992129107ae670f3577e2663b1d26)
- Release 1.5.7-2 [`ffcd78e`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/ffcd78e418989019f1ec9d62a49b89990f264f9b)

#### [1.5.7-1](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.5.7-0...1.5.7-1)

> 7 March 2024

- Update Routes and BackrubVisualizationController [`3d44e24`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/3d44e243e6bc469ce10916507cc7697d98236374)
- Release 1.5.7-1 [`1370be8`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/1370be8b0dd5957d94c70d8725ddf09305a9d660)

#### [1.5.7-0](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.5.6...1.5.7-0)

> 7 March 2024

- Add new routes for Backrub visualization, current CBU and type, and summary data [`a529653`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/a529653d5e511b450bd7499f13a0aac6e138e09e)
- Release 1.5.7-0 [`fce244d`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/fce244dc573ed41c2dfe2a07e0ae09f7d1befec9)

#### [1.5.6](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.5.4...1.5.6)

> 14 February 2024

- Release 1.5.6 [`471a6be`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/471a6be551d3b3964b218be479857d5377d2b478)
- chore: package version updated [`0856c5b`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/0856c5b36796249ff56233ce2acc900f908b8505)

#### [1.5.4](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.5.3...1.5.4)

> 14 February 2024

- fix(TCH-8066) : atalanta core dep updated [`#46`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/46)
- Release 1.5.4 [`06c9eb8`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/06c9eb86b7ea434647ded716af86f80c222598fa)

#### [1.5.3](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.5.2...1.5.3)

> 8 February 2024

- Release 1.5.3 [`14b5935`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/14b59350be81f4c6f5c304b6ebcba616707ec93f)
- chore: util import fix [`49e82c7`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/49e82c7b2b6012bb3d511fc907498f7a4778059b)

#### [1.5.2](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.5.1...1.5.2)

> 8 February 2024

- Release 1.5.2 [`1cc837f`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/1cc837f1ea1a2f9ec582e7948f9c907634af5d62)
- chore: package lock updated [`21d94de`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/21d94ded5ec748ffae3bd1c44216e411dd422aac)

#### [1.5.1](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.5.0...1.5.1)

> 8 February 2024

- chore: package lock file updated [`16d88b3`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/16d88b31301fa3961e5936af2cfd7137bdffd02c)
- Release 1.5.1 [`7743c9a`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/7743c9aeea04e0594ef7bf362d5f5d371163440b)

#### [1.5.0](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.4.30...1.5.0)

> 8 February 2024

- feat(TCH-8066): Balance view controller added with save and get views functionality. This utitlises the atalanta core collection service. [`#45`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/45)
- Release 1.5.0 [`5108ae6`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/5108ae6292ce10f736c712357f800d60addf9ac5)

#### [1.4.30](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.4.29...1.4.30)

> 31 August 2023

- feat(TCH-15184): added request history column for Phase Run [`#44`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/44)
- Release 1.4.30 [`79baae6`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/79baae6cb6186a04d70c822dcbf681a138bc1344)

#### [1.4.29](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.4.28...1.4.29)

> 24 August 2023

- bugfix(TCT-9839) fetch latest execution of orchestra run [`#43`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/43)
- Release 1.4.29 [`9fd36bd`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/9fd36bd2961110e885b41c98a0e2e4b92f85dd14)

#### [1.4.28](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.4.27...1.4.28)

> 16 August 2023

- Release 1.4.28 [`9798288`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/9798288da24c7496f1ad3d172c69c64e7dc21cdd)
- edited package json [`9ab1b9e`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/9ab1b9ec73cda87cfc40f405007b67bf805b26f5)

#### [1.4.27](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.4.26...1.4.27)

> 16 August 2023

- other(TCU-612): fix phase cycle BFF to fetch the right details [`#42`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/42)
- Release 1.4.27 [`6e3c130`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/6e3c130599fd8db77cdca6d6558ef02a700880f3)

#### [1.4.26](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.4.25...1.4.26)

> 10 August 2023

- Release 1.4.26 [`d68af82`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/d68af82d775c03a90474b71b184ea88a9508fc79)
- chore: update package-lock [`8a993ed`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/8a993edad8f1b72239cc328bb08e1e0cc26c44d5)

#### [1.4.25](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.4.24...1.4.25)

> 10 August 2023

- Bugfix(TCT-9228) eod center failed ledgers scree [`#41`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/41)
- Release 1.4.25 [`f657829`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/f657829f0d9eef19c2def3ecb75bdf3cab348856)

#### [1.4.24](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.4.23...1.4.24)

> 9 August 2023

- bugfix(TCT-9228) move get ledger api from tachyon service to ledger service [`#40`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/40)
- Release 1.4.24 [`128d3c8`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/128d3c83be8d1d1ee76d842faaff03a0a61892d4)

#### [1.4.23](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.4.22...1.4.23)

> 3 August 2023

- feature(TCH-11171): revert phase timeline refactor [`#39`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/39)
- Release 1.4.23 [`b5b1576`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/b5b15769891cdd3eeb27dab02d0ec36c9b98169d)

#### [1.4.22](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.4.21...1.4.22)

> 2 August 2023

- feature(TCH-11171): refactor phase cycle to fetch status of all the phases [`#38`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/38)
- Release 1.4.22 [`43ef487`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/43ef487334aba474583db9be2c6c3182b6196904)

#### [1.4.21](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.4.19...1.4.21)

> 1 August 2023

- Release 1.4.21 [`7fa8e3c`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/7fa8e3cecdf3722acd5247723da0c0b5c3691579)
- chore: update package version [`cb914cc`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/cb914cc72560b7c4ade60a97536a72d2210bd361)

#### [1.4.19](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.4.18...1.4.19)

> 1 August 2023

- fix: eod process variables run status check added for 'false' string [`#37`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/37)
- Release 1.4.19 [`051e39b`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/051e39bb1c2e7f5d198bdb103d7c9b77a6300b94)

#### [1.4.18](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.4.17...1.4.18)

> 30 July 2023

- Release 1.4.18 [`15ed2db`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/15ed2db1e0e2e78c2c4c26cadf1373743eca3b95)
- Removed console log from workers list [`76142fe`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/76142fea4a3d14d8454ec4ec178845273f63a267)

#### [1.4.17](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.4.16...1.4.17)

> 28 July 2023

- TCH-14359 Removed the mocked worker list and added the orchestra API to fetch the worker list [`#36`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/36)
- Release 1.4.17 [`a179fcd`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/a179fcd2a6b82e479e78cc6fd71676dc3802bb0a)

#### [1.4.16](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.4.15...1.4.16)

> 18 July 2023

- Release 1.4.16 [`4969808`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/49698080083c332506fb82899ba83324f435a554)
- chore: core version bump [`fd794b0`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/fd794b081cb488581bdc1f873d9ce96d2ccaa9ac)

#### [1.4.15](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.4.14...1.4.15)

> 18 July 2023

- Feature/TCH-8553 failed ledger api update [`#35`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/35)
- Release 1.4.15 [`786a20f`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/786a20f5ac417bf251d710ce88e76882381da0e0)

#### [1.4.14](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.4.13...1.4.14)

> 16 July 2023

- Sorted worker based on end time in asending and added periodicity key [`#34`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/34)
- Release 1.4.14 [`d4f16cb`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/d4f16cbab43eca3c2c4d4b80614485893e36a540)

#### [1.4.13](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.4.12...1.4.13)

> 16 July 2023

- Feature(TCH-8553) failed ledger api update [`#33`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/33)
- Release 1.4.13 [`6c25aef`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/6c25aef8d2d41cd7988035ee95ce8b56261d3677)

#### [1.4.12](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.4.11...1.4.12)

> 16 July 2023

- feature(TCH-8553 send all details for failed ledgers [`#32`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/32)
- Release 1.4.12 [`90223f3`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/90223f3e565418abbcb54d99741059bbd15c99d0)
- fix: return default string [`479822c`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/479822c449c427ce2807a06c9d7666c8910f3f44)

#### [1.4.11](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.4.10...1.4.11)

> 15 July 2023

- Feature(TCH-11171) added failedat in the phase run api [`#31`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/31)
- Release 1.4.11 [`c46b55e`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/c46b55e0d4c5514e40947130619f1c3abe3dabca)

#### [1.4.10](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.4.9...1.4.10)

> 15 July 2023

- Feature(TCH-11171) phase cycle api update [`#30`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/30)
- Release 1.4.10 [`a220f90`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/a220f905b68eb76d91fc846eb24e12dd1e998aaa)

#### [1.4.9](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.4.8...1.4.9)

> 13 July 2023

- Feature(TCH-11171) fix: send correct phase response [`#29`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/29)
- Release 1.4.9 [`9354eec`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/9354eecd8cc1bb765daedd5dbcaabea486a0462b)

#### [1.4.8](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.4.7...1.4.8)

> 13 July 2023

- Release 1.4.8 [`2eb460e`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/2eb460ee907cf8394437ac49ca3720800ba463dc)
- chore: added mock test [`0999bbe`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/0999bbe48b165c8302843ac64111fe549116f948)
- Feat: added run id in worker list controller [`cf96d11`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/cf96d11db33b214213da97a0b0d488485ffc1f0a)

#### [1.4.7](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.4.6...1.4.7)

> 13 July 2023

- Release 1.4.7 [`8a54a8e`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/8a54a8e163403bfb2bdc03456e722c363593e607)

#### [1.4.6](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.4.5...1.4.6)

> 13 July 2023

- Release 1.4.6 [`4b64138`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/4b6413883532a0900d6cb28690673c717f9d0693)
- fix: build fix [`628a0fd`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/628a0fd67f5dded88740c2b5fbc1d66049af24f0)

#### [1.4.5](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.4.4...1.4.5)

> 13 July 2023

- feat(TCH-13212): added method for fetching eod task list with their variables [`#28`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/28)
- Release 1.4.5 [`5963011`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/5963011721f2106579e96c9874c6217a3a1e521d)

#### [1.4.4](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.4.3...1.4.4)

> 10 July 2023

- Worker Master list description update and added worker id in worker data [`0f31b28`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/0f31b28837cf08070a4a2ac02a1a773c67227911)
- Release 1.4.4 [`97adef0`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/97adef00f811efa69c8ead4283646fb80183297d)

#### [1.4.3](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.4.2...1.4.3)

> 10 July 2023

- Release 1.4.3 [`0cb54cd`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/0cb54cd9c9704c09ff28b57987b5c91af73be767)
- chore: package lock update [`65bcecc`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/65bcecc4a6ab2f39f1dadc498846a71c8f751583)

#### [1.4.2](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.4.1...1.4.2)

> 10 July 2023

- Release 1.4.2 [`a83dda2`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/a83dda28aa306d9673c8e10f6bf2568d746d01fd)

#### [1.4.1](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.4.0...1.4.1)

> 10 July 2023

- feature(TCH-11171): Show skipped ledgers as well for failed ledgers [`#27`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/27)
- Release 1.4.1 [`bb10620`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/bb10620e94aba1812fcd024cc2e1384392597a1c)

#### [1.4.0](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.3.5...1.4.0)

> 8 July 2023

- feature(TCH-11922): added a new controller for fetching all phase runs [`#26`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/26)
- Disabled test for batch run summary [`9af569b`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/9af569b37c132e4e8d1fc4c182f4c3cff3cc3066)
- Release 1.4.0 [`824460b`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/824460b474df3156e82c8c651ebbed6f97cb3859)
- Disabled test run on release [`24f14e7`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/24f14e708e16af37f940dbb153fba5b757443848)

#### [1.3.5](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.3.4...1.3.5)

> 7 July 2023

- fix: failed workers check added for empty orchestra runs [`#25`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/25)
- Release 1.3.5 [`d4b3e2c`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/d4b3e2c86dc09579986054bf931576fd9a6533b2)

#### [1.3.4](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.3.3...1.3.4)

> 6 July 2023

- feature(TCH-11171) updated phase-cycle api for start and end periodIDs [`#24`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/24)
- Release 1.3.4 [`256957e`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/256957e2be22bdb4ca7dca98394c58e6fa109cd8)
- fix: remove phase cycle test [`cff859e`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/cff859eacebcad709399fab85f0fd9fffdbfe3f0)

#### [1.3.3](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.3.1...1.3.3)

> 6 July 2023

- feat: skip ledgers period id sent in body [`#23`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/23)
- Release 1.3.3 [`171feb4`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/171feb4b25123739bbe2eb5b04ef10cec9760ea4)
- fix: unit test fix [`a682e75`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/a682e757bb1d1881cc949fe6f40f377e8a442437)
- chore: version update [`6c419b3`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/6c419b39362e5ab140f373a5b0738bd16188b818)

#### [1.3.1](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.3.0...1.3.1)

> 6 July 2023

- Release 1.3.1 [`9149e8f`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/9149e8f315c1a8eee3ba930b8f750f796e8e6020)
- chore: build fix [`863faf7`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/863faf76b7af0176dcc470241b7fb0771ec5c9bd)

#### [1.3.0](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.2.3...1.3.0)

> 6 July 2023

- feat : API added to fetch failed workers for specific phase [`#22`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/22)
- Release 1.3.0 [`092a09e`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/092a09e9da0969cf5c4c5418507057a68ba872c8)

#### [1.2.3](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.2.2...1.2.3)

> 26 June 2023

- feat(TCH-10906) - skip ledgers API response modified [`#21`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/21)
- Changed CLOSED to EOP in worker master list and updated the description [`cfa0bfa`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/cfa0bfa6593cbed0c0f15a75f6e14746238d27d4)
- Release 1.2.3 [`6ce83a6`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/6ce83a6219867fe439473621c5ba809c01b28d90)
- fix: skipping unit test case for release [`d9e5b0d`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/d9e5b0d19eb319e431734003201c11fd2a6e884a)

#### [1.2.2](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.2.0...1.2.2)

> 19 June 2023

- Release 1.2.2 [`d7bdb28`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/d7bdb282e69c05e7c1b89709f328d78e6e09b061)
- chore: updated package.json [`152f4ac`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/152f4ac080cb8dcfe19ec8b9d84ecb87771adcb4)

#### [1.2.0](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.1.9...1.2.0)

> 19 June 2023

- feat(TCH-10906) - skipped ledgers api added [`#19`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/19)
- Release 1.2.0 [`0797bb6`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/0797bb6b9324a8c2c68433a78b8158f742d5ecbd)
- chore: unit test fix [`9bec5b3`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/9bec5b3c3c3f377e890805f1a2b8ce15391c2728)
- chore: unit test fix [`8f34dd0`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/8f34dd056d813ca40a070883c7223947112f024d)

#### [1.1.9](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.1.7...1.1.9)

> 13 June 2023

- Release 1.1.9 [`6bc9c00`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/6bc9c00179a965136981e8307d2847e6b8e08e04)
- fix: Version update [`7c2747e`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/7c2747e6080c5ec09c036a3c297c70f25d61044b)

#### [1.1.7](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.1.6...1.1.7)

> 13 June 2023

- Release 1.1.7 [`fdb9b11`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/fdb9b1114681a57077ad38088df4336713fcce58)
- fix: build fail fix [`991aa0a`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/991aa0a608a19b546bc85bdbaba8a84004e50b37)

#### [1.1.6](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.1.5...1.1.6)

> 13 June 2023

- Feat/TCH-10907 skip ledgers form [`#17`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/17)
- Release 1.1.6 [`79b174d`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/79b174daef6351d9406c48d15e3b076d4faa2b99)
- fix: test case fix [`27496bb`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/27496bbb459068c1b1f65fd76d64168440295a45)

#### [1.1.5](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.1.4...1.1.5)

> 13 June 2023

- Release 1.1.5 [`37db384`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/37db384692a1301ef2f27bd05bd2fa010a9e3a8c)

#### [1.1.4](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.1.3...1.1.4)

> 12 June 2023

- feature(TCH-8553): eod center display failed ledger [`#18`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/18)
- Release 1.1.4 [`eb5cdc0`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/eb5cdc08f1d22b56d856d2272b682c0ef3a71353)
- remove coverage from git [`d381930`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/d3819309c40ac19b8c2273e2ca1c11ee6503e6e5)
- add coverage folder to gitignore [`c81e567`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/c81e567d73b4e098aa5d600ecbb8f5c95176ea6c)

#### [1.1.3](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.1.2...1.1.3)

> 6 June 2023

- Feat/TCH-9457 [`#16`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/16)
- Release 1.1.3 [`abc1d05`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/abc1d0564f4eb4a42787db57be7113a8add46a89)

#### [1.1.2](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.1.1...1.1.2)

> 6 June 2023

- feat/TCH-9457 Added worker master list [`#14`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/14)
- Added new worker to worker master list [`879c9ce`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/879c9cefb8d9fcece89d8c723c2a5904c29be062)
- Release 1.1.2 [`8ced0ee`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/8ced0ee66e6a45455c492f41ee6e765a3431d141)
- Updated coverage summary [`973bed5`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/973bed5cf3dc27a22d77e17a6928d830db3e86bb)

#### [1.1.1](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.1.0...1.1.1)

> 5 June 2023

- Release 1.1.1 [`87b4c4e`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/87b4c4ee79be906f3d11d0d524a0d0f5e53686ca)
- updated node version in jenkins file [`089dee1`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/089dee1b99dcda5ca47130d84d441c970f0960f9)

#### [1.1.0](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.0.16...1.1.0)

> 5 June 2023

- feature(TCH-9458): added method for fetching all periods for a given calendar [`#13`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/13)
- pushed coverage summary [`9903605`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/99036054547ee698d5f421c5f51f3b9c3303bd82)
- Release 1.1.0 [`261f5c9`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/261f5c9d4893da0948e3f81f312b78f1a9189667)

#### [1.0.16](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.0.14...1.0.16)

> 26 April 2023

- Release 1.0.16 [`5ef511b`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/5ef511b7e5684bade915d60323b6cc5a3e5b6913)
- refresh release [`f543614`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/f5436145a7fc8e3ec1aff1ac6608d78ec4c137b8)

#### [1.0.14](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.0.13...1.0.14)

> 26 April 2023

- feat(TRPW-3729) - worker details API added extra fields in response [`#12`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/12)
- Release 1.0.14 [`2e3e6bf`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/2e3e6bf23b821a45787b570ff868f172c2c46479)

#### [1.0.13](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.0.12...1.0.13)

> 24 April 2023

- Release 1.0.13 [`02c3551`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/02c3551a0361a86433dc1223deadd90706874cce)
- refresh build [`d838da9`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/d838da9f9073441d6591b3950a15b3ba8dd7d984)

#### [1.0.12](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.0.11...1.0.12)

> 24 April 2023

- Release 1.0.12 [`34ed810`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/34ed810418e3b73f6f02e53a28349a049eb5a4dc)
- Build fix [`6b21b8d`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/6b21b8d95fba00310d9385a9f33c46160d68e8e7)

#### [1.0.11](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.0.10...1.0.11)

> 24 April 2023

- feat(TRPW-3729) - Handling empty reponse [`#11`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/11)
- fix(TRPW-3729) - worker details api route changed [`#10`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/10)
- Release 1.0.11 [`f278a4c`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/f278a4c7009fd9b96e0c0240f62647cf3360dc98)

#### [1.0.10](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.0.9...1.0.10)

> 17 April 2023

- feat(TRPW-3729) - worker details widget api implementation [`#9`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/9)
- Release 1.0.10 [`7fe0529`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/7fe05293bfc65b9e172bd1ad7a378d154e2c00b3)
- added coverage [`fa90a29`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/fa90a29d99e7922974b6cfe596201e7cf485d279)
- build fix [`c3e7cc9`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/c3e7cc94b23aa107193ae96aad18065fd07af76d)

#### [1.0.9](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.0.8...1.0.9)

> 10 April 2023

- Release 1.0.9 [`6c6cb44`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/6c6cb448cabb709f77679255af1910677442bf3b)
- minor commit to re-release [`dd8782a`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/dd8782a52752d780b78a1399665fce1ee9e7669f)

#### [1.0.8](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.0.7...1.0.8)

> 10 April 2023

- other(TRPW-3685): added additional data for worker summary and updated tests [`#8`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/8)
- Release 1.0.8 [`cb7191a`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/cb7191a1f21b2cefa88ef00e5d8b731b1a3f36f5)
- updated test [`a970913`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/a97091306dfd36b6667894fe701cfdf2f751dc85)

#### [1.0.7](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.0.6...1.0.7)

> 7 April 2023

- bugfix(TRPW-3685): fix status issues [`bf1c51b`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/bf1c51b7470042aef16d1b8b8054f329e2f4ca26)
- Release 1.0.7 [`71411b2`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/71411b2af4a1bf6c5b080d88069b1a30db8e430f)

#### [1.0.6](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.0.5...1.0.6)

> 7 April 2023

- Release 1.0.6 [`ae49325`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/ae49325d80bc25baa46bd26b357176494889c9ac)
- other(TRPW-3685): fixed end time logic of recent coa phase [`702b6db`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/702b6db4cc03ff08ad9870700666f546ddb7bde2)

#### [1.0.5](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.0.4...1.0.5)

> 7 April 2023

- Release 1.0.5 [`9ca5caa`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/9ca5caa226e6858f5fbef3db84f66dd06d3611d7)
- other(TRPW-3685): code sanity [`fa2eb83`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/fa2eb83ae02b690f608e45442356840ee1378550)
- other(TRPW-3685): updated code coverage json [`f7c4ecb`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/f7c4ecb5012e35b0ac73bbeacf34e6eed2e06196)

#### [1.0.4](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.0.3...1.0.4)

> 7 April 2023

- other(TRPW-3685): error handling on util function [`#7`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/7)
- Release 1.0.4 [`d7b1424`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/d7b1424165913aa54a2a6ed31494460a742ec6b2)
- update coverage [`6754df1`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/6754df1432fba32287048f4c953a49fb635670d6)

#### [1.0.3](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.0.2...1.0.3)

> 6 April 2023

- other(TRPW-3658): fixes on atalanta orchestra [`#6`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/6)
- Release 1.0.3 [`e4fd61a`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/e4fd61a3f9261e7f8a538496dc11d88fa7c9b4c8)

#### [1.0.2](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.0.1...1.0.2)

> 5 April 2023

- other: fix error caused at aggregator build script [`#5`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/5)
- Release 1.0.2 [`6a57828`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/6a57828d5e779ba6827ce96bdf8dccca58219d7b)

#### [1.0.1](https://github.com/Zeta-Enterprise/atalanta-orchestra/compare/1.0.0...1.0.1)

> 5 April 2023

- other(TRPW-3685): added name and changelog to package.json [`#4`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/4)
- Release 1.0.1 [`a48e773`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/a48e7737f1178692dd43039cf005b0ce6e30f866)

#### 1.0.0

> 5 April 2023

- other(TRPW-3685): added unit tests for batch run widget service layer [`#3`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/3)
- feat(TRPW-3685): added service layer for worker summary [`#2`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/2)
- feat(TRPW-3685): added service layer for orchestra phase cycle widget [`#1`](https://github.com/Zeta-Enterprise/atalanta-orchestra/pull/1)
- Project setup [`812c3cc`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/812c3cc63f561d1b85b5c6c4c82df09116e333ba)
- Release 1.0.0 [`626b771`](https://github.com/Zeta-Enterprise/atalanta-orchestra/commit/626b77147f7628a69edfd5a630df290bd8027540)
