import { getMockReq, getMockRes } from '@jest-mock/express';
import { StatusCodes } from 'http-status-codes';
import { mockOrchestraRunData } from '../mocks/batch-run-summary';
import { workersListMockResponse } from '../mocks/workers-list';
import { getWorkersList } from '../src/controllers/WorkerController';

describe('Worker List Controller Test suite', () => {
    let mockRequest = {
        params: { tenantId: '140793' },
        query: {},
    };
    /* TODO: We need to fix this unit test @ashish */
    test.skip('it should return 200 with data when request is successful', async () => {
        const request = getMockReq({ query: { ...mockRequest.query }, params: mockRequest.params });
        const { res } = getMockRes();
        await getWorkersList(request, res);
        expect(res.status).toHaveBeenCalledWith(StatusCodes.OK);
        expect(res.send).toHaveBeenCalledWith(expect.objectContaining(workersListMockResponse));
    });
});
