import {
    getCoASummaryData,
    getPhaseCycleData,
    getWorkerSummaryData,
} from '../src/controllers/BatchRunSummaryController';
import {
    coaSummaryMockData,
    mockOrchestraRunData,
    mockPhaseTriggerDataBOFI,
    mockPhaseTriggerDataBOPI,
    phaseCycleMockData,
    workerSummaryMockData,
} from '../mocks/batch-run-summary';

const request = {
    params: {
        tenantId: '151613',
        periodId: '776295113466452341',
    },
    query: {
        coas: '7242184752828075514',
        phase: 'BOFI',
    },
    headers: {
        authorization: '',
    },
    getLogger: () => ({ info: jest.fn(), error: jest.fn() }),
};
const response = {
    status: () => ({ send: (value: unknown) => value }),
};

const next = jest.fn();
jest.mock('@zeta-atalanta/core/dist/services/OrchestraService', () => {
    class OrchestraService {
        constructor() {}
        getOrchestraRunData() {
            return new Promise((resolve) => {
                resolve(mockOrchestraRunData);
            });
        }
    }

    return { OrchestraService };
});
jest.mock('@zeta-atalanta/core/dist/services/AuraService', () => {
    class AuraService {
        constructor() {}
        getPhaseTriggerStatus(requestObj: { triggerValue: string }) {
            return new Promise((resolve) => {
                resolve(
                    requestObj.triggerValue === 'initiateBOF' ? mockPhaseTriggerDataBOFI : mockPhaseTriggerDataBOPI,
                );
            });
        }
    }

    return { AuraService };
});
describe('mock test', () => {
    //mock test
    test('mock test', async () => {
        expect('mock').toBeTruthy();
    });
});
// describe('Batch Run Summary test suite', () => {
//     beforeEach(() => {
//         jest.clearAllMocks();
//     });
//     test('getCoASummaryData unit test', async () => {
//         const apiResponse = await getCoASummaryData(request as never, response as never, next);
//         expect((apiResponse as any).data).toMatchObject(coaSummaryMockData);
//     });
//     test('getWorkerSummaryData unit test', async () => {
//         const apiResponse = await getWorkerSummaryData(request as never, response as never, next);
//         expect((apiResponse as any).data).toMatchObject(workerSummaryMockData);
//     });
// });
