import { getMockReq, getMockRes } from '@jest-mock/express';
import { StatusCodes } from 'http-status-codes';
import { mockResponse, mockRequestBody } from '../mocks/skip-ledgers-form';
import { skipLedgers } from '../src/controllers/SkipLegdersController';

jest.mock('@zeta-atalanta/core/dist/services/OrchestraService', () => {
    class OrchestraService {
        constructor() {}
        skipLedgersForSingleWorker() {
            return new Promise((resolve) => {
                resolve(mockResponse);
            });
        }
        skipLedgersForAllWorkers() {
            return new Promise((resolve) => {
                resolve(mockResponse);
            });
        }
    }

    return { OrchestraService };
});
describe('Skip Ledgers Controller Test suite', () => {
    let mockRequest = {
        params: { tenantId: '140793', periodId: '456' },
        body: mockRequestBody,
    };
    test('it should return 400 error if any mandatory query param is missing', async () => {
        const request = getMockReq({ params: { ...mockRequest.params, tenantId: '' }, body: mockRequest.body });
        const { res } = getMockRes();
        await skipLedgers(request, res);
        expect(res.status).toHaveBeenCalledWith(StatusCodes.BAD_REQUEST);
    });

    test('it should return 200 with data when request is successful', async () => {
        const request = getMockReq({ params: { ...mockRequest.params }, body: mockRequest.body });
        const { res } = getMockRes();
        await skipLedgers(request, res);
        expect(res.status).toHaveBeenCalledWith(StatusCodes.OK);
        expect(res.send).toHaveBeenCalledWith(
            expect.objectContaining({ ...mockResponse, ledgerIdsToBeSkipped: mockRequestBody.ledgerIds }),
        );
    });
});
