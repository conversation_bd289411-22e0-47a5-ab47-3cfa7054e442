import { getAllPeriods } from '../src/controllers/OrchestraPeriodsController';
import { mockAllClocks, mockAllCycles, mockAllPeriods, mockAllPeriodsResponse } from '../mocks/all-periods';
import { StatusCodes } from 'http-status-codes';

let mockAllClocksCopy = [...mockAllClocks];

const request = {
    params: {
        tenantId: '140793',
        calendarId: '1751072138966405248',
    },
    headers: {
        authorization: '',
    },
    getLogger: () => ({ info: jest.fn(), error: jest.fn() }),
};
const response = {
    status: () => ({ send: (value: unknown) => value }),
};

const next = jest.fn();
jest.mock('@zeta-atalanta/core/dist/services/TachyonService', () => {
    class TachyonService {
        constructor() {}
        getClockList() {
            return new Promise((resolve) => {
                resolve(mockAllClocksCopy);
            });
        }
        getCycleList() {
            return new Promise((resolve) => {
                resolve(mockAllCycles);
            });
        }
        getPeriodList() {
            return new Promise((resolve) => {
                resolve(mockAllPeriods);
            });
        }
    }

    return { TachyonService };
});
describe('Orchestra Get All Periods test suite', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });
    test('getAllPeriods successful scenario', async () => {
        const apiResponse = await getAllPeriods(request as never, response as never, next);
        expect((apiResponse as any).data).toMatchObject(mockAllPeriodsResponse.data);
    });

    test('getAllPeriods error scenario', async () => {
        mockAllClocksCopy = [];
        const apiResponse = await getAllPeriods(request as never, response as never, next);
        expect(apiResponse.status).toBe(StatusCodes.INTERNAL_SERVER_ERROR);
    });
});
