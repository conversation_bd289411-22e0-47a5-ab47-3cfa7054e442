import { getMockReq, getMockRes } from '@jest-mock/express';
import { StatusCodes } from 'http-status-codes';
import { mockOrchestraRunData } from '../mocks/batch-run-summary';
import { workerDetailsMockResponse } from '../mocks/worker-details';
import { getWorkerDetails } from '../src/controllers/WorkerDetailsController';

jest.mock('@zeta-atalanta/core/dist/services/OrchestraService', () => {
    class OrchestraService {
        constructor() {}
        getOrchestraRunData() {
            return new Promise((resolve) => {
                resolve(mockOrchestraRunData);
            });
        }
    }

    return { OrchestraService };
});
describe('Worker Details Controller Test suite', () => {
    let mockRequest = {
        params: { tenantId: '140793' },
        query: { phaseId: 'BOPI', cbuId: '6072745572862733862', coaId: '3688973394508412345' },
    };
    test('it should return 400 error if any mandatory query param is missing', async () => {
        const request = getMockReq({ query: { ...mockRequest.query, coaId: '' }, params: mockRequest.params });
        const { res } = getMockRes();
        await getWorkerDetails(request, res);
        expect(res.status).toHaveBeenCalledWith(StatusCodes.BAD_REQUEST);
    });

    test('it should return 200 with data when request is successful', async () => {
        const request = getMockReq({ query: { ...mockRequest.query }, params: mockRequest.params });
        const { res } = getMockRes();
        await getWorkerDetails(request, res);
        expect(res.status).toHaveBeenCalledWith(StatusCodes.OK);
        expect(res.send).toHaveBeenCalledWith(expect.objectContaining(workerDetailsMockResponse));
    });
});
