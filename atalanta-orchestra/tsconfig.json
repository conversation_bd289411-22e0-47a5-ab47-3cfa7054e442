{"compilerOptions": {"module": "commonjs", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "target": "es2020", "noImplicitAny": true, "moduleResolution": "node", "sourceMap": true, "outDir": "dist", "baseUrl": ".", "emitDecoratorMetadata": true, "experimentalDecorators": true, "paths": {"*": ["node_modules/*", "src/types/*"], "@/*": ["src/*"]}}, "include": ["src/**/*", "tests"], "exclude": ["node_modules", "**/*.test.ts", "logs"]}