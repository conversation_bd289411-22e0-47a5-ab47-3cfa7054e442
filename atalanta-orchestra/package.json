{"name": "@zeta-atalanta/orchestra", "version": "1.11.9", "description": "BFF layer for orchestra observability and EoD center", "prettier": "@zeta/prettier-config", "main": "dist/index.js", "types": "dist/index.d.ts", "sideEffects": false, "files": ["dist", "converage"], "scripts": {"prebuild": "rm -rf dist", "build": "node tsconfig.js && tsc -p tsconfig.json", "postbuild": "tsc --declaration --emitDeclarationOnly", "start": "node dist/server.js", "dev": "nodemon", "format": "prettier './**/*.(ts|js|json|html)' --write", "release": "release-it", "prepare": "husky install", "test": "jest"}, "dependencies": {"@zeta-atalanta/core": "1.6.5", "ajv": "^8.11.0", "axios": "1.6.4", "body-parser": "^1.19.0", "compression": "^1.7.4", "cors": "^2.8.5", "dayjs": "^1.11.5", "dot-prop": "^6.0.1", "dotenv-extended": "^2.9.0", "ejs": "^3.1.5", "email-validator": "^2.0.4", "express": "^4.17.1", "express-sse": "0.5.3", "http-status-codes": "^2.1.4", "js-yaml": "^4.1.0", "json-bigint": "1.0.0", "lodash": "^4.17.21", "lru-cache": "^7.14.1"}, "devDependencies": {"@jest-mock/express": "^2.0.1", "@types/compression": "^1.7.2", "@types/cors": "^2.8.12", "@types/ejs": "^3.0.5", "@types/express": "^4.17.11", "@types/jest": "^29.2.6", "@types/js-yaml": "^4.0.5", "@types/json-bigint": "^1.0.1", "@types/module-alias": "^2.0.0", "@types/node": "^14.14.22", "@zeta/prettier-config": "^1.0.0", "husky": "^8.0.1", "jest": "^29.4.0", "lint-staged": "^12.4.2", "module-alias": "^2.2.2", "nodemon": "^2.0.7", "prettier": "^2.6.2", "release-it": "^15.5.0", "ts-jest": "^29.0.5", "typescript": "^4.1.3"}, "release-it": {"npm": {"publish": false}, "git": {"changelog": "npx auto-changelog --stdout --commit-limit false -u --template https://raw.githubusercontent.com/release-it/release-it/master/templates/changelog-compact.hbs"}, "hooks": {"after:bump": "npx auto-changelog -p"}}, "lint-staged": {"*.*(ts|js|json|html)": "npm run format"}}