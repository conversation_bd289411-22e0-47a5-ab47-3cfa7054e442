export const REPORT_TYPE = {
    DAILY: 'Daily',
    WEEKLY: 'Weekly',
    MONTHLY: 'Monthly',
};

export const DATE_TIME_FORMAT = {
    DD_MMM_YYYY_h_mm_ss_A: 'DD MMM YYYY, h:mm:ss A',
    DD_MMM_YYY: 'DD MMM YYYY',
    YYYY_MM_DD: 'YYYY-MM-DD',
    YYYY_MM_DDTHH_mm_ssZ: 'YYYY-MM-DDTHH:mm:ssZ',
};

export const PHASE_RUN_INFO_KEYS = {
    ORCHESTRA_RUNS: 'orchestraRuns',
    PHASE: 'phase',
    START_TIME: 'startTime',
    END_TIME: 'endTime',
    LOCAL_START_TIME: 'localStartTime',
    LOCAL_END_TIME: 'localEndTime',
    FAILED_RUNS: 'failedRuns',
    TOTAL_EXECUTION_TIME: 'totalExecutionTime',
    TOTAL_EXECUTION_TIME_IN_MS: 'totalExecutionTimeInMs',
} as const;

export const ORCHESTRA_RUN_INFO_KEYS = {
    BOOK_DATE: 'bookDate',
    BOOK_DATE_IN_TS: 'bookDateInTs',
    START_TIME: 'startTime',
    END_TIME: 'endTime',
    LOCAL_START_TIME: 'localStartTime',
    LOCAL_END_TIME: 'localEndTime',
    TOTAL_EXECUTION_TIME: 'totalExecutionTime',
    TOTAL_EXECUTION_TIME_IN_MS: 'totalExecutionTimeInMs',
    CUTOFF_TIME: 'cutoffTime',
    CUTOFF_EXECUTION_METHOD: 'cutoffExecutionMethod',
    START_TIME_IN_TS: 'startTimeInTs',
    END_TIME_IN_TS: 'endTimeInTs',
};

export const PONTUS_DAG_REPORT_KEYS = {
    START_TIME: 'startTime',
    END_TIME: 'endTime',
    LOCAL_START_TIME: 'localStartTime',
    LOCAL_END_TIME: 'localEndTime',
    START_TIME_IN_TS: 'startTimeInTs',
    END_TIME_IN_TS: 'endTimeInTs',
    TOTAL_EXECUTION_TIME: 'totalExecutionTime',
    TOTAL_EXECUTION_TIME_IN_MS: 'totalExecutionTimeInMs',
};

export const EXTRACT_DAG_REPORT_KEYS = {
    START_TIME: 'startTime',
    END_TIME: 'endTime',
    LOCAL_START_TIME: 'localStartTime',
    LOCAL_END_TIME: 'localEndTime',
    START_TIME_IN_TS: 'startTimeInTs',
    END_TIME_IN_TS: 'endTimeInTs',
    TOTAL_EXECUTION_TIME: 'totalExecutionTime',
    TOTAL_EXECUTION_TIME_IN_MS: 'totalExecutionTimeInMs',
};

export const PHASE_RUN_KEYS = {
    STARTED_AT: 'startedAt',
    FINISHED_AT: 'finishedAt',
    CBU_START_DATE: 'cbuStartDate',
};

export const MIS_ORCHESTRA_PHASES_MAP = {
    EOPI: 'EOPI',
    EOFI: 'EOFI',
    CLOSED: 'CLOSED',
    BOPI: 'BOPI',
    BOFI: 'BOFI',
} as const;

export const MIS_ORCHESTRA_PHASES = Object.keys(MIS_ORCHESTRA_PHASES_MAP);
export const MIS_ORCHESTRA_PHASES_FOR_UI = ['EOPI', 'EOFI', 'EOP', 'BOPI', 'BOFI'];
export const MIS_ORCHESTRA_START_PHASES = ['EOPI', 'EOFI', 'CLOSED'];
export const MIS_ORCHESTRA_END_PHASES = ['BOPI', 'BOFI'];
export const PHASES_AFTER_BOFI_COMPLETION = ['ACTIVE', ...MIS_ORCHESTRA_START_PHASES];

export const PONTUS_CODES = {
    START_TASK_CODE: 'PONTUS_DAG_START',
    END_TASK_CODE: 'PONTUS_DAG_END',
    START_KPI_CODE: 'dag_start_time',
    END_KPI_CODE: 'dag_end_time',
    STATUS_VERIFIER_CODE: 'PONTUS_CHECKLIST_ITEM_V2',
};
export const EXTRACTS_CODES = {
    TASK_CODE: 'EXTRACTS_DAG_COMPLETED',
    START_KPI_CODE: 'extracts_start_time',
    END_KPI_CODE: 'extracts_end_time',
    STATUS_VERIFIER_CODE: 'EXTRACTS_CHECKLIST_ITEM_V2',
};