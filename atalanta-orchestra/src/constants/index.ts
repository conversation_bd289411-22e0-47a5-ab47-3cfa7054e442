import { CBUStatusTrigger, WORKFLOW_COMPLETION_TYPE } from '../types';
export const PHASES_MAP = {
    ACTIVE: 'ACTIVE',
    EOPI: 'EOPI',
    EOFI: 'EOFI',
    CLOSED: 'CLOSED',
    BOPI: 'BOPI',
    BOFI: 'BOFI',
} as const;
export const PHASES = ['ACTIVE', 'EOPI', 'EOFI', 'CLOSED', 'BOPI', 'BOFI'];
export const PHASES_START = ['ACTIVE', 'EOPI', 'EOFI', 'CLOSED'];
export const PHASES_END = ['BOPI', 'BOFI'];
export const CLOSED_PHASE_LABEL = 'EOP';
export const CLOSED_PHASE = 'CLOSED';
export const PHASE_START_STATUS = 'ACTIVE';
export const CBU_STATUS_TRIGGER_MAP: CBUStatusTrigger = {
    INACTIVE: {
        currentTrigger: null,
        nextTrigger: 'initiateBOP',
        nextPhase: 'BOPI',
    },
    BOPI: {
        currentTrigger: 'initiateBOP',
        nextTrigger: 'initiateBOF',
        nextPhase: 'BOFI',
    },
    BOFI: {
        currentTrigger: 'initiateBOF',
        nextTrigger: 'activate',
        nextPhase: 'ACTIVE',
    },
    ACTIVE: {
        currentTrigger: 'activate',
        nextTrigger: 'initiateEOP',
        nextPhase: 'EOPI',
    },
    EOPI: {
        currentTrigger: 'initiateEOP',
        nextTrigger: 'initiateEOF',
        nextPhase: 'EOFI',
    },
    EOFI: {
        currentTrigger: 'initiateEOF',
        nextTrigger: 'close',
        nextPhase: 'CLOSED',
    },
    CLOSED: {
        currentTrigger: 'close',
        nextTrigger: 'initiateBOP', //For next period
        nextPhase: 'BOPI',
    },
};
export const STATUS = {
    SUCCEEDED: 'SUCCEEDED',
    FAILED: 'FAILED',
    FAILED_RETRIED: 'FAILED_RETRIED',
    COMPLETED: 'COMPLETED',
    SUCCESS: 'SUCCESS',
    PROGRESS: 'PROGRESS',
};
export const ERROR_MESSAGE_INVALID_PARAMS = 'Invalid or missing parameters:';
/**
 * Default page size has been set in aura backend, if more than
 * 51, then we get the below error:
 * {
    "type": "BindException",
    "message": "argument not valid",
    "fieldErrors": [
        {
            "objectName": "orchestraRuns",
            "field": "pageSize",
            "message": "Max Page size allowed is 50"
        }
    ]
 * }
 */
export const DEFAULT_PAGE_SIZE = 50;
export const LEDGER_ENTITY = 'ledger';
export const DEFAULT_ENTITY_SORTBY = 'status';
export const DEFAULT_SORT_ORDER = 'desc';
export const STATUS_FAILURE = 'FAILURE';
export const STATUS_SKIPPED = 'SKIPPED';
export const USER_TASK_STATUS = {
    COMPLETED: 'completed',
    CREATED: 'created',
}

export const BUSINESS_KEY_STATUS = {
    COMPLETED: 10,
    FAILED: 1,
    IN_PROGRESS: 0,
}

export const BUSINESS_KEY_STATUS_MAP: {
    [key: number]: WORKFLOW_COMPLETION_TYPE;
} = {
    10: 'COMPLETED',
    1: 'FAILED',
    0: 'IN_PROGRESS',
}