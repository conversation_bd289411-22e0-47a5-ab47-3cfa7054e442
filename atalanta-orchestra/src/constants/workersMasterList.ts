export const WORKERS_MASTER_LIST = [{
  dependsOn: [],
  description: "dataPreparationWorker",
  name: "dataPreparationWorker",
  periodicities: ["DAILY"],
  phases: ["BOPI", "BOFI", "EOPI", "EOFI", "CLOSED"],
  status: "ACTIVE"
}, {
  dependsOn: ["dataPreparationWorker"],
  description: "couponProcessingWorker",
  name: "couponProcessingWorker",
  periodicities: ["DAILY"],
  phases: ["EOPI", "EOFI"],
  status: "ACTIVE"
}, {
  dependsOn: ["couponProcessingWorker"],
  description: "dataPreparationWorker-1",
  name: "dataPreparationWorker-1",
  periodicities: ["DAILY"],
  phases: ["EOPI", "EOFI"],
  status: "ACTIVE"
}, {
  dependsOn: ["dataPreparationWorker-1"],
  description: "installmentbillingworkerv2",
  name: "installmentbillingworkerv2",
  periodicities: ["DAILY"],
  phases: ["EOPI"],
  status: "ACTIVE"
}, {
  dependsOn: ["dataPreparationWorker"],
  description: "principalliquidationworkerv",
  name: "principalliquidationworkerv2",
  periodicities: ["DAILY"],
  phases: ["BOPI"],
  status: "ACTIVE"
}, {
  dependsOn: ["dataPreparationWorker"],
  description: "loanrepaymentworkerv2",
  name: "loanrepaymentworkerv2",
  periodicities: ["DAILY"],
  phases: ["BOFI"],
  status: "ACTIVE"
}, {
  dependsOn: ["dataPreparationWorker"],
  description: "loanmaturityworkerv2",
  name: "loanmaturityworkerv2",
  periodicities: ["DAILY"],
  phases: ["CLOSED"],
  status: "ACTIVE"
}, {
  dependsOn: ["dataPreparationWorker"],
  description: "couponexpiryworkerv2",
  name: "couponexpiryworkerv2",
  periodicities: ["DAILY"],
  phases: ["BOPI"],
  status: "ACTIVE"
}, {
  dependsOn: ["dataPreparationWorker-1"],
  description: "interestliquidationworkerv2",
  name: "interestliquidationworkerv2",
  periodicities: ["WEEKLY", "MONTHLY"],
  phases: ["EOPI"],
  status: "ACTIVE"
}, {
  dependsOn: ["dataPreparationWorker-1"],
  description: "rubyfeeworker",
  name: "rubyfeeworker",
  periodicities: ["DAILY"],
  phases: ["EOPI"],
  status: "ACTIVE"
}, {
  dependsOn: ["dataPreparationWorker-1"],
  description: "rubyendofdayworker",
  name: "rubyendofdayworker",
  periodicities: ["DAILY"],
  phases: ["EOFI"],
  status: "ACTIVE"
}, {
  dependsOn: ["dataPreparationWorker-1"],
  description: "rubydormancyworker",
  name: "rubydormancyworker",
  periodicities: ["DAILY"],
  phases: ["EOFI"],
  status: "ACTIVE"
}, {
  dependsOn: ["dataPreparationWorker"],
  description: "rubypostbillingworker",
  name: "rubypostbillingworker",
  periodicities: ["WEEKLY", "MONTHLY"],
  phases: ["CLOSED"],
  status: "ACTIVE"
}, {
  dependsOn: ["dataPreparationWorker"],
  description: "balanceworker",
  name: "balanceworker",
  periodicities: ["DAILY", "WEEKLY", "MONTHLY"],
  phases: ["CLOSED"],
  status: "ACTIVE"
}, {
  dependsOn: ["dataPreparationWorker"],
  description: "rubyexcesscreditworker",
  name: "rubyexcesscreditworker",
  periodicities: ["WEEKLY", "MONTHLY"],
  phases: ["BOFI"],
  status: "ACTIVE"
}]