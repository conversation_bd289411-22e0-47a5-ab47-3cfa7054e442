import { Request, Response } from 'express';
import { StatusCodes } from 'http-status-codes';
import { OrchestraService } from '@zeta-atalanta/core/dist/services/OrchestraService';

import { InternalAPIException } from '@zeta-atalanta/core/dist/exceptions/InternalAPIException';
import { SkipLegdersRequest } from '../types';
import { ALL_WORKER, PHASE, SKIP_LEDGERS_PAYLOAD } from '../utils';

export const skipLedgers = async (request: Request, response: Response) => {
    const {
        params: { tenantId = '' } = {},
        headers: { authorization: token = '' } = {},
        body: { phase, ledgerIds = [], worker = '', applyFor = '', coaId = '', periodId = '' },
    } = request;

    const orchestraService = new OrchestraService(request);

    if (applyFor === PHASE) {
        try {
            if (tenantId && ledgerIds.length && phase && periodId && coaId) {
                const orchestraResponse = await Promise.all(
                    ledgerIds.map((ledgerId: string) => {
                        const request: SkipLegdersRequest = {
                            tenantId,
                            coaId,
                            periodId,
                            phase,
                            ledgerId,
                            worker,
                            token,
                        };

                        if (worker === ALL_WORKER) {
                            delete request['worker'];
                            return orchestraService.skipLedgersForAllWorkers(request, SKIP_LEDGERS_PAYLOAD);
                        } else {
                            return orchestraService.skipLedgersForSingleWorker(request, SKIP_LEDGERS_PAYLOAD);
                        }
                    }),
                );

                return response
                    .status(StatusCodes.OK)
                    .send({ response: 'OK', ledgerIdsToBeSkipped: ledgerIds, orchestraResponse });
            } else {
                throw new InternalAPIException({
                    api: 'skipLedgers',
                    error: {
                        statusCode : StatusCodes.BAD_REQUEST,
                        response: {
                            status: StatusCodes.BAD_REQUEST,
                            data: { message: 'Payload is missing required fields' },
                        },
                    },
                });
            }
        } catch (error) {
            console.log(error);
            // If Backend error
            if (error instanceof InternalAPIException) {
                return response.status(error.statusCode).send(error.response);
            }
            // In case of other javascript errors
            return response.status(StatusCodes.INTERNAL_SERVER_ERROR).send({ error: error.message });
        }
    } else {
        // temporary check
        return response.status(StatusCodes.INTERNAL_SERVER_ERROR).send({ error: 'API support not available' });
    }
};
