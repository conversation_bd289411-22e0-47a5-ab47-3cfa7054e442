import { Request, Response } from 'express';
import { StatusCodes } from 'http-status-codes';
import { InternalAPIException } from '@zeta-atalanta/core/dist/exceptions/InternalAPIException';
import { CalendarService } from '../services/CalendarService';

const handleRequest = async (request: Request, response: Response, serviceMethod: string) => {
    try {
        const calendarService = new CalendarService(request);
        const res = await (calendarService[serviceMethod as keyof CalendarService] as Function).call(
            calendarService,
        );
        return response.status(StatusCodes.OK).send(res);
    } catch (error) {
        request.getLogger(__filename).error(`API Failed path: ${serviceMethod}`, error);

        if (error instanceof InternalAPIException) {
            return response.status(error.statusCode).send(error);
        }

        return response
            .status(StatusCodes.INTERNAL_SERVER_ERROR)
            .send({ api: `/${serviceMethod}`, error: error.message });
    }
};

export default {
    getAllCalendar: (req: Request, res: Response) => handleRequest(req, res, 'getAllCalendar'),
    getCalendarById: (req: Request, res: Response) => handleRequest(req, res, 'getCalendarById'),
    getCurrentCBUByCalendarId: (req: Request, res: Response) => handleRequest(req, res, 'getCurrentCBUByCalendarId'),
    getClocksByCalendarId: (req: Request, res: Response) => handleRequest(req, res, 'getClocksByCalendarId'),
    getInfoByCalendarId: (req: Request, res: Response) => handleRequest(req, res, 'getInfoByCalendarId'),
    getCyclesByCalendarAndClockId: (req: Request, res: Response) => handleRequest(req, res, 'getCyclesByCalendarAndClockId'),
    getPeriodsByCalendarClockAndCycleId: (req: Request, res: Response) => handleRequest(req, res, 'getPeriodsByCalendarClockAndCycleId'),
    getPeriodByCalendarClockCycleAndPeriodId: (req: Request, res: Response) => handleRequest(req, res, 'getPeriodByCalendarClockCycleAndPeriodId'),
    getSchedulesByCalendarClockAndCycleId: (req: Request, res: Response) => handleRequest(req, res, 'getSchedulesByCalendarClockAndCycleId'),
};
