import { Request, Response } from 'express';
import { StatusCodes } from 'http-status-codes';
import { OrchestraService } from '@zeta-atalanta/core/dist/services/OrchestraService';

import { InternalAPIException } from '@zeta-atalanta/core/dist/exceptions/InternalAPIException';

import {
    ALL_WORKER,
    DEFAULT_ENTITY_SORTBY,
    DEFAULT_SORT_ORDER,
    GET_LEDGER_DEFAULT_PAGE_ID,
    GET_LEDGER_DEFAULT_PAGE_SIZE,
    LEGDER_FIELD,
    STATUS_SKIPPED,
    WORKER_FIELD,
    WORKER_ID_MAP,
} from '../utils';
import { LEDGER_ENTITY } from '../constants';
import {
    EntitiesModel,
    EntitiesResponse,
    EntitiesRequestParams,
    EntitiesQueryParams,
} from '@zeta-atalanta/core/dist/services/Interfaces/Aura';

export const getSkippedLedgers = async (request: Request, response: Response) => {
    const {
        params: { tenantId = '', coaId = '', periodId = '' } = {},
        headers: { authorization: token = '' } = {},
        body: {
            phases = [],
            workers = [],
            ledgerIdToSearch,
            pageID = GET_LEDGER_DEFAULT_PAGE_ID,
            pageSize = GET_LEDGER_DEFAULT_PAGE_SIZE,
        },
    } = request;

    const orchestraService = new OrchestraService(request);

    try {
        if (tenantId && periodId && coaId) {
            const atTrackerLevel = workers.find((worker: string) => worker === ALL_WORKER);
            let data = [];
            if (atTrackerLevel) {
                data = await Promise.all(
                    // fetch data phase by phase at tracker level
                    phases.map(async (phase: string) => {
                        return fetchEntites(orchestraService.getEntitiesAtTrackerLevel.bind(orchestraService), {
                            tenantId,
                            coaId,
                            periodId,
                            phase,
                            token,
                            ledgerIdToSearch,
                            pageID: String(pageID),
                            pageSize: String(pageSize),
                        });
                    }),
                );
            } else {
                data = await Promise.all(
                    // fetch data phase by phase
                    phases.map(async (phase: string) => {
                        // fetch data for phase for every worker
                        return Promise.all(
                            workers.map(async (worker: string) => {
                                return fetchEntites(orchestraService.getEntities.bind(orchestraService), {
                                    tenantId,
                                    coaId,
                                    periodId,
                                    phase,
                                    token,
                                    ledgerIdToSearch,
                                    pageID: String(pageID),
                                    pageSize: String(pageSize),
                                    worker,
                                });
                            }),
                        );
                    }),
                );
            }
            // flatten nested array
            data = data.flat(2);
            let filteredData = [...data];
            if (ledgerIdToSearch) {
                filteredData = filteredData.filter((data) => data[LEGDER_FIELD].includes(ledgerIdToSearch));
            }
            return response.status(StatusCodes.OK).send({
                data: [...filteredData].splice(Number(pageID) * Number(pageSize) - Number(pageSize), Number(pageSize)),
                pagination: {
                    pageID: Number(pageID),
                    pageSize: Number(pageSize),
                    pagesCount: Math.round(filteredData.length / Number(pageSize)) || 1,
                    totalCount: filteredData.length,
                },
            });
        } else {
            throw new InternalAPIException({
                api: 'skippedLedgers',
                error: {
                    statusCode : StatusCodes.BAD_REQUEST,
                    response: {
                        status: StatusCodes.BAD_REQUEST,
                        data: { message: 'Payload is missing required fields' },
                    },
                },
            });
        }
    } catch (error) {
        console.log(error);
        // If Backend error
        if (error instanceof InternalAPIException) {
            return response.status(error.statusCode).send(error.response);
        }
        // In case of other javascript errors
        return response.status(StatusCodes.INTERNAL_SERVER_ERROR).send({ error: error.message });
    }
};

const fetchEntites = async (
    fetchFn: (requestParams: EntitiesRequestParams, pageQueryParams: EntitiesQueryParams) => Promise<EntitiesResponse>,
    { tenantId, coaId, periodId, phase, token, worker }: { [key: string]: string },
) => {
    const requestParams = {
        tenantId,
        coaId,
        periodId,
        phaseId: phase,
        entityType: LEDGER_ENTITY,
        token,
        workerId: '',
    };
    if (worker) {
        requestParams.workerId = worker;
    } else {
        delete requestParams[WORKER_FIELD];
    }
    const entitiesQueryParams = {
        sortOrder: DEFAULT_SORT_ORDER,
        sortBy: DEFAULT_ENTITY_SORTBY,
        status: STATUS_SKIPPED,
        pageID: GET_LEDGER_DEFAULT_PAGE_ID,
        pageSize: GET_LEDGER_DEFAULT_PAGE_SIZE,
    };
    {
        const response = await fetchFn(requestParams, entitiesQueryParams);
        const {
            pagination: { totalCount = 10 },
            entities: firstEntities = [],
        } = response;
        // got all legders in first call itself
        if (firstEntities.length >= totalCount) {
            return firstEntities.map((data: EntitiesModel) => modifyResponse(data, phase));
        } else {
            // fetch the remaining legders all at once
            const { entities: remainingEntities = [] } = await fetchFn(requestParams, {
                ...entitiesQueryParams,
                pageID: GET_LEDGER_DEFAULT_PAGE_ID + 1,
                pageSize: totalCount - firstEntities.length,
            });
            // returning all the data from first page and remaining
            return [...firstEntities, ...remainingEntities].map((data: EntitiesModel) => modifyResponse(data, phase));
        }
    }
};

const modifyResponse = (data: EntitiesModel, phase: string) => {
    return { phase, ledgerID: data.entityID, worker: WORKER_ID_MAP[data.workerID]?.name || data.workerID };
};
