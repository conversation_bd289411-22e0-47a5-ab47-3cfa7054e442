import { Request, Response } from 'express';
import { StatusCodes } from 'http-status-codes';
import { InternalAPIException } from '@zeta-atalanta/core/dist/exceptions/InternalAPIException';
import { CoaService } from '../services/CoaService';

const handleRequest = async (request: Request, response: Response, serviceMethod: string) => {
    try {
        const coaService = new CoaService(request);
        const res = await (coaService[serviceMethod as keyof CoaService] as Function).call(coaService);
        return response.status(StatusCodes.OK).send(res);
    } catch (error) {
        request.getLogger(__filename).error(`API Failed path: ${serviceMethod}`, error);

        if (error instanceof InternalAPIException) {
            return response.status(error.statusCode).send(error);
        }

        return response
            .status(StatusCodes.INTERNAL_SERVER_ERROR)
            .send({ api: `/${serviceMethod}`, error: error.message });
    }
};

export default {
    getAllCoa: (req: Request, res: Response) => handleRequest(req, res, 'getAllCoa'),
    getCoaById: (req: Request, res: Response) => handleRequest(req, res, 'getCoaById'),

    async getPeriodDetailsByPeriodId(request: Request, response: Response) {
        try {
            const { periodId } = request.query;
            const coaService = new CoaService(request);
            const res = await coaService.getPeriodDetailsByPeriodId({ periodId: String(periodId) });

            return response.status(StatusCodes.OK).send(res);
        } catch (error) {
            request.getLogger(__filename).error(`API Failed path: ${error?.response?.api}`, error);

            if (error instanceof InternalAPIException) {
                return response.status(error.statusCode).send(error);
            }

            return response.status(StatusCodes.INTERNAL_SERVER_ERROR).send({ error: error.message });
        }
    },
};
