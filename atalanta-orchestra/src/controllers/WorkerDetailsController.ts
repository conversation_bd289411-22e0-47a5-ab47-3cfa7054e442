import { Request, Response, NextFunction } from 'express';
import { StatusCodes } from 'http-status-codes';
import { OrchestraService } from '@zeta-atalanta/core/dist/services/OrchestraService';
import { OrchestraRunRequestObj } from '@zeta-atalanta/core/dist/services/Interfaces/Aura';

import { STATUS } from '../constants';
import { InternalAPIException } from '@zeta-atalanta/core/dist/exceptions/InternalAPIException';
import { parseWorkerName } from '../utils';
import _ from 'lodash';

export const getWorkerDetails = async (request: Request, response: Response) => {
    const {
        params: { tenantId = '' } = {},
        headers: { authorization: token = '' } = {},
        query: { phaseId = '', cbuId = '', coaId = '' } = {},
    } = request;

    const orchestraRunRequestObj = {
        tenantId,
        coaId,
        token,
        phaseId,
        includeWorkerRuns: 'true',
        cbuId,
    };
    const orchestraService = new OrchestraService(request);

    try {
        if (tenantId && coaId && phaseId && cbuId) {
            // Orchestra Run Data
            console.timeLog('Worker Details');
            let orchestraRunResponse = await orchestraService.getOrchestraRunData(
                orchestraRunRequestObj as OrchestraRunRequestObj,
            );

            console.timeEnd('Worker Details');
            const workerDetailsResponse: { [key: string]: any } = { [STATUS.SUCCESS]: {}, [STATUS.FAILED]: {} };
            if (orchestraRunResponse) {
                const { orchestraRuns = [] } = orchestraRunResponse;
                if (orchestraRuns.length) {
                    orchestraRuns.forEach((orchestra) => {
                        const { workerRuns = [], ...orchestraParams } = orchestra;
                        workerRuns.forEach((worker) => {
                            const workerDetail = {
                                workerName: parseWorkerName(worker.workerID),
                                status: worker.status,
                                executionID: worker.executionID,
                                totalEntityCount: worker.entityCount.total,
                                successEntityCount: worker.entityCount.success ? worker.entityCount.success : 0,
                                failedEntityCount: worker.entityCount.failure ? worker.entityCount.failure : 0,
                                createdAt: worker.createdAt,
                                runId: worker.runID,
                                entityType: worker.entityType,
                                updatedAt: worker.updatedAt,
                                workerID: worker.workerID,
                                coaId: (orchestraParams as any).coaID,
                                jobId: orchestraParams.id,
                                phase: orchestraParams.phase,
                                ...worker.period,
                            };

                            const status =
                                worker.status === STATUS.SUCCEEDED
                                    ? STATUS.SUCCESS
                                    : worker.status === STATUS.FAILED || worker.status === STATUS.FAILED_RETRIED
                                    ? STATUS.FAILED
                                    : '';

                            if (status) {
                                // workerDetailsResponse[STATUS.SUCCESS].push(workerDetail);
                                if (workerDetailsResponse[status]?.[workerDetail.workerName]) {
                                    workerDetailsResponse[status][workerDetail.workerName] = {
                                        ...workerDetailsResponse[status][workerDetail.workerName],
                                        executionIDs: [
                                            ...workerDetailsResponse[status][workerDetail.workerName].executionIDs,
                                            workerDetail.executionID,
                                        ],
                                        totalEntityCount:
                                            workerDetailsResponse[status][workerDetail.workerName].totalEntityCount +
                                            workerDetail.totalEntityCount,
                                        successEntityCount:
                                            workerDetailsResponse[status][workerDetail.workerName].successEntityCount +
                                            workerDetail.successEntityCount,
                                        failedEntityCount:
                                            workerDetailsResponse[status][workerDetail.workerName].failedEntityCount +
                                            workerDetail.failedEntityCount,
                                    };
                                } else {
                                    workerDetailsResponse[status][workerDetail.workerName] = {
                                        ...workerDetail,
                                        executionIDs: [workerDetail.executionID],
                                    };
                                }
                            }
                        });
                    });
                    return response.status(StatusCodes.OK).send(workerDetailsResponse);
                } else {
                    // no runs found
                    return response.status(StatusCodes.OK).send([]);
                }
            }
        } else {
            throw new InternalAPIException({
                api: 'getWorkerDetails',
                error: {
                    statusCode : StatusCodes.BAD_REQUEST,
                    response: {
                        status: StatusCodes.BAD_REQUEST,
                        data: { message: 'Missing mandatory query parameters' },
                    },
                },
            });
        }
    } catch (error) {
        console.log(error);
        // If Backend error
        if (error instanceof InternalAPIException) {
            return response.status(error.statusCode).send(error.response);
        }
        // In case of other javascript errors
        return response.status(StatusCodes.INTERNAL_SERVER_ERROR).send({ error: error.message });
    }
};
