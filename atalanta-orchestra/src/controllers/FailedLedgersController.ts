import { Request, Response, NextFunction } from 'express';
import { StatusCodes } from 'http-status-codes';
import { OrchestraService } from '@zeta-atalanta/core/dist/services/OrchestraService';
import { TachyonService } from '@zeta-atalanta/core/dist/services/TachyonService';
import { LedgerService } from '@zeta-atalanta/core/dist/services/LedgerService';
import { verifyParameters, addPath, addParentPath, shortenWorkerName, parseWorkerName } from '../utils';
import {
    LEDGER_ENTITY,
    DEFAULT_ENTITY_SORTBY,
    STATUS_FAILURE,
    STATUS_SKIPPED,
    DEFAULT_SORT_ORDER,
    STATUS,
} from '../constants';
import { InternalAPIException } from '@zeta-atalanta/core/dist/exceptions/InternalAPIException';
import {
    EntitiesModel,
    OrchestraRunRequestObj,
} from '@zeta-atalanta/core/dist/services/Interfaces/Aura';
import { GetLedgerResponse } from "@zeta-atalanta/core/dist/services/Interfaces/Tachyon";
import _ from 'lodash';
import { LedgerResponse } from 'index';

const getFailedLedger = async (request: Request) => {
    const { params, query } = request;
    const { tenantId, coaId, periodId, phaseId, workerId } = params;
    const { pageId, pageSize } = query;
    const token = request.headers.authorization;
    const checkParameterSanity = verifyParameters({ tenantId, coaId, periodId, phaseId, workerId });
    if (checkParameterSanity.valid) {
        try {
            const tachyonService = new TachyonService(request);
            const ledgerService = new LedgerService(request);
            const orchestraService = new OrchestraService(request);
            // fetch coaTree to map to corresponding parent node
            const coaRequestParams = {
                tenantId: tenantId,
                coaId: coaId,
                startLevel: 0,
                endLevel: 4,
                asOnTimestamp: new Date().getTime(),
                includeSummaries: false,
                token,
            };

            const coaResponse = await tachyonService.getCoaTree(coaRequestParams);
            const coaTree = addPath(coaResponse?.nodes?.length && coaResponse.nodes[0].children);

            const entitiesRequestParams = {
                tenantId,
                coaId,
                phaseId,
                periodId,
                workerId,
                entityType: LEDGER_ENTITY,
                token,
            };

            const entitiesQueryParams = {
                sortOrder: DEFAULT_SORT_ORDER,
                sortBy: DEFAULT_ENTITY_SORTBY,
                pageID: +pageId,
                pageSize: +pageSize,
                status: STATUS_FAILURE,
            };
            // Get all failed ledgers
            const { entities: failedLedgerArray } = await orchestraService.getEntities(
                entitiesRequestParams,
                entitiesQueryParams,
            );

            if (failedLedgerArray.length > 0) {
                // Using individual ledger IDs, fetch ledger data
                const ledgerDataPromises = failedLedgerArray.map((ledger: EntitiesModel) => {
                    const payload = {
                        tenantId: tenantId,
                        coaId: coaId,
                        ledgerId: ledger.entityID as string,
                        token,
                    };

                    return ledgerService.getLedger(payload);
                });

                const ledgerDetailArrayResponse = await Promise.allSettled(ledgerDataPromises);
                let failedLedgerList: GetLedgerResponse[] = [];
                if (Array.isArray(ledgerDetailArrayResponse) && ledgerDetailArrayResponse.length) {
                    if (ledgerDetailArrayResponse.every((result) => result.status === 'rejected')) {
                        throw new Error('Fetching ledger details failed');
                    } else {
                        ledgerDetailArrayResponse.forEach((result) => {
                            if (result.status === 'fulfilled' && result?.value) {
                                failedLedgerList = [...failedLedgerList, { ...result.value }];
                            }
                        });
                    }
                }
                return addParentPath(failedLedgerList, coaTree);
            } else {
                return [];
            }
        } catch (error) {
            return Promise.reject(error);
        }
    } else {
        return Promise.reject({
            message: checkParameterSanity.message,
            statusCode: StatusCodes.BAD_REQUEST,
        });
    }
};

export const getFailedLedgersForWorker = async (request: Request, response: Response) => {
    try {
        const failedLedgerList = await getFailedLedger(request);
        return response.status(StatusCodes.OK).send(failedLedgerList);
    } catch (error) {
        request.getLogger(__filename).error('Failed to get failed ledger Data', error);
        // If Backend error
        if (error instanceof InternalAPIException) {
            return response.status(error.statusCode).send(error.response);
        }
        // In case of other javascript errors
        const statusCode = error.statusCode || StatusCodes.INTERNAL_SERVER_ERROR;
        return response.status(statusCode).send({
            error: {
                message: error.message,
                status: statusCode,
            },
        });
    }
};

/**
 * This function retrieves failed ledger data for all workers in a given period and phase for a Coa
 */
export const getFailedLedgersForAllWorkers = async (request: Request, response: Response) => {
    const { params, query } = request;
    const { tenantId, coaId, periodId, phaseId } = params;
    const token = request.headers.authorization;
    const checkParameterSanity = verifyParameters({ tenantId, coaId, periodId, phaseId });
    if (checkParameterSanity.valid) {
        try {
            // This will get the latest orchestra run
            const orchestraRunRequestObj = {
                tenantId,
                coaId,
                phaseId,
                includeWorkerRuns: 'true',
                cbuId: periodId,
                token,
                sortBy: 'executionID',
                sortOrder: 'desc',
            };
            const orchestraService = new OrchestraService(request);
            const orchestraRunResponse = await orchestraService.getOrchestraRunData(
                orchestraRunRequestObj as OrchestraRunRequestObj,
            );
            const { orchestraRuns } = orchestraRunResponse;

            if (Array.isArray(orchestraRuns) && orchestraRuns.length > 0) {
                const lastExecution = orchestraRuns[0];
                if ([STATUS.FAILED_RETRIED, STATUS.FAILED].includes(lastExecution.status)) {
                    const workerRuns = lastExecution?.workerRuns ?? [];
                    const failedWorkersRuns = workerRuns.filter((worker) =>
                        [STATUS.FAILED_RETRIED, STATUS.FAILED].includes(worker?.status),
                    );

                    const failedLedgerResults = await Promise.all(
                        failedWorkersRuns.map(async (workerRun) => {
                            request.params.workerId = shortenWorkerName(workerRun?.workerID);
                            const ledgers: GetLedgerResponse[] & {
                                parentPath: string;
                                parentPathFull: string;
                                parentPathShort: string;
                            } = await getFailedLedger(request);
                            return ledgers.map((ledger) => {
                                const workerNameArray = workerRun?.workerID.split('-') ?? '-';
                                return {
                                    workerName: workerRun?.workerID,
                                    periodicity: workerNameArray[workerNameArray.length - 2],
                                    ...ledger,
                                };
                            });
                        }),
                    );
                    response.status(StatusCodes.OK).send(failedLedgerResults.flat());
                } else {
                    response.status(StatusCodes.OK).send([]);
                }
            } else {
                response.status(StatusCodes.OK).send([]);
            }
        } catch (error) {
            request.getLogger(__filename).error('Failed to get failed ledger Data', error);
            // If Backend error
            if (error instanceof InternalAPIException) {
                response.status(error.statusCode).send(error.response);
            } else {
                // In case of other javascript errors
                response.status(StatusCodes.INTERNAL_SERVER_ERROR).send({ error: error.message });
            }
        }
    } else {
        response.status(StatusCodes.BAD_REQUEST).send({
            message: checkParameterSanity.message,
            statusCode: StatusCodes.BAD_REQUEST,
        });
    }
};
