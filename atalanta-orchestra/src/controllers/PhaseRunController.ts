import { Request, Response } from 'express';
import { StatusCodes } from 'http-status-codes';
import dayjs from 'dayjs';
import { DEFAULT_PAGE_SIZE, STATUS } from '../constants';
import { getFormattedTimeDuration, verifyParameters } from '../utils';
import { PhaseRunService } from '../services/PhaseRunService';
import { OrchestraService } from '@zeta-atalanta/core/dist/services/OrchestraService';
import { InternalAPIException } from '@zeta-atalanta/core/dist/exceptions/InternalAPIException';

/**
 * To fetch the phase run details for start and end period id
 * @param request
 * @param response
 * @returns workers list
 */
export const phaseRunDetails = async (request: Request, response?: Response) => {
    const { tenantId, coaId } = request.params;
    const token = request.headers.authorization;
    const { phase, startPeriodId, endPeriodId, includeWorkerRuns } = request.query as { [key: string]: string };
    const orchestraService = new OrchestraService(request);
    request.getLogger(__filename).info('Fetching orchestra run data');
    const orchestraRunPromise = [
        orchestraService.getOrchestraRunData({
            tenantId,
            coaId,
            token,
            ...(phase && { phaseId: phase }),
            ...(includeWorkerRuns && { includeWorkerRuns }),
            cbuId: startPeriodId,
            pageSize: `${DEFAULT_PAGE_SIZE}`,
        }),
    ];
    if (endPeriodId) {
        orchestraRunPromise.push(
            orchestraService.getOrchestraRunData({
                tenantId,
                coaId,
                token,
                ...(phase && { phaseId: phase }),
                ...(includeWorkerRuns && { includeWorkerRuns }),
                cbuId: endPeriodId,
                pageSize: `${DEFAULT_PAGE_SIZE}`,
            }),
        );
    }
    const orchestraRunsResponse = await Promise.all(orchestraRunPromise);
    request.getLogger(__filename).info('Fetched orchestra run data');
    orchestraRunsResponse.map(({ orchestraRuns }) => {
        return orchestraRuns.map((orchestraRun, index) => {
            orchestraRun.sequenceNumber = index + 1;
            if (orchestraRun.status === STATUS.SUCCEEDED) {
                orchestraRun.status = STATUS.SUCCEEDED;
            } else if (orchestraRun.status === STATUS.FAILED || orchestraRun.status === STATUS.FAILED_RETRIED) {
                orchestraRun.status = STATUS.FAILED;
            } else orchestraRun.status = STATUS.PROGRESS;
            if (orchestraRun.finishedAt) {
                const { startedAt, finishedAt } = orchestraRun;
                const totalTimeTakenInProcess = dayjs(finishedAt).diff(dayjs(startedAt));
                orchestraRun.timeTaken = getFormattedTimeDuration(totalTimeTakenInProcess);
            }
            return orchestraRun;
        });
    });
    return {
        startPeriodId: orchestraRunsResponse[0],
        endPeriodId: orchestraRunsResponse[1],
    };
};

export const getPhaseRunDetails = async (request: Request, response: Response): Promise<any> => {
    const { tenantId, coaId } = request.params;
    request.getLogger(__filename).info(`getPhaseRunDetails method called for tenant ${tenantId} & coa Id ${coaId}`);
    const {
        phase,
        startPeriodId,
        endPeriodId,
        includeWorkerRuns,
        pageSize = `${DEFAULT_PAGE_SIZE}`,
    } = request.query as { [key: string]: string };
    const { valid, message } = verifyParameters({ tenantId, startPeriodId, coaId });
    if (!valid) {
        request
            .getLogger(__filename)
            .error(`getPhaseRunDetails method parameter validation failed for tenant ${tenantId} & coa Id ${coaId}`);
        return response.status(StatusCodes.BAD_REQUEST).send({ error: message });
    }
    try {
        const phaseRunService = new PhaseRunService(request);
        const phaseRunData = await phaseRunService.getPhaseRunDetails({
            phase,
            startPeriodId,
            endPeriodId,
            includeWorkerRuns,
            pageSize,
        });
        return response.status(StatusCodes.OK).send(phaseRunData);
    } catch (error) {
        request.getLogger(__filename).error('Failed to get phase run details', error);
        // If Backend error
        if (error instanceof InternalAPIException) {
            return response.status(error.statusCode).send(error.response);
        }
        // In case of other javascript errors
        return response.status(StatusCodes.INTERNAL_SERVER_ERROR).send({ error: error.message });
    }
};
