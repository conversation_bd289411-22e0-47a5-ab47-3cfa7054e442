import { Request, Response, NextFunction } from 'express';
import { StatusCodes } from 'http-status-codes';
import { AuraService } from '@zeta-atalanta/core/dist/services/AuraService';
import { OrchestraService } from '@zeta-atalanta/core/dist/services/OrchestraService';
import { getCoAStatus, getCurrentTriggerValueFromStatus, verifyParameters, getFormattedTimeDuration } from '../utils';
import {
    BatchRunCoASummary,
    BatchRunCoASummaryResponse,
    BatchRunPhaseCycleData,
    BatchRunPhaseCycleResponse,
    BatchRunWorkerSummary,
    BatchRunWorkerSummaryResponse,
    CBUStatusType,
    PhaseDetails,
} from '../types';
import { DEFAULT_PAGE_SIZE, PHASES, STATUS, PHASES_START, PHASES_END, PHASE_START_STATUS } from '../constants';
import { InternalAPIException } from '@zeta-atalanta/core/dist/exceptions/InternalAPIException';
import { OrchestraRunsObject } from '@zeta-atalanta/core/dist/services/Interfaces/Aura';
import dayjs from 'dayjs';
import _ from 'lodash';

/**
 * Common function to fetch trigger status data for a given coa
 * and period
 * @param request http request
 * @param parameters parameters required for the http request
 */
const fetchCoAPhaseTriggerStatus = async (
    request: Request,
    parameters: {
        tenantId: string;
        periodId: string;
        coas: string;
        phase: string;
        token: string;
    },
): Promise<PromiseFulfilledResult<BatchRunCoASummary>[]> => {
    const { tenantId, periodId, coas, phase, token } = parameters;
    // Split the query string
    const phaseCoAs = coas.split(',');
    const auraService = new AuraService(request);
    // Fetch the phase data for each CoaId
    const coaPhaseData = await Promise.allSettled(
        phaseCoAs.map(async (coaId: string) => {
            let coaObj: BatchRunCoASummary = {
                coaId,
            };
            try {
                request.getLogger(__filename).info(`Fetching coa phase trigger status for COA: ${coaId}`);
                const triggerStatusResponse = await auraService.getPhaseTriggerStatus({
                    tenantId,
                    coaId,
                    periodId,
                    triggerValue: getCurrentTriggerValueFromStatus(phase as CBUStatusType),
                    token,
                });
                if (triggerStatusResponse) {
                    // Get coa phase status
                    const responseStatus = triggerStatusResponse.status;
                    coaObj = {
                        coaId,
                        coaPhaseStatus: getCoAStatus(responseStatus),
                        startTime: triggerStatusResponse.transitionMap?.INITIATED?.startTime || 0,
                        endTime: triggerStatusResponse.transitionMap?.[responseStatus]?.startTime || 0,
                        ...(responseStatus === STATUS.FAILED && {
                            failedAt: triggerStatusResponse.transitionMap?.[responseStatus]?.startTime || 0,
                        }),
                    };
                }
            } catch (error: unknown) {
                request.getLogger(__filename).info(`COA phase trigger API failed for: ${coaId}`);
            }
            return coaObj;
        }),
    );
    return coaPhaseData as PromiseFulfilledResult<BatchRunCoASummary>[];
};

/**
 * Function to find the final phase of a period based on the below decision
 * if any one of the CoAs fail -> final Phase is FAILED
 * if none of the CoAs have failed -> check for SUCCESS if not all of them are successful
 * then its in progress
 * @param coaPhaseData
 */
const findFinalStatusForCalendarPhase = (coaPhaseData: PromiseFulfilledResult<BatchRunCoASummary>[]): string | null => {
    let successfulCoACount = 0,
        totalCoACount = 0,
        inProgressCoACount = 0;
    for (const coaPhasePromise of coaPhaseData) {
        // For incorrect coas, value could be null, so we should avoud that
        if (coaPhasePromise.value) {
            const coaPhaseStatus = coaPhasePromise.value.coaPhaseStatus;
            totalCoACount += 1;
            if (coaPhaseStatus === STATUS.FAILED || coaPhaseStatus === STATUS.FAILED_RETRIED) {
                // if at least one Failed, then break and return
                return STATUS.FAILED;
            } else if (
                coaPhaseStatus === STATUS.COMPLETED ||
                coaPhaseStatus === STATUS.SUCCEEDED ||
                coaPhaseStatus === STATUS.SUCCESS
            ) {
                successfulCoACount += 1;
            } else if (coaPhaseStatus !== undefined) {
                // basically any other status
                inProgressCoACount += 1;
            }
        }
    }
    return successfulCoACount === totalCoACount ? STATUS.SUCCESS : inProgressCoACount ? STATUS.PROGRESS : null;
};

// Batch Run Summary Widget -> Phase Cycle
export const getPhaseCycleData = async (request: Request, response: Response, next: NextFunction) => {
    const tenantId = request.params.tenantId;
    const token = request.headers.authorization;
    const { coas, phase, startPeriodId, endPeriodId } = request.query as {
        [key: string]: string;
    };
    const checkParameterSanity = verifyParameters({
        tenantId,
        startPeriodId,
        endPeriodId,
        coas,
        phase,
    });
    if (checkParameterSanity.valid) {
        try {
            const auraService = new AuraService(request);
            const coaPhaseData = await fetchCoAPhaseTriggerStatus(request, {
                tenantId,
                periodId: PHASES_END.includes(phase) ? endPeriodId : startPeriodId,
                coas,
                phase,
                token,
            });
            const coaPhaseDetails = coaPhaseData[0].value;
            const finalStatus = coaPhaseDetails.coaPhaseStatus;
            const failedAt = coaPhaseDetails.failedAt;
            let eodStartTime!: number;
            // Now fetch all the phase details
            let cumulativePhases = await Promise.allSettled([
                ...PHASES_START.map(async (phaseString, index) => {
                    let phaseDetails: PhaseDetails = {
                        phaseName: phaseString,
                    };

                    try {
                        request
                            .getLogger(__filename)
                            .info(
                                `Fetching coa phase trigger status for COA: ${coaPhaseDetails.coaId} & phase: ${phaseString}`,
                            );
                        const triggerStatusResponse = await auraService.getPhaseTriggerStatus({
                            tenantId,
                            coaId: coaPhaseDetails.coaId,
                            periodId: startPeriodId,
                            triggerValue: getCurrentTriggerValueFromStatus(phaseString as CBUStatusType),
                            token,
                        });
                        if (triggerStatusResponse) {
                            phaseDetails.initiatedStartTime =
                                triggerStatusResponse.transitionMap?.INITIATED?.startTime || 0;
                            phaseDetails.completedStartTime =
                                triggerStatusResponse.transitionMap?.COMPLETED?.startTime || 0;
                            // Store start time for future use
                            if (phaseString === PHASE_START_STATUS) {
                                eodStartTime = phaseDetails.initiatedStartTime;
                            }
                        }
                    } catch (error: unknown) {
                        request
                            .getLogger(__filename)
                            .info(
                                `Failed fetching coa phase trigger status for COA: ${coaPhaseDetails.coaId} & phase: ${phaseString}`,
                            );
                    }

                    return phaseDetails;
                }),
                ...PHASES_END.map(async (phaseString, index) => {
                    let phaseDetails: PhaseDetails = {
                        phaseName: phaseString,
                    };

                    try {
                        request
                            .getLogger(__filename)
                            .info(
                                `Fetching coa phase trigger status for COA: ${coaPhaseDetails.coaId} & phase: ${phaseString}`,
                            );
                        const triggerStatusResponse = await auraService.getPhaseTriggerStatus({
                            tenantId,
                            coaId: coaPhaseDetails.coaId,
                            periodId: endPeriodId,
                            triggerValue: getCurrentTriggerValueFromStatus(phaseString as CBUStatusType),
                            token,
                        });
                        if (triggerStatusResponse) {
                            phaseDetails.initiatedStartTime =
                                triggerStatusResponse.transitionMap?.INITIATED?.startTime || 0;
                            phaseDetails.completedStartTime =
                                triggerStatusResponse.transitionMap?.COMPLETED?.startTime || 0;
                        }
                    } catch (error: unknown) {
                        request
                            .getLogger(__filename)
                            .info(
                                `Failed fetching coa phase trigger status for COA: ${coaPhaseDetails.coaId} & phase: ${phaseString}`,
                            );
                    }
                    return phaseDetails;
                }),
            ]);
            // Serialize the settled promises to normal array and emit the response
            return response.status(StatusCodes.OK).send(<BatchRunPhaseCycleResponse>{
                data: {
                    startTime: eodStartTime,
                    endTime: coaPhaseDetails.endTime,
                    ...(finalStatus && { coaPhaseStatus: finalStatus }),
                    ...(failedAt && { failedAt }),
                    phaseDetails: cumulativePhases.map((phaseData: PromiseFulfilledResult<PhaseDetails>) => {
                        return phaseData.value;
                    }),
                },
            });
        } catch (error) {
            request.getLogger(__filename).error('Failed to get coa phase cycle data', error);
            // If Backend error
            if (error instanceof InternalAPIException) {
                return response.status(error.statusCode).send(error.response);
            }
            // In case of other javascript errors
            return response.status(StatusCodes.INTERNAL_SERVER_ERROR).send({ error: error.message });
        }
    } else {
        return response.status(StatusCodes.BAD_REQUEST).send({ error: checkParameterSanity.message });
    }
};

// Batch Run Summary Widget -> CoA Summary
export const getCoASummaryData = async (request: Request, response: Response, next: NextFunction) => {
    const tenantId = request.params.tenantId;
    const periodId = request.params.periodId;
    const token = request.headers.authorization;
    const { coas, phase } = request.query as { [key: string]: string };
    const checkParameterSanity = verifyParameters({ tenantId, periodId, coas, phase });
    if (checkParameterSanity.valid) {
        try {
            // Fetch the phase data for each CoaId
            let coaPhaseData = await fetchCoAPhaseTriggerStatus(request, {
                tenantId,
                periodId,
                coas,
                phase,
                token,
            });
            // Serialize the settled promises to normal array and emit the response
            return response.status(StatusCodes.OK).send(<BatchRunCoASummaryResponse>{
                data: coaPhaseData.map((coa: PromiseFulfilledResult<BatchRunCoASummary>) => coa.value),
            });
        } catch (error) {
            request.getLogger(__filename).error('Failed to get coa phase cycle data', error);
            // If Backend error
            if (error instanceof InternalAPIException) {
                return response.status(error.statusCode).send(error.response);
            }
            // In case of other javascript errors
            return response.status(StatusCodes.INTERNAL_SERVER_ERROR).send({ error: error.message });
        }
    } else {
        return response.status(StatusCodes.BAD_REQUEST).send({ error: checkParameterSanity.message });
    }
};

// Batch Run Summary Widget -> Worker Summary
export const getWorkerSummaryData = async (request: Request, response: Response, next: NextFunction) => {
    const tenantId = request.params.tenantId;
    const periodId = request.params.periodId;
    const token = request.headers.authorization;
    const { coas, phase } = request.query as { [key: string]: string };
    const checkParameterSanity = verifyParameters({ tenantId, periodId, phase, coas });
    if (checkParameterSanity.valid) {
        try {
            // Split the query string
            const phaseCoAs = coas.split(',');
            const orchestraService = new OrchestraService(request);
            // Fetch the phase data for each CoaId
            const coaOrchestraRunData = await Promise.allSettled(
                phaseCoAs.map(async (coaId: string) => {
                    let coaWorkerObj: BatchRunWorkerSummary = {
                        coaId,
                    };
                    try {
                        request.getLogger(__filename).info(`Fetching orchestra run data for COA: ${coaId}`);
                        const orchestraRunResponse = await orchestraService.getOrchestraRunData({
                            tenantId,
                            coaId,
                            token,
                            phaseId: phase,
                            cbuId: periodId,
                            includeWorkerRuns: 'true', // we need worker details
                            pageSize: `${DEFAULT_PAGE_SIZE}`,
                        });
                        if (orchestraRunResponse) {
                            /**
                             * For all the orchestra runs for a COA, check the status of the latest
                             * one ( use updated at time to check )
                             */
                            const orchestraRun = orchestraRunResponse?.orchestraRuns.reduce(
                                (
                                    accumulatorOrchestraRun: OrchestraRunsObject,
                                    currentOrchestraRun: OrchestraRunsObject,
                                ) => {
                                    if (
                                        new Date(currentOrchestraRun.finishedAt).getTime() >
                                        new Date(accumulatorOrchestraRun.finishedAt).getTime()
                                    ) {
                                        accumulatorOrchestraRun = currentOrchestraRun;
                                    }
                                    return currentOrchestraRun;
                                },
                                orchestraRunResponse?.orchestraRuns[0],
                            );
                            // If desired orchestra run is found
                            if (orchestraRun) {
                                const totalTimeTakenInProcess =
                                    orchestraRun.finishedAt && orchestraRun.finishedAt
                                        ? dayjs(orchestraRun.finishedAt).diff(dayjs(orchestraRun.startedAt))
                                        : null;
                                let totalSuccessfulWorkers = 0,
                                    totalFailedWorkers = 0;
                                // Accumulate successful and failed worker counts
                                orchestraRun.workerRuns?.forEach((worker) => {
                                    if (worker.status === STATUS.SUCCEEDED || worker.status === STATUS.SUCCESS) {
                                        totalSuccessfulWorkers += 1;
                                    } else {
                                        totalFailedWorkers += 1; // assuming all the other worker with status other than 'SUCCEEDED' are failed ones
                                    }
                                });
                                // accumulate worker count
                                coaWorkerObj = {
                                    coaId,
                                    totalFailedWorkers,
                                    totalSuccessfulWorkers,
                                    totalWorkers: totalSuccessfulWorkers + totalFailedWorkers,
                                    createdAt: orchestraRun.startedAt,
                                    updatedAt: orchestraRun.finishedAt,
                                    startedAt: orchestraRun.startedAt,
                                    finishedAt: orchestraRun.finishedAt,
                                    jobId: orchestraRun.id,
                                    executionId: orchestraRun.executionID,
                                    ...(totalTimeTakenInProcess && {
                                        timeTaken: getFormattedTimeDuration(totalTimeTakenInProcess),
                                    }),
                                    status: orchestraRun.status,
                                };
                            } else {
                                request.getLogger(__filename).info(`Orchestra run was empty for coa: ${coaId}`);
                            }
                        }
                    } catch (error: unknown) {
                        request.getLogger(__filename).info(`Orchestra Run API failed for: ${coaId}`);
                    }
                    return coaWorkerObj;
                }),
            );
            // Send serialized data
            return response.status(StatusCodes.OK).send(<BatchRunWorkerSummaryResponse>{
                data: coaOrchestraRunData.map(
                    (coaData: PromiseFulfilledResult<BatchRunWorkerSummary>) => coaData.value,
                ),
            });
        } catch (error) {
            request.getLogger(__filename).error('Failed to get orchestra worker data', error);
            // If Backend error
            if (error instanceof InternalAPIException) {
                return response.status(error.statusCode).send(error.response);
            }
            // In case of other javascript errors
            return response.status(StatusCodes.INTERNAL_SERVER_ERROR).send({ error: error.message });
        }
    } else {
        return response.status(StatusCodes.BAD_REQUEST).send({ error: checkParameterSanity.message });
    }
};
