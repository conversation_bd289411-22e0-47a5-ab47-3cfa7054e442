import { Request, Response, NextFunction } from 'express';
import { StatusCodes } from 'http-status-codes';
import { TachyonService } from '@zeta-atalanta/core/dist/services/TachyonService';
import { verifyParameters } from '../utils';
import { GetAllPeriodsAtalantaResponse, GetAllPeriodsQuery, GetAllPeriodsResponse } from '../types';

import _ from 'lodash';
import { InternalAPIException } from '@zeta-atalanta/core/dist/exceptions/InternalAPIException';

/**
 * Fetch all the periods for a given calendar, clock and cycle combintation
 * @param request API request
 * @param response API response
 * @param next Next function
 */
export const getAllPeriods = async (request: Request, response: Response, next: NextFunction) => {
    const { params: { tenantId = '', calendarId = '' } = {}, headers: { authorization: token = '' } = {} } = request;
    const checkParameterSanity = verifyParameters({ tenantId, calendarId });
    if (checkParameterSanity.valid) {
        try {
            // 1. Fetch all clocks for given calendar
            const commonParams = {
                tenantId,
                calendarId,
                token,
            };
            const tachyonService = new TachyonService(request);
            const clocksResponse = await tachyonService.getClockList({
                ...commonParams,
            });
            if (clocksResponse?.length) {
                let periodList: GetAllPeriodsResponse[] = [];
                for (const clock of clocksResponse) {
                    // 2. Fetch all cycles
                    const cyclesResponse = await tachyonService.getCycleList({
                        ...commonParams,
                        clockId: clock.id,
                    });
                    if (cyclesResponse?.length) {
                        for (const cycle of cyclesResponse) {
                            // 3. Fetch all periods
                            const periodsResponse = await tachyonService.getPeriodList({
                                ...commonParams,
                                clockId: clock.id,
                                cycleId: cycle.id,
                            });
                            if (periodsResponse?.length) {
                                periodList = periodList.concat(
                                    periodsResponse.map((period) => {
                                        return {
                                            sequenceNumber: period.sequenceNumber,
                                            endCbuSequenceNumber: period.endCbuSequenceNumber,
                                            startTime: period.startTime,
                                            nextPeriodStartTime: period.nextPeriodStartTime,
                                            periodicity: cycle.periodicity,
                                            cycleCode: cycle.code,
                                            cycleName: cycle.name,
                                            clockType: clock.type,
                                            periodStatus: period.status,
                                        };
                                    }),
                                );
                            } else {
                                request
                                    .getLogger(__filename)
                                    .info(`Periods not present or failed to fetch for cycle: ${cycle.id}`);
                            }
                        }
                    } else {
                        throw new Error(`Failed to fetch cycles for clock: ${clock.id}`);
                    }
                }
                return response.status(StatusCodes.OK).send(<GetAllPeriodsAtalantaResponse>{
                    data: periodList,
                });
            } else {
                throw new Error(`Failed to fetch clocks for calendar: ${calendarId}`);
            }
        } catch (error) {
            request.getLogger(__filename).info(error.message); // Show errors if any of the dependent requests fail
            request.getLogger(__filename).error('Failed to get all the periods for given calendar', error);
            // If Backend error
            if (error instanceof InternalAPIException) {
                return response.status(error.statusCode).send(error.response);
            }
            // In case of other javascript errors
            return response
                .status(StatusCodes.INTERNAL_SERVER_ERROR)
                .send({ error: error.message, status: StatusCodes.INTERNAL_SERVER_ERROR });
        }
    } else {
        return response.status(StatusCodes.BAD_REQUEST).send({ error: checkParameterSanity.message });
    }
};
