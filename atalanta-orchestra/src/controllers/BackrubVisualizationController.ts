import { Request, Response, NextFunction, response } from 'express';
import { StatusCodes } from 'http-status-codes';
import { getFormattedTimeDuration, getPeriodicity, keyMap, parseWorkerName, verifyParameters } from '../utils';
import { TachyonService } from '@zeta-atalanta/core/dist/services/TachyonService';
import { InternalAPIException } from '@zeta-atalanta/core/dist/exceptions/InternalAPIException';
import { BackrubService, AuraService, BackrubOrchestraService } from '../services/backrubService';
import {
    calendarId,
    coaId,
    token,
    PHASES,
    EOFIUseCases,
    EOPIUseCases,
    EOPIOrchestraMap,
    EOPIDaybookMap,
    EOFIOrchestraMap,
    EOFIDaybookMap,
    FeesMaps,
    FeesFormatValues,
    FeesList,
    orchestraStatusVerifierCode,
    coaCode,
} from '../constants/backrubConstants';
import dayjs from 'dayjs';
import _ from 'lodash';
import { STATUS } from '@zeta-atalanta/core/dist/Constants';
import { OrchestraService } from '@zeta-atalanta/core/dist/services/OrchestraService';
import { DEFAULT_PAGE_SIZE } from '../constants';

export const visualizationData = async (request: Request, response: Response) => {
    const { params, query } = request;
    const { phaseId, type } = params;
    const { version, span } = query;
    const checkParameterSanity = verifyParameters({ phaseId, type });
    if (checkParameterSanity.valid) {
        try {
            const backrubService = new BackrubService(request, version as string);
            let currentPhaseUseCase: any;
            let currentPhaseMap: any;
            let workerListPromise: null | Promise<any> = null;
            let workersProcessed = null;
            const accountsProcessedPromise =
                type === 'orchestra'
                    ? backrubService.orchestraAccountsProcessed({ phaseId }, span as string)
                    : backrubService.daybookAccountsProcessed({ phaseId }, version as string, span as string);
            if (phaseId === 'CLOSED') {
                return response.status(StatusCodes.BAD_REQUEST).send({
                    message: 'Invalid phaseId',
                    statusCode: StatusCodes.BAD_REQUEST,
                });
            }
            if (phaseId === 'EOPI') {
                if (type === 'orchestra') {
                    currentPhaseUseCase = Object.keys(EOPIOrchestraMap);
                    currentPhaseMap = EOPIOrchestraMap;
                    workerListPromise = populateWorkerListStats(request);
                } else {
                    currentPhaseUseCase = Object.keys(EOPIDaybookMap);
                    currentPhaseMap = EOPIDaybookMap;
                }
            } else if (phaseId === 'EOFI') {
                if (type === 'orchestra') {
                    currentPhaseUseCase = Object.keys(EOFIOrchestraMap);
                    currentPhaseMap = EOFIOrchestraMap;
                    workerListPromise = populateWorkerListStats(request);
                } else {
                    currentPhaseUseCase = Object.keys(EOFIDaybookMap);
                    currentPhaseMap = EOFIDaybookMap;
                }
            } else {
                return response.status(StatusCodes.BAD_REQUEST).send({
                    message: 'Invalid phaseId',
                    statusCode: StatusCodes.BAD_REQUEST,
                });
            }
            const functionalUseCasePromises = currentPhaseUseCase?.map((useCase: any) => {
                return type === 'orchestra'
                    ? backrubService.orchestraFunctionalUseCase(
                          { phaseId, useCase: currentPhaseMap[useCase] },
                          span as string,
                      )
                    : backrubService.daybookFunctionalUseCase({ useCase: currentPhaseMap[useCase] }, span as string);
            });
            if (phaseId === 'EOFI') {
                if (type === 'orchestra') {
                    functionalUseCasePromises.splice(
                        1,
                        1,
                        backrubService.orchestraDelinquencyUseCase({ span }, version as string),
                    );
                } else {
                    functionalUseCasePromises.splice(
                        1,
                        1,
                        backrubService.daybookDelinquencyUseCase({ span }, version as string),
                    );
                }
            }
            const allPromiseses = [accountsProcessedPromise, ...functionalUseCasePromises];
            if (workerListPromise) {
                allPromiseses.push(workerListPromise);
            }

            const cumulativeResponse = await Promise.allSettled(allPromiseses);
            const cumulativeResult: any = {};
            cumulativeResponse.forEach((status, index, array) => {
                if (status.status === 'rejected') {
                    throw status.reason;
                } else {
                    if (status.value?.data?.result) {
                        if (workerListPromise && index === cumulativeResponse.length - 1) {
                            const value = status.value.data.result?.find((item: any) => item.phase === phaseId);
                            if (!isNaN(value?.successful)) {
                                workersProcessed = value?.successful + '';
                            }
                        } else {
                            const value = status.value.data.result?.[0]?.value[1] ?? '';
                            if (index === 0) {
                                cumulativeResult['AccountsProcessed'] = value;
                            } else {
                                cumulativeResult[currentPhaseUseCase[index - 1]] = value;
                            }
                        }
                    }
                }
            });
            let returnResponse = {};
            if (workerListPromise) {
                // keeping it as first attribute in case value exsits
                returnResponse = { DataPreparation: workersProcessed || "" };
            }
            returnResponse = { ...returnResponse, ...cumulativeResult };
            return response.status(StatusCodes.OK).send({
                ...returnResponse,
            });
        } catch (error) {
            request.getLogger(__filename).error('Failed to get visualization data', error);
            // If Backend error
            if (error instanceof InternalAPIException) {
                response.status(error.statusCode).send(error.response);
            } else {
                // In case of other javascript errors
                response.status(StatusCodes.INTERNAL_SERVER_ERROR).send({ error: error.message });
            }
        }
    } else {
        return response.status(StatusCodes.BAD_REQUEST).send({
            message: checkParameterSanity.message,
            statusCode: StatusCodes.BAD_REQUEST,
        });
    }
};

export const currentCBUAndType = async (request: Request, response: Response) => {
    const { params } = request;
    const { tenantId } = params;
    const checkParameterSanity = verifyParameters({ tenantId });
    if (checkParameterSanity.valid) {
        try {
            const tachyonService = new TachyonService(request);
            const auraService = new AuraService(request);
            const bearerToken = `Bearer ${token}`;
            const currentCBU = await tachyonService.getCurrentCBUData({ tenantId, calendarId, token: bearerToken });
            const cbuDate = dayjs(currentCBU.startTime).format('YYYY-MM-DD');
            let statusVerificationReports = await auraService.statusVerificationReports({
                tenantId,
                phaseId: currentCBU.status,
                cbuDate: cbuDate,
                coaCode: coaCode,
                token: bearerToken,
            });
            let type = null;
            request.getLogger(__filename).info('statusVerificationReports', statusVerificationReports);
            statusVerificationReports = statusVerificationReports?.data ? statusVerificationReports?.data : statusVerificationReports;
            const orchestraReport = statusVerificationReports?.contents?.find((content: any) => {
                return content?.report?.statusVerifierCode === orchestraStatusVerifierCode;
            });
            request.getLogger(__filename).info('OrchestraReport', orchestraReport);
            if (orchestraReport) {
                if (orchestraReport?.report?.status?.value === 'ALLOW') {
                    type = 'daybook';
                } else {
                    type = 'orchestra';
                }
            }
            response.status(StatusCodes.OK).send({
                ...currentCBU,
                phaseDetails: {
                    type,
                    modifiedAt: orchestraReport?.report?.modifiedAt,
                },
            });
        } catch (error) {
            console.log('currentCBUAndType - error:', error);
            request.getLogger(__filename).error('Failed to get visualization data', error);
            // If Backend error
            if (error instanceof InternalAPIException) {
                response.status(error.statusCode).send(error.response);
            } else {
                // In case of other javascript errors
                response.status(StatusCodes.INTERNAL_SERVER_ERROR).send({ error: error.message });
            }
        }
    } else {
        return response.status(StatusCodes.BAD_REQUEST).send({
            message: checkParameterSanity.message,
            statusCode: StatusCodes.BAD_REQUEST,
        });
    }
};

export const summaryData = async (request: Request, response: Response) => {
    const { params, query } = request;
    const { tenantId, phaseId } = params;
    const { version, span } = query;
    const checkParameterSanity = verifyParameters({ tenantId, phaseId });
    if (checkParameterSanity.valid) {
        try {
            const backrubService = new BackrubService(request, version as string);
            const feesCollectedPromises = [];
            for (const [key, value] of Object.entries(FeesMaps)) {
                feesCollectedPromises.push(
                    backrubService.feesSummaryData(
                        {
                            phaseId,
                            useCase: value,
                            formatValue: FeesFormatValues[key as keyof typeof FeesFormatValues],
                        },
                        version as string,
                        span as string,
                    ),
                );
            }
            const cumulativeResponse = await Promise.allSettled(feesCollectedPromises);

            const cumulativeResult: any = {};
            cumulativeResponse.forEach((status, index, array) => {
                if (status.status === 'rejected') {
                    throw status.reason;
                } else {
                    if (status.value?.data?.result) {
                        const value = status.value.data.result?.[0]?.value[1] ?? '';
                        if (index === 0) {
                            cumulativeResult['AccountsProcessed'] = value;
                        } else {
                            // divide by 2 for annual fee and joining fee and Delinquent Accounts and Revolver Accounts
                            cumulativeResult[FeesList[index]] = value.toString();
                        }
                    }
                }
            });
            response.status(StatusCodes.OK).send({
                feesSummary: cumulativeResult,
            });
        } catch (error) {
            request.getLogger(__filename).error('Failed to get visualization data', error);
            // If Backend error
            if (error instanceof InternalAPIException) {
                response.status(error.statusCode).send(error.response);
            } else {
                // In case of other javascript errors
                response.status(StatusCodes.INTERNAL_SERVER_ERROR).send({ error: error.message });
            }
        }
    } else {
        return response.status(StatusCodes.BAD_REQUEST).send({
            message: checkParameterSanity.message,
            statusCode: StatusCodes.BAD_REQUEST,
        });
    }
};

export const getCurrentTransactions = async (request: Request, response: Response) => {
    const { params, query } = request;
    const { tenantId } = params;
    const { type } = params;
    const { version } = query;
    const checkParameterSanity = verifyParameters({ tenantId, type });
    if (checkParameterSanity.valid) {
        try {
            const backrubService = new BackrubService(request, version as string);
            if (type === 'orchestra') {
                const tpsData = await backrubService.orchestraTPS(version as string);
                return response.status(StatusCodes.OK).send({
                    currentTransactions: {
                        currentValue: tpsData?.data?.result[0]?.value[1] ?? null,
                        type,
                    },
                });
            } else if (type === 'daybook') {
                const tpsData = await backrubService.daybookTPS(version as string);
                return response.status(StatusCodes.OK).send({
                    currentTransactions: {
                        currentValue: tpsData?.data?.result[0]?.value[1] ?? null,
                        type,
                    },
                });
            } else {
                return response.status(StatusCodes.BAD_REQUEST).send({
                    message: 'Invalid type',
                    statusCode: StatusCodes.BAD_REQUEST,
                });
            }
        } catch (error) {
            request.getLogger(__filename).error('Failed to get visualization data', error);
            // If Backend error
            if (error instanceof InternalAPIException) {
                response.status(error.statusCode).send(error.response);
            } else {
                // In case of other javascript errors
                response.status(StatusCodes.INTERNAL_SERVER_ERROR).send({ error: error.message });
            }
        }
    } else {
        return response.status(StatusCodes.BAD_REQUEST).send({
            message: checkParameterSanity.message,
            statusCode: StatusCodes.BAD_REQUEST,
        });
    }
};

export const getCurrentTransactionsNew = async (request: Request, response: Response) => {
    const { params, query } = request;
    const { tenantId } = params;
    const { type, phase } = params;
    const { version, span, step } = query;
    const checkParameterSanity = verifyParameters({ tenantId, type });
    if (checkParameterSanity.valid) {
        try {
            const backrubService = new BackrubService(request, version as string);
            if (type?.toLowerCase() === 'orchestra') {
                const tpsData = await backrubService.orchestraTPSNew(
                    version as string,
                    phase as string,
                    span as string,
                    step as string,
                );
                return response.status(StatusCodes.OK).send({
                    currentTransactions: {
                        values: tpsData?.data?.result[0]?.values ?? [],
                        type,
                    },
                });
            } else if (type?.toLowerCase() === 'daybook') {
                const tpsData = await backrubService.daybookTPSNew(
                    version as string,
                    phase as string,
                    span as string,
                    step as string,
                );
                return response.status(StatusCodes.OK).send({
                    currentTransactions: {
                        values: tpsData?.data?.result[0]?.values ?? [],
                        type,
                    },
                });
            } else {
                return response.status(StatusCodes.BAD_REQUEST).send({
                    message: 'Invalid type',
                    statusCode: StatusCodes.BAD_REQUEST,
                });
            }
        } catch (error) {
            request.getLogger(__filename).error('Failed to get visualization data', error);
            // If Backend error
            if (error instanceof InternalAPIException) {
                response.status(error.statusCode).send(error.response);
            } else {
                // In case of other javascript errors
                response.status(StatusCodes.INTERNAL_SERVER_ERROR).send({ error: error.message });
            }
        }
    } else {
        return response.status(StatusCodes.BAD_REQUEST).send({
            message: checkParameterSanity.message,
            statusCode: StatusCodes.BAD_REQUEST,
        });
    }
};

/**
 * To fetch the worker list from the latest batch runs for various phases
 * @param request
 * @param response
 * @returns workers list
 */
export const getWorkersListStats = async (request: Request, response: Response) => {
    try {
        const workerStats = await populateWorkerListStats(request);
        return response.status(StatusCodes.OK).send({ ...workerStats });
    } catch (error) {
        // If Backend error
        if (error instanceof InternalAPIException) {
            return response.status(error.statusCode).send(error.response);
        }
        // In case of other javascript errors
        return response.status(StatusCodes.INTERNAL_SERVER_ERROR).send({ error: error.message });
    }
};
function getWorkerEntityStatus(status: string) {
    if (status === STATUS.SUCCEEDED) {
        return STATUS.SUCCESSFUL;
    } else if (status === STATUS.FAILED || status === STATUS.FAILED_RETRIED) {
        return _.capitalize(STATUS.FAILED);
    } else return STATUS.NOT_EXECUTED;
}
const populateWorkerListStats = async (request: Request) => {
    try {
        const { params } = request;
        const { tenantId } = params;
        const tachyonService = new TachyonService(request);
        const authorization = request?.headers?.authorization;
        const bearerToken = `Bearer ${token}`;
        const currentCBU = await tachyonService.getCurrentCBUData({ tenantId, calendarId, token: bearerToken });
        const phaseRunData: any = await phaseRunDetailsForBackRub(request, coaId ,currentCBU.id , authorization || bearerToken);
        //Make a map of phase and their orchestra runs based on phase
        const phaseWiseBatchRun: {
            [k: string]: any;
        } = {
            startPeriodId: keyMap(phaseRunData?.startPeriodId?.orchestraRuns, 'phase'),
            endPeriodId: keyMap(phaseRunData?.endPeriodId?.orchestraRuns, 'phase'),
        };
        // Various phases to covered in the respective period id
        const phasesWithPeriodId: {
            [k: string]: string[];
        } = {
            startPeriodId: ['EOPI', 'EOFI', 'CLOSED'],
            endPeriodId: ['BOPI', 'BOFI'],
        };

        // Pick the latest batch run from each phase .This batch run would be used to get the Workers
        const parseWorkerData = (periodKey: string) => {
            return phasesWithPeriodId[periodKey].reduce((acc: any, phase: any) => {
                const orchestraRunArray = phaseWiseBatchRun[periodKey][phase];
                if (orchestraRunArray?.length) {
                    const latestBatchRun = orchestraRunArray[orchestraRunArray.length - 1];
                    const workers = latestBatchRun.workerRuns
                        .map((worker: any, index: number) => {
                            const workerID = worker.workerID.split('-');
                            workerID[1] = _.capitalize(workerID[1]);
                            return {
                                phase,
                                sequenceNumber: index + 1,
                                entityType: _.capitalize(worker.entityType),
                                //Capitalise first letter and space before every captital letter
                                workerName: parseWorkerName(worker.workerID),
                                workerId: worker.workerID,
                                startTime: worker.createdAt,
                                updatedTime: worker.updatedAt,
                                timeTaken: getFormattedTimeDuration(
                                    parseInt(worker.updatedAt, 10) - parseInt(worker.createdAt, 10),
                                ),
                                totalEntityCount: worker.entityCount.total ?? 0,
                                successfulEntityCount: worker.entityCount.success ?? 0,
                                failedEntityCount: worker.entityCount.failure ?? 0,
                                status: getWorkerEntityStatus(worker.status),
                                periodId: worker?.period?.periodID,
                                periodicity: _.capitalize(getPeriodicity(worker.period)),
                                ...(worker.workerSuccessExecutionID && {
                                    workerSuccessExecutionID: worker?.workerSuccessExecutionID,
                                }),
                                runID: worker?.runID,
                                lastSuccessfulExecutionID: worker.workerSuccessExecutionID ?? '-',
                            };
                        })
                        .sort((a: any, b: any) => dayjs(a.updatedTime).valueOf() - dayjs(b.updatedTime).valueOf());
                    acc = [...acc, ...workers];
                }
                return acc;
            }, []);
        };

        const workerList = [...parseWorkerData('startPeriodId')];
        const requiredDataPrepWorkerList = workerList.filter(
            (item: any) => item?.workerId?.startsWith('dataPreparationWorker')
        );
        let workerStats: any = [];
        requiredDataPrepWorkerList.forEach((dataPrep: any) => {
            const phase = dataPrep.phase;
            if (phase === 'EOPI') {
                workerStats.push({
                    phase: phase,
                    successful: dataPrep.successfulEntityCount,
                    total: dataPrep.totalEntityCount,
                });
            } else if (phase === 'EOFI') {
                workerStats.push({
                    phase: phase,
                    successful: dataPrep.successfulEntityCount,
                    total: dataPrep.totalEntityCount,
                });
            }
            if (phase === 'CLOSED') {
                workerStats.push({
                    phase: phase,
                    successful: dataPrep.successfulEntityCount,
                    total: dataPrep.totalEntityCount,
                });
            }
        });
        return { data: { result: [...workerStats] } };
    } catch (error) {
        request.getLogger(__filename).error('Fetching orchestra worker preperation data',error);
        
        return {
            data: {
                result: [],
            },
        };
    }
};
const phaseRunDetailsForBackRub = async (request: Request,coaId: string, cbuId: string,token: string) => {
    const tenantId = request.params.tenantId;
    const orchestraService = new BackrubOrchestraService(request);
    request.getLogger(__filename).info('Fetching orchestra run data');
    const orchestraRunPromise = [
        orchestraService.getOrchestraRunData({
            tenantId,
            coaId,
            token,
            includeWorkerRuns: true,
            cbuID: cbuId,
            pageId: 1,
            pageSize: `${DEFAULT_PAGE_SIZE}`,
        }),
    ];
    const orchestraRunsResponse = await Promise.all(orchestraRunPromise);
    orchestraRunsResponse.map(({ orchestraRuns }) => {
        return orchestraRuns.map((orchestraRun: any, index: any) => {
            orchestraRun.sequenceNumber = index + 1;
            if (orchestraRun.status === STATUS.SUCCEEDED) {
                orchestraRun.status = STATUS.SUCCEEDED;
            } else if (orchestraRun.status === STATUS.FAILED || orchestraRun.status === STATUS.FAILED_RETRIED) {
                orchestraRun.status = STATUS.FAILED;
            } else orchestraRun.status = STATUS.PROGRESS;
            if (orchestraRun.finishedAt) {
                const { startedAt, finishedAt } = orchestraRun;
                const totalTimeTakenInProcess = dayjs(finishedAt).diff(dayjs(startedAt));
                orchestraRun.timeTaken = getFormattedTimeDuration(totalTimeTakenInProcess);
            }
            return orchestraRun;
        });
    });
    return {
        startPeriodId: orchestraRunsResponse[0],
        endPeriodId: orchestraRunsResponse[1],
    };
};

export const executeVmUiQuery = async (request: Request, response: Response) => {
    const { params, query } = request;
    const { tenantId } = params;
    const { vmQuery, version, span, step, start, end, nocache, isrange } = query;
    const checkParameterSanity = verifyParameters({ tenantId });
    if (checkParameterSanity.valid) {
        try {
            const backrubService = new BackrubService(request, version as string);
            const vmQuerData = await backrubService.executeVmUiQuery({
                vmQuery: vmQuery as string,
                version: version as string,
                span: span as string,
                step: step as string,
                start: start as string,
                end: end as string,
                nocache: nocache as string,
                isrange: isrange as string,
            });
            return response.status(StatusCodes.OK).send({ ...vmQuerData });
        } catch (error) {
            request.getLogger(__filename).error('Failed to get executeVmUiQuery data', error);
            // If Backend error
            if (error instanceof InternalAPIException) {
                response.status(error.statusCode).send(error.response);
            } else {
                // In case of other javascript errors
                response.status(StatusCodes.INTERNAL_SERVER_ERROR).send({ error: error.message });
            }
        }
    } else {
        return response.status(StatusCodes.BAD_REQUEST).send({
            message: checkParameterSanity.message,
            statusCode: StatusCodes.BAD_REQUEST,
        });
    }
};
