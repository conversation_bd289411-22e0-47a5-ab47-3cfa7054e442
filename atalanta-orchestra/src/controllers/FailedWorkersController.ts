import { Request, Response } from 'express';
import { StatusCodes } from 'http-status-codes';
import { OrchestraService } from '@zeta-atalanta/core/dist/services/OrchestraService';

import { InternalAPIException } from '@zeta-atalanta/core/dist/exceptions/InternalAPIException';
import { OrchestraRunRequestObj, WorkerObject } from '@zeta-atalanta/core/dist/services/Interfaces/Aura';
import { STATUS } from '../constants';
import { getWorkerNameFromId } from '../utils';
import { FailedWorkerResponse } from '../types';

export const getFailedWorkers = async (request: Request, response: Response) => {
    const {
        params: { tenantId = '', coaId = '' } = {},
        headers: { authorization: token = '' } = {},
        query: { phaseId = '', cbuId = '' },
    } = request;

    const orchestraService = new OrchestraService(request);

    try {
        if (tenantId && phaseId && cbuId && coaId) {
            const orchestraRunRequestObj = {
                tenantId,
                coaId,
                token,
                phaseId,
                includeWorkerRuns: 'true',
                cbuId,
            };

            let orchestraRunResponse = await orchestraService.getOrchestraRunData(
                orchestraRunRequestObj as OrchestraRunRequestObj,
            );
            if (orchestraRunResponse) {
                const { orchestraRuns = [] } = orchestraRunResponse;

                // get latest batch run by latest execution id
                if (orchestraRuns.length) {
                    const latestBatchRun = orchestraRuns.reduce((a, b) => (a.executionID > b.executionID ? a : b));
                    // check the status of latest batch run and return the failed workers
                    const { status = '', workerRuns = [] } = latestBatchRun;
                    let failedWorkers: FailedWorkerResponse[] = [];
                    if (status === STATUS.FAILED) {
                        failedWorkers = workerRuns
                            .filter((worker) => worker.status === STATUS.FAILED)
                            .map((worker) => {
                                const { workerID = '', period: { periodID = '' } = {} } = worker;
                                const workerName = getWorkerNameFromId(workerID);
                                return {
                                    id: workerName,
                                    name: workerName,
                                    periodId: periodID,
                                };
                            });
                    }
                    if (failedWorkers.length) {
                        return response.status(StatusCodes.OK).send({ data: failedWorkers });
                    } else {
                        return response.status(StatusCodes.NOT_FOUND).send({ data: [] });
                    }
                } else {
                    return response.status(StatusCodes.NOT_FOUND).send({ data: [] });
                }
            }
        } else {
            throw new InternalAPIException({
                api: 'failed-workers',
                error: {
                    statusCode : StatusCodes.BAD_REQUEST,
                    response: {
                        status: StatusCodes.BAD_REQUEST,
                        data: { message: 'Payload is missing required fields' },
                    },
                },
            });
        }
    } catch (error) {
        console.log(error);
        // If Backend error
        if (error instanceof InternalAPIException) {
            return response.status(error.statusCode).send(error.response);
        }
        // In case of other javascript errors
        return response.status(StatusCodes.INTERNAL_SERVER_ERROR).send({ error: error.message });
    }
};
