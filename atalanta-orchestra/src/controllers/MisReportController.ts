import { Request, Response } from 'express';
import { StatusCodes } from 'http-status-codes';
import { InternalAPIException } from '@zeta-atalanta/core/dist/exceptions/InternalAPIException';
import { MisReportService } from '../services/MisReportService';
import dayjs from 'dayjs';
import { CalendarService } from '../services/CalendarService';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { PHASES_AFTER_BOFI_COMPLETION, REPORT_TYPE } from '../constants/misReportConstant';
import { createErrorObj } from '@zeta-atalanta/core/dist/utils';

dayjs.extend(utc);
dayjs.extend(timezone);

export default {
    async getReport(request: Request, response: Response) {
        try {
            const { periodTime, timezone, days = 31, localTimezone, coaCode } = request.query;

            const misReportService = new MisReportService(request);

            // Fetch reports and summary from service
            const res = await misReportService.getReport({
                periodTime: Number(periodTime),
                days: Number(days),
                timezone: String(timezone),
                localTimezone: String(localTimezone),
                coaCode: String(coaCode),
            });

            return response.status(StatusCodes.OK).send(res);
        } catch (error) {
            request.getLogger(__filename).error(`API Failed path: ${error?.response?.api}`, error);

            if (error instanceof InternalAPIException) {
                return response.status(error.statusCode).send(error);
            }

            return response.status(StatusCodes.INTERNAL_SERVER_ERROR).send({ error: error.message });
        }
    },

    async getDataForSchedulingReport(request: Request, response: Response) {
        try {
            let { periodTime, timezone, days, localTimezone, reportType, coaCode } = request.query;
            timezone = decodeURIComponent(String(timezone));
            localTimezone = decodeURIComponent(String(localTimezone));

            // Validate reportType if provided
            const validReportTypes = [REPORT_TYPE.DAILY, REPORT_TYPE.WEEKLY, REPORT_TYPE.MONTHLY];
            if (reportType && !validReportTypes.includes(reportType as string)) {
                return response.status(StatusCodes.BAD_REQUEST).send({
                    error: `Invalid reportType. Allowed values are ${validReportTypes.join(', ')}`,
                });
            }

            if (!periodTime) {
                async function fetchPeriodTimeFallback(
                    request: Request,
                ): Promise<{ periodTime: string; reportDays: string } | null> {
                    try {
                        const calendarService = new CalendarService(request);
                        const calendarInfo = await calendarService.getInfoByCalendarId();
                        const cbdStartTime = calendarInfo?.currentCBU?.startTime;
                        const isCbdBofiCompleted = PHASES_AFTER_BOFI_COMPLETION.includes(calendarInfo?.currentCBU?.status);

                        // Getting latest report date
                        let startPeriodTimeDayjs = dayjs(cbdStartTime).subtract(isCbdBofiCompleted ? 1 : 2, 'day');
                        let reportDays = 1;

                        if (reportType === REPORT_TYPE.WEEKLY) {
                            /**
                             * Subtracting 6 days to get date corresponding to the 7th most recent report.
                             */
                            startPeriodTimeDayjs = startPeriodTimeDayjs.tz(timezone as string).subtract(6, 'day');
                            reportDays = 7;
                        } else if (reportType === REPORT_TYPE.MONTHLY) {
                            /**
                             * Getting first day of the month for the latest report date
                             */
                            reportDays = startPeriodTimeDayjs.date();
                            startPeriodTimeDayjs = startPeriodTimeDayjs.tz(timezone as string).startOf('month');
                        }

                        return {
                            periodTime: startPeriodTimeDayjs.valueOf().toString(),
                            reportDays: String(reportDays),
                        };
                    } catch (error) {
                        request
                            .getLogger(__filename)
                            .error('Failed to fetch periodTime fallback from internal API', error);

                        if (error instanceof InternalAPIException) {
                            throw error;
                        }
                        throw new InternalAPIException({
                            api: 'fetchPeriodTimeFallback',
                            error: createErrorObj(error),
                        });
                    }
                }

                const { periodTime: startPeriodTime, reportDays } = await fetchPeriodTimeFallback(request);
                periodTime = startPeriodTime;
                if (!days) {
                    days = reportDays;
                }
                if (!periodTime) {
                    return response.status(StatusCodes.INTERNAL_SERVER_ERROR).send({
                        error: 'Unable to determine periodTime from internal API',
                    });
                }
            }

            if (!days) {
                const fallbackDaysMap: Record<string, number> = {
                    [REPORT_TYPE.DAILY]: 1,
                    [REPORT_TYPE.WEEKLY]: 7,
                };

                const isMonthly = reportType === REPORT_TYPE.MONTHLY;

                if (fallbackDaysMap[reportType as string] !== undefined) {
                    days = fallbackDaysMap[reportType as string].toString();
                } else if (isMonthly) {
                    days = dayjs
                        .tz(Number(periodTime), timezone as string)
                        .daysInMonth()
                        .toString();
                } else {
                    days = '31';
                }
            }

            const misReportService = new MisReportService(request);

            // Fetch reports from service
            const res = await misReportService.getDataForSchedulingReport({
                periodTime: Number(periodTime),
                days: Number(days),
                timezone: String(timezone),
                localTimezone: String(localTimezone),
                coaCode: String(coaCode),
            });

            return response.status(StatusCodes.OK).send(res);
        } catch (error) {
            request.getLogger(__filename).error(`API Failed path: ${error?.response?.api}`, error);

            if (error instanceof InternalAPIException) {
                return response.status(error.statusCode).send(error);
            }

            return response.status(StatusCodes.INTERNAL_SERVER_ERROR).send({ error: error.message });
        }
    },
};
