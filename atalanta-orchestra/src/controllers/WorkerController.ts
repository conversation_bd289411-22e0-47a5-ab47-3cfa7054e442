import { Request, Response } from 'express';
import { StatusCodes } from 'http-status-codes';
import { InternalAPIException } from '@zeta-atalanta/core/dist/exceptions/InternalAPIException';
import _ from 'lodash';
import { getFormattedTimeDuration, keyMap, getPeriodicity } from '../utils';
import { phaseRunDetails } from './PhaseRunController';
import { STATUS } from '@zeta-atalanta/core/dist/Constants';
import dayjs from 'dayjs';
import { OrchestraService } from '@zeta-atalanta/core/dist/services/OrchestraService';
import { CLOSED_PHASE, CLOSED_PHASE_LABEL } from '../constants';

export const getWorkersMasterList = async (request: Request, response: Response) => {
    try {
        const tenantId = request.params.tenantId;
        const coaId = request.params.coaId;
        const token = request.headers.authorization;
        const orchestraService = new OrchestraService(request);
        let list = await orchestraService.getCoaWorkersMasterList({
            tenantId,
            coaId,
            token
        },{});
        // Change CLOSED to EOP in the phase list
        const workerList = list?.workerList.map((worker: any) => {
            const closedIndex = worker.phase.indexOf(CLOSED_PHASE);
            worker.phase[closedIndex] = CLOSED_PHASE_LABEL;
            return worker;
        });
        return response.status(StatusCodes.OK).send({
            ...list,
            workerList
        });
    } catch (error) {
        // If Backend error
        if (error instanceof InternalAPIException) {
            return response.status(error.statusCode).send(error);
        }
        // In case of other javascript errors
        return response.status(StatusCodes.INTERNAL_SERVER_ERROR).send({ error: error.message });
    }
};

/**
 * To fetch the worker list from the latest batch runs for various phases
 * @param request
 * @param response
 * @returns workers list
 */
export const getWorkersList = async (request: Request, response: Response) => {
    try {
        const phaseRunData: any = await phaseRunDetails(request, response);
        //Make a map of phase and their orchestra runs based on phase
        const phaseWiseBatchRun: {
            [k: string]: any;
        } = {
            startPeriodId: keyMap(phaseRunData?.startPeriodId?.orchestraRuns, 'phase'),
            endPeriodId: keyMap(phaseRunData?.endPeriodId?.orchestraRuns, 'phase'),
        };
        // Various phases to covered in the respective period id
        const phasesWithPeriodId: {
            [k: string]: string[];
        } = {
            startPeriodId: ['EOPI', 'EOFI', 'CLOSED'],
            endPeriodId: ['BOPI', 'BOFI'],
        };

        // Pick the latest batch run from each phase .This batch run would be used to get the Workers
        const parseWorkerData = (periodKey: string) => {
            return phasesWithPeriodId[periodKey].reduce((acc: any, phase: any) => {
                const orchestraRunArray = phaseWiseBatchRun[periodKey][phase];
                if (orchestraRunArray?.length) {
                    const latestBatchRun = orchestraRunArray[orchestraRunArray.length - 1];
                    const workers = latestBatchRun.workerRuns.map((worker: any, index: number) => {
                        const workerID = worker.workerID.split('-');
                        workerID[1] = _.capitalize(workerID[1]);
                        return {
                            phase,
                            sequenceNumber: index + 1,
                            entityType: _.capitalize(worker.entityType),
                            //Capitalise first letter and space before every captital letter
                            workerName: parseWorkerName(worker.workerID),
                            workerId: worker.workerID,
                            startTime: worker.createdAt,
                            updatedTime: worker.updatedAt,
                            timeTaken: getFormattedTimeDuration(
                                parseInt(worker.updatedAt, 10) - parseInt(worker.createdAt, 10),
                            ),
                            totalEntityCount: worker.entityCount.total ?? 0,
                            successfulEntityCount: worker.entityCount.success ?? 0,
                            failedEntityCount: worker.entityCount.failure ?? 0,
                            status: getWorkerEntityStatus(worker.status),
                            periodId: worker?.period?.periodID,
                            periodicity: _.capitalize(getPeriodicity(worker.period)),
                            ...(
                                worker.workerSuccessExecutionID &&
                                { workerSuccessExecutionID : worker?.workerSuccessExecutionID}
                            ),
                            runID: worker?.runID,
                            lastSuccessfulExecutionID: worker.workerSuccessExecutionID ?? '-',
                        };

                    }).sort((a: any,b: any)=> dayjs(a.updatedTime).valueOf() - dayjs(b.updatedTime).valueOf());
                    acc = [...acc, ...workers];
                }
                return acc;
            }, []);
        };
        return response
            .status(StatusCodes.OK)
            .send([...parseWorkerData('startPeriodId'), ...parseWorkerData('endPeriodId')]);
    } catch (error) {
        // If Backend error
        if (error instanceof InternalAPIException) {
            return response.status(error.statusCode).send(error.response);
        }
        // In case of other javascript errors
        return response.status(StatusCodes.INTERNAL_SERVER_ERROR).send({ error: error.message });
    }
};
/**
 * Sample Input - interestAccrualWorker-DAILY-1
 * Sample Output - Interest Accrual Worker-Daily-1
 */
const parseWorkerName = (workerName: string) => {
    return workerName.split('-').slice(0, -2).join('-');
};

function getWorkerEntityStatus(status: string) {
    if (status === STATUS.SUCCEEDED) {
        return STATUS.SUCCESSFUL;
    } else if (status === STATUS.FAILED || status === STATUS.FAILED_RETRIED) {
        return _.capitalize(STATUS.FAILED);
    } else return STATUS.NOT_EXECUTED;
}
