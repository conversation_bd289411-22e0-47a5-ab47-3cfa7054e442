import { Request, Response } from 'express';
import { StatusCodes } from 'http-status-codes';
import { OperationsService } from '@zeta-atalanta/core/dist/services/OperationsService';
import { InternalAPIException } from '@zeta-atalanta/core/dist/exceptions/InternalAPIException';
import { flattenFormVariables, verifyParameters } from '../utils';
import { EODRunProcessQuery, EODRunProcessResponse } from 'index';
import { BUSINESS_KEY_STATUS, BUSINESS_KEY_STATUS_MAP } from '../constants';
import dayjs from 'dayjs';

/**
 * Function to fetch EOD run processes task information for a given workbench
 * Supported query params:
 * catalogId - catalog id of the workbench - optional if form variables are not required
 * workbenchId - workbench id of the workbench mandatory for task variables since we are only considering EOD
 * pageSize - page size for pagination - optional
 * pageNumber - page number for pagination - optional
 * startDate - start date for task creation date range - optional
 * endDate - end date for task creation date range - optional
 * includeFormVariables - whether to include form variables or not - optional
 * ...otherParams - other params to be passed to the API
 * @param request request
 * @param response response
 * @returns void
 */
export const getEODRunProcesses = async (request: Request, response: Response): Promise<any> => {
    const tenantId = request.params.tenantId;
    const token = request.headers.authorization;
    const { catalogId, workbenchId, pageSize, pageNumber, includeFormVariables, startDate, endDate, ...otherParams } =
        request.query as EODRunProcessQuery;
    const checkParameterSanity = verifyParameters({ tenantId, workbenchId });
    if (checkParameterSanity.valid) {
        try {
            const opsService = new OperationsService(request);
            // Fetch the user task info list based on workbench id or other params as provided
            const userTaskResponse = await opsService.getUserTaskInformationList({
                tenantId,
                token,
                workbenchIdList: [workbenchId], // we are only considering EOD run processes hence one workbench id
                ...(pageSize && { pageSize: Number(pageSize) }),
                ...(pageNumber && { pageNumber: Number(pageNumber) }),
                ...(startDate && {
                    taskCreationDateRange: {
                        start: startDate,
                        end: endDate || dayjs().endOf('day').format('YYYY-MM-DDTHH:mm:ss.SSSZZ'),
                    },
                }),
                ...(otherParams && { ...otherParams }),
            });
            // Save total count and user task information list
            const totalCount = userTaskResponse.totalTaskCount;
            const userTaskInformationList = userTaskResponse.userTaskInformationList;
            let cumulativeTaskResponses = await Promise.allSettled([
                ...userTaskInformationList.map(async (userTaskInformation) => {
                    let eodProcess: EODRunProcessResponse = {
                        taskId: userTaskInformation.taskId,
                        taskName: userTaskInformation.taskName,
                        taskStatus: userTaskInformation.taskStatus,
                        taskCreatedAt: userTaskInformation.taskCreatedAt,
                        businessKey: userTaskInformation.businessKey,
                        businessKeyStatus: userTaskInformation.businessKeyStatus,
                        // Set workflow completion status based on business key status
                        /**
                         * business key status - 1 - failed - present in history/variables & not in rhea/variables
                         * business key status - 0 - in progress - not present in history/variables & in rhea/variables
                         * business key status - 10 completed - present in history/variables & not in rhea/variables
                         */
                        workCompletionStatus: BUSINESS_KEY_STATUS_MAP[userTaskInformation.businessKeyStatus] || null,
                        ...(userTaskInformation.assigneeName && { assigneeName: userTaskInformation.assigneeName }),
                        ...(userTaskInformation.taskCompletedAt && {
                            taskCompletedAt: userTaskInformation.taskCompletedAt,
                        }),
                    };
                    if (includeFormVariables && includeFormVariables === 'true' && catalogId) {
                        try {
                            // Fetch form variables for the user task
                            const formVariablesResponse = await opsService.getFormVariables({
                                tenantId,
                                catalogId: Number(catalogId),
                                workbenchId: Number(workbenchId),
                                taskId: userTaskInformation.taskId,
                                token,
                                getCompletedTasks:
                                    userTaskInformation.businessKeyStatus === BUSINESS_KEY_STATUS.COMPLETED
                                        ? true
                                        : false,
                            });
                            // If form variables are present, then populate in the response
                            if (formVariablesResponse && Object.keys(formVariablesResponse).length) {
                                const formVariables = flattenFormVariables(formVariablesResponse);
                                eodProcess = {
                                    ...eodProcess,
                                    ...formVariables,
                                };
                                // Check Phase Run or EoD Run status
                                if (formVariables.triggerSuccess !== undefined) {
                                    const isPhaseRun = !!formVariables.selectedPhase;
                                    let runStatus!: string;
                                    if (formVariables.triggerSuccess) {
                                        // If EoD run was successful, then set status as Retried or Started based on the Start/Retry action from EoD Center
                                        // If Phase run was successful, then set status as Started
                                        runStatus = isPhaseRun ? 'Started' : formVariables.isRetry ? 'Retried' : 'Started';
                                    } else {
                                        // If EoD run or Phase Run has failed, then set status as Failed
                                        runStatus = 'Failed';
                                    }
                                    eodProcess.runStatus = isPhaseRun ? `Phase Run ${runStatus}` : `EoD ${runStatus}`;
                                }
                            }
                        } catch (error) {
                            console.log(error);
                            request
                                .getLogger(__filename)
                                .info(
                                    `Failed fetching form variables for task: ${userTaskInformation.taskId} for workbench: ${workbenchId}}`,
                                );
                        }
                    }
                    return eodProcess;
                }),
            ]);
            return response.status(StatusCodes.OK).send({
                totalCount,
                pageNumber: Number(pageNumber),
                pageSize: Number(pageSize),
                data: cumulativeTaskResponses.map(
                    (taskResponse: PromiseFulfilledResult<EODRunProcessResponse>) => taskResponse.value,
                ),
            });
        } catch (error) {
            request.getLogger(__filename).error('Failed to get EOD run processes', error);
            // If Backend error
            if (error instanceof InternalAPIException) {
                return response.status(error.statusCode).send(error.response);
            }
            // In case of other javascript errors
            return response.status(StatusCodes.INTERNAL_SERVER_ERROR).send({ error: error.message });
        }
    } else {
        return response.status(StatusCodes.BAD_REQUEST).send({ error: checkParameterSanity.message });
    }
};
