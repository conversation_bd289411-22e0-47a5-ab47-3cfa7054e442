import { Request } from 'express';
import { BaseService } from '@zeta-atalanta/core/dist/services/BaseService';
import { InternalAPIException } from '@zeta-atalanta/core/dist/exceptions/InternalAPIException';

// const LLToken =
//     'x-olympus-ingress-session=***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';
const time = '15m';
const baseUrl = 'https://prometheus.internal.mum1-pp.zetaapps.in/api/v1/query';
const baseUrlVictoria =
    'http://vmselect-olympus-zonemonitoring-vmcluster.zonemonitoring.svc.cluster.local:8481/select/0/prometheus/api/v1/query';
// const baseUrlVictoria = 'https://vm-ui.internal.mum1-pp.zetaapps.in/select/0/prometheus/api/v1/query';
const baseUrlOrchBackrub = 'https://orchestra-backrub.internal.mum1-pp.zetaapps.in/orchestra-v3';
export class BackrubService extends BaseService {
    constructor(req: Request, version: string) {
        super(req, {
            baseURL: version === 'v2' ? baseUrlVictoria : baseUrl,
            headers: {
                'Content-Type': 'application/json',
                // Cookie: LLToken,
            },
        });
    }

    public async orchestraAccountsProcessed(requestParams: any, span: string) {
        try {
            const { phaseId } = requestParams;
            this.req.getLogger(__filename).info(`Fetching orchestra account processing data for Phase: ${phaseId}`);
            const params = new URLSearchParams({
                query: `sum(max_over_time(flink_taskmanager_job_task_operator_phase_jobId_shardId_workerClass_successCounter{workerClass="Tightloop",phase="${phaseId}"}[${
                    span || time
                }]))`,
            });
            const response = await this.axios.get(``, {
                params,
                // headers: {
                //     Cookie: LLToken,
                // },
            });
            return response.data;
        } catch (error) {
            this.req.getLogger(__filename).error('Error', { error });
            throw new InternalAPIException({
                api: 'orchestraAccountsProcessed',
                error,
            });
        }
    }

    public async orchestraFunctionalUseCase(requestParams: any, span: string) {
        try {
            const { phaseId, useCase } = requestParams;
            this.req
                .getLogger(__filename)
                .info(`Fetching function processing for Phase: ${phaseId} and use case: ${useCase}`);
            const params = new URLSearchParams({
                query: `sum(max_over_time(flink_taskmanager_job_task_operator_phase_jobId_shardId_functionalUseCase_ledgerCounter{phase="${phaseId}",functionalUseCase="${useCase}"}[${
                    span || time
                }]))`,
            });
            const response = await this.axios.get(``, {
                params,
                // headers: {
                //     Cookie: LLToken,
                // },
            });
            return response.data;
        } catch (error) {
            this.req.getLogger(__filename).error('Error', { error });
            throw new InternalAPIException({
                api: 'orchestraFunctionalUseCase',
                error,
            });
        }
    }

    public async orchestraDelinquencyUseCase(requestParams: any, version: string) {
        try {
            const { span } = requestParams;
            this.req.getLogger(__filename).info(`Fetching orchestra delinquency function`);
            const params = new URLSearchParams({
                // query: `sum(flink_taskmanager_job_task_operator_command_${useCase}_successCounter)`,
                query: `sum (max_over_time(flink_taskmanager_job_task_operator_phase_jobId_shardId_functionalUseCase_ledgerCounter{phase="EOFI",functionalUseCase=~"delinquency.*"}[${
                    span || time
                }]))`,
            });
            const response = await this.axios.get(``, {
                params,
            });
            return response.data;
        } catch (error) {
            this.req.getLogger(__filename).error('Error', { error });
            throw new InternalAPIException({
                api: 'orchestraDelinquencyUseCase',
                error,
            });
        }
    }

    public async daybookAccountsProcessed(requestParams: any, version: string, span: string) {
        try {
            const { phaseId } = requestParams;
            this.req.getLogger(__filename).info(`Fetching daybook account processing data for Phase: ${phaseId}`);
            const params = new URLSearchParams({
                query: `sum%28perseus_job_records_processed%7Boperator%3D'command'%2Cjob_id%3D~'.*${phaseId.toLowerCase()}.*'%7D%29`,
            });
            const response = await this.axios.get(
                `${
                    version === 'v2' ? baseUrlVictoria : baseUrl
                    // }?query=sum%28perseus_job_records_processed%7Boperator%3D%27command%27%2Cjob_id%3D%7E%27.*${phaseId.toLowerCase()}.*%27%7D%29`,
                }?query=sum(max_over_time(perseus_job_records_processed{operator="command",job_id=~".*${phaseId.toLowerCase()}.*"})[${
                    span || time
                }])`,
            );
            return response.data;
        } catch (error) {
            this.req.getLogger(__filename).error('Error', { error });
            throw new InternalAPIException({
                api: 'daybookAccountsProcessed',
                error,
            });
        }
    }

    public async daybookFunctionalUseCase(requestParams: any, span: string) {
        try {
            const { useCase } = requestParams;
            this.req.getLogger(__filename).info(`Fetching daybook function processing for Use case: ${useCase}`);
            const params = new URLSearchParams({
                // query: `sum(flink_taskmanager_job_task_operator_command_${useCase}_successCounter)`,
                query: `sum(max_over_time(flink_taskmanager_job_task_operator_command_${useCase}_successCounter)[${
                    span || time
                }])`,
            });
            const response = await this.axios.get(``, {
                params,
            });
            return response.data;
        } catch (error) {
            this.req.getLogger(__filename).error('Error', { error });
            throw new InternalAPIException({
                api: 'daybookFunctionalUseCase',
                error,
            });
        }
    }

    public async daybookDelinquencyUseCase(requestParams: any, version: string) {
        try {
            const { span } = requestParams;
            this.req.getLogger(__filename).info(`Fetching daybook delinquency function`);
            const params = new URLSearchParams({
                // query: `sum(flink_taskmanager_job_task_operator_command_${useCase}_successCounter)`,
                // query: `sum(max_over_time(flink_taskmanager_job_task_operator_command_UPDATE_LEDGER_ATTACHMENTS_successCounter)[${span}])+sum(max_over_time(flink_taskmanager_job_task_operator_command_BILLED_UNBILLED_ADJUSTMENT_successCounter)[${span}])`,
            });
            const response = await this.axios.get(
                `${
                    version === 'v2' ? baseUrlVictoria : baseUrl
                    // }?query=sum%28perseus_job_records_processed%7Boperator%3D%27command%27%2Cjob_id%3D%7E%27.*${phaseId.toLowerCase()}.*%27%7D%29`,
                }?query=sum(max_over_time(flink_taskmanager_job_task_operator_command_BILLED_UNBILLED_ADJUSTMENT_successCounter)[${
                    span || time
                }])`,
                {
                    params,
                },
            );
            return response.data;
        } catch (error) {
            this.req.getLogger(__filename).error('Error', { error });
            throw new InternalAPIException({
                api: 'daybookDelinquencyUseCase',
                error,
            });
        }
    }

    public async feesSummaryData(requestParams: any, version: string, span: string) {
        try {
            const { phaseId, useCase, formatValue } = requestParams;
            this.req
                .getLogger(__filename)
                .info(`Fetching fees summary data for Phase: ${phaseId} and use case: ${useCase}`);
            const params = new URLSearchParams({
                // query: `sum(max_over_time(flink_taskmanager_job_task_operator_phase_jobId_shardId_functionalUseCase_totalAmount{phase="${phaseId}",functionalUseCase="${useCase}"}[${time}]))/${formatValue}`,
            });
            const response = await this.axios.get(
                `${
                    version === 'v2' ? baseUrlVictoria : baseUrl
                }?query=sum%28max_over_time%28flink_taskmanager_job_task_operator_phase_jobId_shardId_functionalUseCase_totalAmount%7Bphase%3D%22${phaseId}%22%2CfunctionalUseCase%3D%22${useCase}%22%7D%5B${
                    span || time
                }%5D%29%29%2F200/${formatValue}`,
            );
            return response.data;
        } catch (error) {
            this.req.getLogger(__filename).error('Error', { error });
            throw new InternalAPIException({
                api: 'feesSummaryData',
                error,
            });
        }
    }

    public async orchestraTPS(version: string) {
        try {
            this.req.getLogger(__filename).info(`Fetching orchestra TPS`);
            const response = await this.axios.get(
                `${
                    version === 'v2' ? baseUrlVictoria : baseUrl
                }?query=sum(flink_taskmanager_job_task_operator_numRecordsOutPerSecond{task_name="Source:JsonToRecordConverter -> CommandOperator -> (Process, json-file-sink: Writer -> json-file-sink: Committer)", operator_name="CommandOperator"})`,
            );
            return response.data;
        } catch (error) {
            this.req.getLogger(__filename).error('Error', { error });
            throw new InternalAPIException({
                api: 'orchestraTPS',
                error,
            });
        }
    }

    public async daybookTPS(version: string) {
        try {
            this.req.getLogger(__filename).info(`Fetching orchestra TPS`);
            const response = await this.axios.get(
                `${
                    version === 'v2' ? baseUrlVictoria : baseUrl
                }?query=sum%28flink_taskmanager_job_task_operator_numRecordsOutPerSecond%7Btask_name%3D%22Source%3AJsonToRecordConverter+-%3E+CommandOperator+-%3E+%28Process%2C+json-file-sink%3A+Writer+-%3E+json-file-sink%3A+Committer%29%22%2C+operator_name%3D%22CommandOperator%22%7D%29`,
            );
            return response.data;
        } catch (error) {
            this.req.getLogger(__filename).error('Error', { error });
            throw new InternalAPIException({
                api: 'orchestraTPS',
                error,
            });
        }
    }
    public async orchestraTPSNew(version: string, phase: string, span: string, step?: string) {
        try {
            this.req.getLogger(__filename).info(`Fetching orchestra TPS`);
            let queyString = `query=sum(flink_taskmanager_job_task_operator_numRecordsOutPerSecond%7Bnamespace%3D%22orchestra-backrub%22%2Coperator_name%3D~%22WORKER%22%2Cjob_name%3D~%22.*${phase}.*%22%7D)[${
                span || time
            }]`;
            if (step) {
                queyString = queyString + `&step=${step}`;
            }
            let triggerUrl = version === 'v2' ? baseUrlVictoria : baseUrl;
            if (queyString) {
                triggerUrl = `${triggerUrl}?${queyString}`;
            }
            this.req.getLogger(__filename).info('Executing orchestraTPS Query', `${triggerUrl}`);
            const response = await this.axios.get(triggerUrl);
            return response.data;
        } catch (error) {
            this.req.getLogger(__filename).error('Error', { error });
            throw new InternalAPIException({
                api: 'orchestraTPS',
                error,
            });
        }
    }

    public async daybookTPSNew(version: string, phase: string, span: string, step?: string) {
        try {
            this.req.getLogger(__filename).info(`Fetching orchestra TPS`);
            let queyString = `query=sum(flink_taskmanager_job_task_operator_numRecordsOutPerSecond{namespace="perseus-backrub",task_name="Source:JsonToRecordConverter -> CommandOperator -> (Process, json-file-sink: Writer -> json-file-sink: Committer)", operator_name="CommandOperator", job_name=~".*${phase}.*"})[${
                span || time
            }]`;
            if (step) {
                queyString = queyString + `&step=${step}`;
            }
            let triggerUrl = version === 'v2' ? baseUrlVictoria : baseUrl;
            if (queyString) {
                triggerUrl = `${triggerUrl}?${queyString}`;
            }
            this.req.getLogger(__filename).info('Executing daybookTPS Query', `${triggerUrl}`);
            const response = await this.axios.get(triggerUrl);
            return response.data;
        } catch (error) {
            this.req.getLogger(__filename).error('Error', { error });
            throw new InternalAPIException({
                api: 'daybookTPS',
                error,
            });
        }
    }

    public async executeVmUiQuery(queryParams: {
        vmQuery: string;
        version: string;
        step?: string;
        span?: string;
        start?: string;
        end?: string;
        nocache?: string;
        isrange?: string;
    }) {
        try {
            this.req.getLogger(__filename).info(`Fetching orchestra TPS`);

            let queyString = `query=${queryParams.vmQuery}`;
            if (queryParams.span) {
                queyString = queyString + `[${queryParams.span}]`;
            }
            if (queryParams.step) {
                queyString = queyString + `&step=${queryParams.step}`;
            }
            if (queryParams.nocache) {
                queyString = queyString + `&nocache=${queryParams.nocache}`;
            }
            if (queryParams.start) {
                queyString = queyString + `&start=${queryParams.start}`;
            }
            if (queryParams.end) {
                queyString = queyString + `&end=${queryParams.end}`;
            }
            let triggerUrl = queryParams.version === 'v2' ? baseUrlVictoria : baseUrl;

            if (queryParams.isrange && Boolean(queryParams.isrange)) {
                triggerUrl = triggerUrl.replace('query', 'query_range');
            }
            if (queyString) {
                triggerUrl = `${triggerUrl}?${queyString}`;
            }
            this.req.getLogger(__filename).info('Executing VM Query', `${triggerUrl}`);
            const response = await this.axios.get(triggerUrl);
            return response.data;
        } catch (error) {
            this.req.getLogger(__filename).error('Error', { error });
            throw new InternalAPIException({
                api: 'executeVmUiQuery',
                error,
            });
        }
    }
}

export class AuraService extends BaseService {
    constructor(req: Request) {
        super(req, {
            baseURL: 'https://sb1-god-aura.mum1-pp.zetaapps.in/tachyon/',
            headers: {
                'Content-Type': 'application/json',
                // Cookie: LLToken,
            },
        });
    }

    public async statusVerificationReports(requestParams: any) {
        try {
            const { tenantId, phaseId, cbuDate, coaCode, token } = requestParams;
            this.req
                .getLogger(__filename)
                .info(
                    `Fetching status verification reports data for Phase: ${phaseId}, CBU: ${cbuDate} and COA: ${coaCode}`,
                );
            const params = new URLSearchParams({
                phases: phaseId,
                pageSize: '50',
                pageNo: '1',
            });
            const response = await this.axios.get(
                `coa/tenants/${tenantId}/coas/${coaCode}/cbus/${cbuDate}/statusVerificationReports`,
                {
                    params,
                    headers: {
                        ['authorization']: token,
                    },
                },
            );
            return response.data;
        } catch (error) {
            this.req.getLogger(__filename).error('Error', { error });
            throw new InternalAPIException({
                api: 'statusVerificationReports',
                error,
            });
        }
    }
}
export class BackrubOrchestraService extends BaseService {
    constructor(req: Request) {
        super(req, {
            baseURL: baseUrlOrchBackrub,
            headers: {
                'Content-Type': 'application/json',
            },
        });
    }
public async getOrchestraRunData(requestParams: any) {
    const { tenantId, coaId, cbuID, pageId, includeWorkerRuns,  pageSize,  token } = requestParams;
    try {
        this.req.getLogger(__filename).info('Fetching orchestra run data', requestParams);
        const response = await this.axios.get(`/v1/tenants/${tenantId}/coas/${coaId}/orchestraRuns`, {
            headers: { ['authorization']: token },
            params: {
                cbuID: cbuID,
                includeWorkerRuns: includeWorkerRuns,
                pageID: pageId,
                pageSize: pageSize,
            },
        });
        return response.data;
    }
    catch (error) {
        this.req.getLogger(__filename).error(error);
        throw new InternalAPIException({
            api: 'getOrchestraRunData',
            error,
        });
    }
}
}