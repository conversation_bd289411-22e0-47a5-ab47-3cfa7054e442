import { Request } from 'express';
import { BaseService } from '@zeta-atalanta/core/dist/services/BaseService';
import { createErrorObj, transformRequest, transformResponse } from '@zeta-atalanta/core/dist/utils';
import { InternalAPIException } from '@zeta-atalanta/core/dist/exceptions/InternalAPIException';
import { CalendarTypes, CoaTypes } from '../types';
import { CoaService } from './CoaService';

export class CalendarService extends BaseService {
    constructor(req: Request) {
        super(req, {
            baseURL: process.env.TACHYON_URL,
            headers: {
                'Content-Type': 'application/json',
            },
            transformRequest,
            transformResponse,
        });
    }

    public async getAllCalendar(): Promise<CalendarTypes.Calendar[]> {
        const token = this.req.headers.authorization.split(' ')[1];
        const { tenantId } = this.req.params;
        try {
            this.req.getLogger(__filename).info(`Fetching all calendars for tenant ${tenantId}`);
            const response = await this.axios.get(`/calendar/tenants/${tenantId}/calendars`, {
                headers: { Authorization: `Bearer ${token}` },
            });
            return response.data as CalendarTypes.Calendar[];
        } catch (error) {
            this.req.getLogger(__filename).error(`API getAllCalendar failed for tenant ${tenantId}:`, error);
            throw new InternalAPIException({
                api: 'getAllCalendar',
                error: createErrorObj(error),
            });
        }
    }

    public async getCalendarById(): Promise<CalendarTypes.Calendar> {
        const token = this.req.headers.authorization.split(' ')[1];
        const { tenantId, calendarId } = this.req.params;
        try {
            this.req
                .getLogger(__filename)
                .info(`Fetching calendar details for tenant ${tenantId} and calendar ${calendarId}`);
            const response = await this.axios.get(`/calendar/tenants/${tenantId}/calendars/${calendarId}`, {
                headers: { Authorization: `Bearer ${token}` },
            });
            return response.data as CalendarTypes.Calendar;
        } catch (error) {
            this.req
                .getLogger(__filename)
                .error(`API getCalendarById failed for tenant ${tenantId} and calendar ${calendarId}:`, error);
            throw new InternalAPIException({
                api: 'getCalendarById',
                error: createErrorObj(error),
            });
        }
    }

    public async getCurrentCBUByCalendarId(): Promise<CalendarTypes.CBU> {
        const token = this.req.headers.authorization.split(' ')[1];
        const { tenantId, calendarId } = this.req.params;
        try {
            this.req
                .getLogger(__filename)
                .info(`Fetching current CBU for tenant ${tenantId} and calendar ${calendarId}`);
            const response = await this.axios.get(`/calendar/tenants/${tenantId}/calendars/${calendarId}/currentCBU`, {
                headers: { Authorization: `Bearer ${token}` },
            });
            return response.data as CalendarTypes.CBU;
        } catch (error) {
            this.req
                .getLogger(__filename)
                .error(
                    `API getCurrentCBUByCalendarId failed for tenant ${tenantId} and calendar ${calendarId}:`,
                    error,
                );
            throw new InternalAPIException({
                api: 'getCurrentCBUByCalendarId',
                error: createErrorObj(error),
            });
        }
    }

    public async getClocksByCalendarId(): Promise<CalendarTypes.Clock[]> {
        const token = this.req.headers.authorization.split(' ')[1];
        const { tenantId, calendarId } = this.req.params;
        try {
            this.req.getLogger(__filename).info(`Fetching clocks for tenant ${tenantId} and calendar ${calendarId}`);
            const response = await this.axios.get(`/calendar/tenants/${tenantId}/calendars/${calendarId}/clocks`, {
                headers: { Authorization: `Bearer ${token}` },
            });
            return response.data as CalendarTypes.Clock[];
        } catch (error) {
            this.req
                .getLogger(__filename)
                .error(`API getClocksByCalendarId failed for tenant ${tenantId} and calendar ${calendarId}:`, error);
            throw new InternalAPIException({
                api: 'getClocksByCalendarId',
                error: createErrorObj(error),
            });
        }
    }

    public async getInfoByCalendarId(): Promise<CalendarTypes.GetInfoByCalendarIdResponse> {
        const { tenantId, calendarId } = this.req.params;
        try {
            this.req
                .getLogger(__filename)
                .info(`Fetching calendar info for tenant ${tenantId} and calendar ${calendarId}`);
            const coaService = new CoaService(this.req);
            const coas: CoaTypes.Coa[] = await coaService.getAllCoa();
            const coaByCalenderId: CoaTypes.Coa[] = coas?.filter((coa: CoaTypes.Coa) => coa.calendarID === calendarId);

            const currentCBU: CalendarTypes.CBU = await this.getCurrentCBUByCalendarId();

            const clocks: CalendarTypes.Clock[] = await this.getClocksByCalendarId();
            const bookClock: CalendarTypes.Clock = clocks?.find(
                (clock: CalendarTypes.Clock) => clock.type.toUpperCase() === 'BOOK',
            );

            return {
                coas: Array.isArray(coaByCalenderId) && coaByCalenderId.length > 0 ? coaByCalenderId : null,
                currentCBU: currentCBU,
                bookClockDetails: bookClock,
            } as CalendarTypes.GetInfoByCalendarIdResponse;
        } catch (error) {
            this.req
                .getLogger(__filename)
                .error(`API getInfoByCalendarId failed for tenant ${tenantId} and calendar ${calendarId}:`, error);
            if (error instanceof InternalAPIException) {
                throw error;
            }
            throw new InternalAPIException({
                api: 'getInfoByCalendarId',
                error: createErrorObj(error),
            });
        }
    }

    public async getCyclesByCalendarAndClockId(): Promise<CalendarTypes.Cycle[]> {
        const token = this.req.headers.authorization.split(' ')[1];
        const { tenantId, calendarId, clockId } = this.req.params;
        try {
            this.req
                .getLogger(__filename)
                .info(`Fetching cycles for tenant ${tenantId}, calendar ${calendarId} and clock ${clockId}`);
            const response = await this.axios.get(
                `/calendar/tenants/${tenantId}/calendars/${calendarId}/clocks/${clockId}/cycles`,
                {
                    headers: { Authorization: `Bearer ${token}` },
                },
            );
            return response.data as CalendarTypes.Cycle[];
        } catch (error) {
            this.req
                .getLogger(__filename)
                .error(
                    `API getCyclesByCalendarAndClockId failed for tenant ${tenantId}, calendar ${calendarId} and clock ${clockId}:`,
                    error,
                );
            throw new InternalAPIException({
                api: 'getCyclesByCalendarAndClockId',
                error: createErrorObj(error),
            });
        }
    }

    public async getPeriodsByCalendarClockAndCycleId(
        params: CalendarTypes.GetPeriodsByCalendarClockAndCycleIdParams,
    ): Promise<CalendarTypes.Period[]> {
        const token = this.req.headers.authorization.split(' ')[1];
        const { tenantId, calendarId, clockId, cycleId } = this.req.params;
        try {
            this.req
                .getLogger(__filename)
                .info(
                    `Fetching periods for tenant ${tenantId}, calendar ${calendarId}, clock ${clockId} and cycle ${cycleId}`,
                );
            const response = await this.axios.get(
                `/calendar/tenants/${tenantId}/calendars/${calendarId}/clocks/${clockId}/cycles/${cycleId}/periods`,
                {
                    headers: { Authorization: `Bearer ${token}` },
                    params,
                },
            );
            return response.data as CalendarTypes.Period[];
        } catch (error) {
            this.req
                .getLogger(__filename)
                .error(
                    `API getPeriodsByCalendarClockAndCycleId failed for tenant ${tenantId}, calendar ${calendarId}, clock ${clockId} and cycle ${cycleId}:`,
                    error,
                );
            throw new InternalAPIException({
                api: 'getPeriodsByCalendarClockAndCycleId',
                error: createErrorObj(error),
            });
        }
    }
    public async getPeriodByCalendarClockCycleAndPeriodId(
        params: CalendarTypes.GetPeriodsByCalendarClockAndCycleIdParams,
    ): Promise<CalendarTypes.Period[]> {
        const token = this.req.headers?.authorization?.split(' ')[1];
        const { tenantId, calendarId, clockId, cycleId, periodId } = this.req.params;
        try {
            this.req
                .getLogger(__filename)
                .info(
                    `Fetching periods for tenant ${tenantId}, calendar ${calendarId}, clock ${clockId} and cycle ${cycleId}`,
                );
            const response = await this.axios.get(
                `/calendar/tenants/${tenantId}/calendars/${calendarId}/clocks/${clockId}/cycles/${cycleId}/periods/${periodId}`,
                {
                    headers: { Authorization: `Bearer ${token}` },
                    params,
                },
            );
            return response.data as CalendarTypes.Period[];
        } catch (error) {
            this.req
                .getLogger(__filename)
                .error(
                    `API getPeriodsByCalendarClockAndCycleId failed for tenant ${tenantId}, calendar ${calendarId}, clock ${clockId} and cycle ${cycleId}:`,
                    error,
                );
            throw new InternalAPIException({
                api: 'getPeriodsByCalendarClockAndCycleId',
                error: createErrorObj(error),
            });
        }
    }

    public async getSchedulesByCalendarClockAndCycleId(): Promise<CalendarTypes.Schedule[]> {
        const token = this.req.headers.authorization.split(' ')[1];
        const { tenantId, calendarId, clockId, cycleId } = this.req.params;
        try {
            this.req
                .getLogger(__filename)
                .info(
                    `Fetching schedules for tenant ${tenantId}, calendar ${calendarId}, clock ${clockId} and cycle ${cycleId}`,
                );
            const response = await this.axios.get(
                `/calendar/tenants/${tenantId}/calendars/${calendarId}/clocks/${clockId}/cycles/${cycleId}/schedules`,
                {
                    headers: { Authorization: `Bearer ${token}` },
                    params: this.req.query || {},
                },
            );
            return response.data as CalendarTypes.Schedule[];
        } catch (error) {
            this.req
                .getLogger(__filename)
                .error(
                    `API getSchedulesByCalendarClockAndCycleId failed for tenant ${tenantId}, calendar ${calendarId}, clock ${clockId} and cycle ${cycleId}:`,
                    error,
                );
            throw new InternalAPIException({
                api: 'getSchedulesByCalendarClockAndCycleId',
                error: createErrorObj(error),
            });
        }
    }
}
