import { Request } from 'express';
import { BaseService } from '@zeta-atalanta/core/dist/services/BaseService';
import { InternalAPIException } from '@zeta-atalanta/core/dist/exceptions/InternalAPIException';

export class MockService extends BaseService {
    constructor(req: Request) {
        super(req, {
            baseURL: process.env.CERBERUS_URL,
            headers: {
                'Content-Type': 'application/json',
            },
        });
    }

    public async getMockData(): Promise<any> {
        try {
            this.req.getLogger(__filename).info('Fetching mock data for atalanta module');
            const response = await this.axios.get(`/domains/13-admin.India/auth_profiles/D1j4JPR_B0m4dEaxwPEIaA==`, {
                headers: { ['authorization']: `Bearer ${process.env.TOKEN}` },
            });
            return response.data as any;
        } catch (error) {
            this.req.getLogger(__filename).error('Error', { error });
            throw new InternalAPIException({
                api: 'getMockAPIData',
                error,
            });
        }
    }
}
