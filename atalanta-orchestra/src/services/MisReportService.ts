import { Request } from 'express';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import * as _ from 'lodash';
import { getCurrentTimeInTimeZone, getDateInTimezone, getFormattedTimeDurationForMisReport } from '../utils';
import { getPontusAndExtractReports, processPhaseRunToReport, processDagReport } from '../utils/misReportUtil';
import { CalendarTypes, MisReportTypes } from '../types';
import { CalendarService } from './CalendarService';
import { PhaseRunService } from './PhaseRunService';
import { BaseService } from '@zeta-atalanta/core/dist/services/BaseService';
import { TachyonService } from '@zeta-atalanta/core/dist/services/TachyonService';
import { StatusVerificationReportsTypes } from '@zeta-atalanta/core/dist/services/Interfaces/Tachyon';
import { createErrorObj, transformRequest, transformResponse } from '@zeta-atalanta/core/dist/utils';
import { InternalAPIException } from '@zeta-atalanta/core/dist/exceptions/InternalAPIException';
import { CoaService } from './CoaService';
import { DATE_TIME_FORMAT, MIS_ORCHESTRA_PHASES_FOR_UI } from '../constants/misReportConstant';

dayjs.extend(timezone);

export class MisReportService extends BaseService {
    constructor(req: Request) {
        super(req, {
            baseURL: process.env.TACHYON_URL,
            headers: {
                'Content-Type': 'application/json',
            },
            transformRequest,
            transformResponse,
        });
    }

    async getReport(params: MisReportTypes.GetReportParams): Promise<MisReportTypes.GetReportResponse> {
        const { tenantId, calendarId, coaId } = this.req.params;
        const { periodTime, days, timezone, localTimezone, coaCode } = params;
        try {
            this.req
                .getLogger(__filename)
                .info(`Fetching orchestra run report for tenant ${tenantId}, calendar ${calendarId} and coa ${coaId}`);
            const startDate = dayjs(periodTime).tz(timezone).format(DATE_TIME_FORMAT.YYYY_MM_DDTHH_mm_ssZ);
            const endDate = dayjs(periodTime)
                .add(days + 2, 'day')
                .tz(timezone)
                .format(DATE_TIME_FORMAT.YYYY_MM_DDTHH_mm_ssZ);
            const calendarService = new CalendarService(this.req);

            // Fetching periods for the given date
            const periods: CalendarTypes.Period[] = await calendarService.getPeriodsByCalendarClockAndCycleId({
                startDate,
                endDate,
            });

            let currentPeriodIndex = this.findCurrentPeriodIndex(periods, periodTime, timezone);
            if (currentPeriodIndex === -1) {
                throw new Error(`No period details found for Book Date ${getDateInTimezone(periodTime, timezone)}.`);
            }

            const periodsInRange = periods.slice(currentPeriodIndex, currentPeriodIndex + days + 1);

            // Using phase transition history to get start and end time of each phase
            const periodsDataArray: MisReportTypes.PeriodData[] = periodsInRange.reduce((periodData, current) => {
                const { id, phaseTransitionHistory, formattedStartTime } = current;
                const [bookDate] = formattedStartTime.split('T');
                periodData.push({ periodId: id, phaseTimeline: phaseTransitionHistory, bookDate });
                return periodData;
            }, []);

            // Fetch phase runs
            const phaseRunsForEachPeriod: MisReportTypes.FetchPhaseRunsForPeriodsResponse[] =
                await this.fetchReportsForPeriods(periodsDataArray, {coaCode, timezone, localTimezone});

            // Generate report
            const reports = this.processPhaseRunsToReports(phaseRunsForEachPeriod, { timezone, localTimezone });

            // Generate summary
            const summary = this.generateSummary(reports);

            const coaService = new CoaService(this.req);
            const coaDetail = await coaService.getCoaById();

            const coaInfo = {
                coaCode: coaDetail.code,
                coaName: coaDetail.name,
            };
            return { reports, summary, coaInfo };
        } catch (error) {
            this.req
                .getLogger(__filename)
                .error(`API getReport failed for tenant ${tenantId}, calendar ${calendarId}, coa ${coaId}:`, error);
            if (error instanceof InternalAPIException) {
                throw error;
            }
            throw new InternalAPIException({
                api: 'getReport',
                error: createErrorObj(error),
            });
        }
    }

    async getDataForSchedulingReport(
        params: MisReportTypes.GetDataForSchedulingReportParams,
    ): Promise<MisReportTypes.GetDataForSchedulingReportResponse> {
        const { tenantId, calendarId, coaId } = this.req.params;
        const { periodTime, days, timezone, localTimezone, coaCode } = params;
        try {
            this.req
                .getLogger(__filename)
                .info(`Fetching orchestra run report for tenant ${tenantId}, calendar ${calendarId} and coa ${coaId}`);

            const getReportResponse = await this.getReport({
                periodTime: Number(periodTime),
                days: Number(days),
                timezone: decodeURIComponent(String(timezone)),
                localTimezone: decodeURIComponent(String(localTimezone)),
                coaCode: String(coaCode),
            });
            const reports = this.transformGetReportToScheduleReport(getReportResponse);
            return { data: reports };
        } catch (error) {
            this.req
                .getLogger(__filename)
                .error(
                    `API getDataForSchedulingReport failed for tenant ${tenantId}, calendar ${calendarId}, coa ${coaId}:`,
                    error,
                );
            if (error instanceof InternalAPIException) {
                throw error;
            }
            throw new InternalAPIException({
                api: 'getDataForSchedulingReport',
                error: createErrorObj(error),
            });
        }
    }

    private findCurrentPeriodIndex(periods: CalendarTypes.Period[], periodTime: number, timezone: string) {
        const periodDate = dayjs.tz(periodTime, timezone);
        return periods.findIndex((period) => {
            const { startTime } = period;
            const periodStartDate = dayjs.tz(startTime, timezone);
            return (
                periodStartDate.date() === periodDate.date() &&
                periodStartDate.month() === periodDate.month() &&
                periodStartDate.year() === periodDate.year()
            );
        });
    }

    private async fetchReportsForPeriods(periodData: MisReportTypes.PeriodData[], params?: { [k: string]: string }) {
        const phaseRunService = new PhaseRunService(this.req);
        const tachyonService = new TachyonService(this.req);
        const { coaCode } = params || {};
        return Promise.all(
            periodData.map(async ({ periodId, phaseTimeline, bookDate }) => {
                const { startPeriodId } = await phaseRunService.getPhaseRunDetails({ startPeriodId: periodId });
                const {
                    orchestraRuns: orchestraRunsData,
                    pagination: { pagesCount },
                } = startPeriodId;
                const orchestraRuns = [...(orchestraRunsData || [])];
                for (let page = 2; page <= pagesCount; ++page) {
                    const { startPeriodId } = await phaseRunService.getPhaseRunDetails({
                        startPeriodId: periodId,
                        pageId: String(page),
                    });
                    orchestraRuns.push(...(startPeriodId?.orchestraRuns || []));
                }
                let extractReport = {},
                    pontusReport = {};
                try {
                    const svr : StatusVerificationReportsTypes.GetStatusVerifierReportsResponse = await tachyonService.getStatusVerifierReport({
                        tenantId: this.req.params.tenantId,
                        coaCode: coaCode,
                        cbus: bookDate,
                    });
                    const pontusAndExtractReports = getPontusAndExtractReports(svr.contents);
                    extractReport = pontusAndExtractReports.extractReport;
                    pontusReport = pontusAndExtractReports.pontusReport;
                } catch (error) {
                    console.error('Error fetching Status Verifier Report:', error);
                    this.req.getLogger(__filename).error('Error fetching Status Verifier Report', error);
                }
                return { orchestraRuns, phaseTimeline, extractReport, pontusReport };
            }),
        );
    }

    private processPhaseRunsToReports(
        phaseRunsForEachPeriod: MisReportTypes.FetchPhaseRunsForPeriodsResponse[],
        { timezone, localTimezone }: { [k: string]: string },
    ): MisReportTypes.Report[] {
        const reports: MisReportTypes.Report[] = [];
        for (let i = 0; i < phaseRunsForEachPeriod.length - 1; ++i) {
            const phaseReport = processPhaseRunToReport(phaseRunsForEachPeriod[i], phaseRunsForEachPeriod[i + 1], {
                timezone,
                localTimezone,
            });
            if (!phaseReport?.phaseRunInfo) {
                throw new Error('No phase run data found');
            }
            if (phaseReport.phaseRunInfo.length !== MIS_ORCHESTRA_PHASES_FOR_UI.length) {
                const set = new Set();
                phaseReport.phaseRunInfo.forEach(({ phase }) => {
                    set.add(phase);
                });
                const MISSING_PHASES: string[] = [];
                MIS_ORCHESTRA_PHASES_FOR_UI.forEach((phaseKey) => {
                    if (!set.has(phaseKey)) {
                        MISSING_PHASES.push(phaseKey);
                    }
                });
                throw new Error(`No phase run data found for: ${MISSING_PHASES.join(', ')}`);
            }
            const dagReport = processDagReport(phaseRunsForEachPeriod[i] as unknown as MisReportTypes.GetPontusAndExtractReports, { timezone, localTimezone });
            reports.push({...phaseReport, ...dagReport});
        }
        return reports;
    }

    private generateSummary(reports: MisReportTypes.Report[]): MisReportTypes.Summary {
        const summary = {
            totalRun: reports.length,
            totalExecutionTimeInMs: reports.reduce((sum, report) => {
                const { orchestraRunInfo, pontusReport, extractReport } = report;
                const { startTimeInTs: orchestraStartTimeInTs, endTimeInTs: orchestraEndTimeInTs } = orchestraRunInfo;
                const { endTimeInTs: potusEndTimeInTs } = pontusReport || {};
                const { endTimeInTs: extractEndTimeInTs } = extractReport || {};

                const executionEndTimeInMs = Math.max(orchestraEndTimeInTs, potusEndTimeInTs, extractEndTimeInTs);

                return sum + (executionEndTimeInMs - orchestraStartTimeInTs);
            }, 0),
            totalExecutionTime: '',
            averageExecutionTimeInMs: 0,
            averageExecutionTime: '',
        } as MisReportTypes.Summary;

        summary.totalExecutionTime = getFormattedTimeDurationForMisReport(summary.totalExecutionTimeInMs);
        summary.averageExecutionTimeInMs = summary.totalRun
            ? Math.round(summary.totalExecutionTimeInMs / summary.totalRun)
            : 0;
        summary.averageExecutionTime = getFormattedTimeDurationForMisReport(summary.averageExecutionTimeInMs);

        return summary;
    }

    private mapPhaseInfo(phaseRunInfo: MisReportTypes.PhaseRunInfo[]): MisReportTypes.MapPhaseInfoResponse {
        const result: MisReportTypes.MapPhaseInfoResponse = {
            EOPI_START_TIME: '',
            EOPI_END_TIME: '',
            TOTAL_EOPI_TIME: '',
            EOFI_START_TIME: '',
            EOFI_END_TIME: '',
            TOTAL_EOFI_TIME: '',
            EOP_START_TIME: '',
            EOP_END_TIME: '',
            TOTAL_EOP_TIME: '',
            BOPI_START_TIME: '',
            BOPI_END_TIME: '',
            TOTAL_BOPI_TIME: '',
            BOFI_START_TIME: '',
            BOFI_END_TIME: '',
            TOTAL_BOFI_TIME: '',
        };

        MIS_ORCHESTRA_PHASES_FOR_UI.forEach((label, i) => {
            const phase = phaseRunInfo[i];
            result[`${label}_START_TIME` as keyof MisReportTypes.MapPhaseInfoResponse] = phase?.startTime ?? null;
            result[`${label}_END_TIME` as keyof MisReportTypes.MapPhaseInfoResponse] = phase?.endTime ?? null;
            result[`TOTAL_${label}_TIME` as keyof MisReportTypes.MapPhaseInfoResponse] =
                phase?.totalExecutionTime ?? null;
        });

        return result as MisReportTypes.MapPhaseInfoResponse;
    }

    private transformGetReportToScheduleReport(
        orchestraReport: MisReportTypes.GetReportResponse,
    ): MisReportTypes.DataForSchedulingReport[] {
        const { timezone } = this.req.query;
        const { coaInfo, reports, summary } = orchestraReport;
        const flattenReport: MisReportTypes.DataForSchedulingReport[] = reports.map(
            (report: MisReportTypes.Report) => {
                const { orchestraRunInfo, phaseRunInfo, pontusReport, extractReport } = report;
                return {
                    CBU_DATE: orchestraRunInfo.bookDate,
                    CUTOFF_TIME: orchestraRunInfo.cutoffTime,
                    ORCHESTRA_START_TIME: orchestraRunInfo.startTime,
                    ORCHESTRA_END_TIME: orchestraRunInfo.endTime,
                    TOTAL_ORCHESTRA_RUN_TIME: orchestraRunInfo.totalExecutionTime,
                    PONTUS_START_TIME: pontusReport.startTime,
                    PONTUS_END_TIME: pontusReport.endTime,
                    TOTAL_PONTUS_RUN_TIME: pontusReport.totalExecutionTime,
                    EXTRACT_START_TIME: extractReport.startTime,
                    EXTRACT_END_TIME: extractReport.endTime,
                    TOTAL_EXTRACT_RUN_TIME: extractReport.totalExecutionTime,
                    ...this.mapPhaseInfo(phaseRunInfo),
                };
            },
        );

        flattenReport[0] = {
            ...flattenReport[0],
            REPORT_GENERATION_TIME: `${getCurrentTimeInTimeZone(String(timezone))} (${timezone})`,
            COA_CODE: coaInfo.coaCode,
            COA_NAME: coaInfo.coaName,
            AVERAGE_ORCHESTRA_RUN_TIME: summary.averageExecutionTime,
        };

        return flattenReport;
    }
}
