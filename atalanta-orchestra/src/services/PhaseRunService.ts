import { Request } from 'express';
import dayjs from 'dayjs';
import { BaseService } from '@zeta-atalanta/core/dist/services/BaseService';
import { createErrorObj, transformRequest, transformResponse } from '@zeta-atalanta/core/dist/utils';
import { InternalAPIException } from '@zeta-atalanta/core/dist/exceptions/InternalAPIException';
import { OrchestraService } from '@zeta-atalanta/core/dist/services/OrchestraService';
import { DEFAULT_PAGE_SIZE, STATUS } from '../constants';
import { getFormattedTimeDuration } from '../utils';
import { PhaseRunTypes } from '../types/index';
import { OrchestraRunsObject } from '@zeta-atalanta/core/dist/services/Interfaces/Aura';

export class PhaseRunService extends BaseService {
    constructor(req: Request) {
        super(req, {
            headers: {
                'Content-Type': 'application/json',
            },
            transformRequest,
            transformResponse,
        });
    }

    public async getPhaseRunDetails(
        params: PhaseRunTypes.GetPhaseRunDetailsParams,
    ): Promise<PhaseRunTypes.GetPhaseRunDetailsResponse> {
        const { tenantId, coaId } = this.req.params;
        const {
            phase,
            startPeriodId,
            endPeriodId,
            includeWorkerRuns,
            pageSize = `${DEFAULT_PAGE_SIZE}`,
            pageId = '1',
        } = params;
        try {
            this.req.getLogger(__filename).info(`Fetching phase run details for tenant ${tenantId} and coa ${coaId}`);
            const orchestraService = new OrchestraService(this.req);
            const orchestraRunPromise = [
                orchestraService.getOrchestraRunData({
                    tenantId,
                    coaId,
                    ...(phase && { phaseId: phase }),
                    ...(includeWorkerRuns && { includeWorkerRuns }),
                    cbuId: startPeriodId,
                    pageSize: pageSize,
                    pageId: pageId,
                }),
            ];
            if (endPeriodId) {
                orchestraRunPromise.push(
                    orchestraService.getOrchestraRunData({
                        tenantId,
                        coaId,
                        ...(phase && { phaseId: phase }),
                        ...(includeWorkerRuns && { includeWorkerRuns }),
                        cbuId: endPeriodId,
                        pageSize: pageSize,
                        pageId: pageId,
                    }),
                );
            }
            const orchestraRunsResponse = await Promise.all(orchestraRunPromise);
            orchestraRunsResponse.map(({ orchestraRuns }) => {
                return orchestraRuns.map((orchestraRun, index) =>
                    this.processOrchestraRunToPhaseRun(orchestraRun, index + 1),
                );
            });
            return {
                startPeriodId: orchestraRunsResponse[0],
                endPeriodId: orchestraRunsResponse[1],
            };
        } catch (error) {
            this.req
                .getLogger(__filename)
                .error(`API getPhaseRunDetails failed for tenant ${tenantId} and coa ${coaId}:`, error);
            if (error instanceof InternalAPIException) {
                throw error;
            }
            throw new InternalAPIException({
                api: 'getPhaseRunDetails',
                error: createErrorObj(error),
            });
        }
    }

    private processOrchestraRunToPhaseRun(orchestraRun: OrchestraRunsObject, sequenceNumber: number) {
        orchestraRun.sequenceNumber = sequenceNumber + 1;
        if (orchestraRun.status === STATUS.FAILED || orchestraRun.status === STATUS.FAILED_RETRIED) {
            orchestraRun.status = STATUS.FAILED;
        } else if (orchestraRun.status !== STATUS.SUCCEEDED) {
            orchestraRun.status = STATUS.PROGRESS;
        }
        if (orchestraRun.finishedAt) {
            const { startedAt, finishedAt } = orchestraRun;
            const totalTimeTakenInProcess = dayjs(finishedAt).diff(dayjs(startedAt));
            orchestraRun.timeTaken = getFormattedTimeDuration(totalTimeTakenInProcess);
        }
        return orchestraRun;
    }
}
