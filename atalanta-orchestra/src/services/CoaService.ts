import { Request } from 'express';
import { BaseService } from '@zeta-atalanta/core/dist/services/BaseService';
import { createErrorObj, transformRequest, transformResponse } from '@zeta-atalanta/core/dist/utils';
import { InternalAPIException } from '@zeta-atalanta/core/dist/exceptions/InternalAPIException';
import { CoaTypes } from '../types';

export class CoaService extends BaseService {
    constructor(req: Request) {
        super(req, {
            baseURL: process.env.TACHYON_URL,
            headers: {
                'Content-Type': 'application/json',
            },
            transformRequest,
            transformResponse,
        });
    }

    public async getAllCoa(): Promise<CoaTypes.Coa[]> {
        const token = this.req.headers.authorization.split(' ')[1];
        const { tenantId } = this.req.params;
        try {
            this.req.getLogger(__filename).info(`Fetching all coa for tenant ${tenantId}`);
            const response = await this.axios.get(`/coa/tenants/${tenantId}/coas`, {
                headers: { Authorization: `Bearer ${token}` },
            });
            return response.data as CoaTypes.Coa[];
        } catch (error) {
            this.req.getLogger(__filename).error(`API getAllCoa failed for tenant ${tenantId}:`, error);
            throw new InternalAPIException({
                api: 'getAllCoa',
                error: createErrorObj(error),
            });
        }
    }

    public async getCoaById(): Promise<CoaTypes.Coa> {
        const token = this.req.headers.authorization.split(' ')[1];
        const { tenantId, coaId } = this.req.params;
        try {
            this.req.getLogger(__filename).info(`Fetching coa details for tenant ${tenantId} and coa ${coaId}`);
            const response = await this.axios.get(`/coa/tenants/${tenantId}/coas/${coaId}`, {
                headers: { Authorization: `Bearer ${token}` },
            });
            return response.data as CoaTypes.Coa;
        } catch (error) {
            this.req
                .getLogger(__filename)
                .error(`API getCoaById failed for tenant ${tenantId} and coa ${coaId}:`, error);
            throw new InternalAPIException({
                api: 'getCoaById',
                error: createErrorObj(error),
            });
        }
    }

    public async getPeriodDetailsByPeriodId(
        queryParams: CoaTypes.PeriodDetailsRequest,
    ): Promise<CoaTypes.PeriodDetailsResponse> {
        const token = this.req.headers?.authorization?.split(' ')[1];
        const { tenantId, coaId } = this.req.params;
        const { periodId } = queryParams;
        try {
            this.req
                .getLogger(__filename)
                .info(`Fetching period details for tenant ${tenantId}, coa ${coaId}, period ${periodId}`);

            const response = await this.axios.get(`/coa/tenants/${tenantId}/coas/${coaId}/periods`, {
                params: { periodID: periodId },
                headers: { Authorization: `Bearer ${token}` },
            });
            return response.data[0];
        } catch (error) {
            this.req
                .getLogger(__filename)
                .error(
                    `API getPeriodDetailsByPeriodId failed for tenant ${tenantId}, coa ${coaId}, period ${periodId}:`,
                    error,
                );
            throw new InternalAPIException({
                api: 'getPeriodDetailsByPeriodId',
                error: createErrorObj(error),
            });
        }
    }
}
