import { CBU_STATUS_TRIGGER_MAP, ERROR_MESSAGE_INVALID_PARAMS, STATUS } from '../constants';
import { CBUStatusType, EODRunProcessVariables, RheaTaskVariableModel } from '../types';
import * as _ from 'lodash';
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { COATreeModel } from '../types/index';
import { GetAuthResponse } from '@zeta-atalanta/core/dist/services/Interfaces/Cipher';
import { DATE_TIME_FORMAT } from '../constants/misReportConstant';

dayjs.extend(duration);
dayjs.extend(utc);
dayjs.extend(timezone);

/**
 * Function to fetch trigger values keys required for HTTP request
 * @param status coa phase status
 */
export function getCurrentTriggerValueFromStatus(status: CBUStatusType): string | null {
    return status ? CBU_STATUS_TRIGGER_MAP[status.toUpperCase() as CBUStatusType]?.currentTrigger || null : null;
}

/**
 * Function to check for nullish or nullish type
 * values
 * @param value provided value
 * @returns boolean
 */
export function checkNullish(value: string): boolean {
    return value !== undefined && value !== null && value !== 'undefined' && value !== 'null';
}

/**
 * Function to check whether given parameters are valid
 * @param parameters parameters
 *
 */
export function verifyParameters(parameters: { [k: string]: string }): { valid: boolean; message?: string } {
    let message = '';
    Object.keys(parameters).reduce((previousValue: string, currentValue: string) => {
        return (message += checkNullish(parameters[currentValue]) ? '' : `, ${currentValue}`);
    }, message);
    const valid = message.trim().length ? false : true;
    return {
        valid,
        ...(!valid && { message: `${ERROR_MESSAGE_INVALID_PARAMS}${message.slice(1)}` }),
    };
}

/**
 * Function to cumulate all the different kinds of status of coa phase
 * under a bracket of 3 status - success, failed or in progress
 * @param coaStatus coa phase status
 */
export function getCoAStatus(coaStatus: string): string {
    const cappedCoAStatus = coaStatus.toUpperCase(); // for sanity
    if (cappedCoAStatus === STATUS.FAILED || cappedCoAStatus === STATUS.FAILED_RETRIED) {
        return STATUS.FAILED;
    } else if (
        cappedCoAStatus === STATUS.COMPLETED ||
        cappedCoAStatus === STATUS.SUCCEEDED ||
        cappedCoAStatus === STATUS.SUCCESS
    ) {
        return STATUS.SUCCESS;
    } else {
        return STATUS.PROGRESS;
    }
}

/**
 * Function to get formatted time taken based on the difference between startTime & endTime
 * @param {number} duration timestamp in number
 * @return {string} formatted time
 */
export function getFormattedTimeDuration(duration: number, ignoreZeroTimeFormat = true) {
    let format = '';
    const durationObj = dayjs.duration(duration);
    if (ignoreZeroTimeFormat) {
        if (durationObj.get('d')) {
            format += 'D[Day] ';
        }
        /**
         * We can skip adding hour if it is 0 unless the day has some value
         */
        if (format !== '' || durationObj.get('hour')) {
            format += 'H[Hr] ';
        }
        /**
         * We can skip adding minute if it is 0 unless the day, hour has some value
         */
        if (format !== '' || durationObj.get('minute')) {
            format += 'm[Min] ';
        }
        /**
         * We can skip adding sec if it is 0 unless the day, hour,minute has some value
         */
        if (format !== '' || durationObj.get('second')) {
            format += 's[Sec]';
        }
    } else {
        format = 'D[Day] H[Hr] m[Min] s[Sec]';
    }

    return durationObj.format(format);
}

/**
 * Function to get formatted time taken based on the difference between startTime & endTime
 * @param {number} duration timestamp in number
 * @return {string} formatted time
 */
export function getFormattedTimeDurationForMisReport(duration: number, ignoreZeroTimeFormat = true) {
    let format = '';
    const durationObj = dayjs.duration(duration);
    if (ignoreZeroTimeFormat) {
        if (durationObj.get('d')) {
            format += 'D [day] ';
        }
        /**
         * We can skip adding hour if it is 0 unless the day has some value
         */
        if (format !== '' || durationObj.get('hour')) {
            format += 'H [hr] ';
        }
        /**
         * We can skip adding minute if it is 0 unless the day, hour has some value
         */
        if (format !== '' || durationObj.get('minute')) {
            format += 'm [min] ';
        }
        /**
         * We can skip adding sec if it is 0 unless the day, hour,minute has some value
         */
        if (format !== '' || durationObj.get('second')) {
            format += 's [sec]';
        }
    } else {
        format = 'D [day] H [hr] m [min] s [sec]';
    }

    return durationObj.format(format);
}


/**
 * Sample Input - interestAccrualWorker-DAILY-1
 * Sample Output - Interest Accrual Worker-Daily-1
 */
export const parseWorkerName = (workerName: string = '') => {
    return workerName
        .split('-')
        .map((worker: string) => {
            const w = _.startCase(worker).split(' ');
            if (w.length > 1) {
                return w.join(' ');
            } else {
                return _.capitalize(worker);
            }
        })
        .join('-');
};

/**
 * Function to convert worker name
 * Sample Input - Interest Accrual Worker-Daily-1
 * Sample Output - interestAccrualWorker
 * @param workerName string value of given worker
 */
export function shortenWorkerName(workerName: string = '') {
    return workerName
        .split('-')[0]
        .split(' ')
        .map((str: string, idx) => {
            if (idx === 0) {
                return _.lowerFirst(str);
            }
            return str;
        })
        .join('');
}

/**
 * Function to iterate through COA nodes and add
 * a path parameter to be used in ledger list COA tree view
 * @param coaTree coa nodes response
 * @param path path string
 */
export function addPath(coaTree: COATreeModel[], path = '') {
    const tree: COATreeModel[] = [];
    for (let index = 0; index < coaTree.length; index++) {
        const element = coaTree[index];
        tree[index] = {
            ...element,
            path: path + element.nodeValue + '/',
        };
        if (element.children && element.children.length) {
            tree[index].children = addPath(element.children, tree[index].path);
        }
    }
    return tree;
}

export function addParentPath(data: any, tree: any) {
    return data.map((item: any) => {
        const path: string = findPath(item.parentNodeID, tree);
        return {
            ...item,
            parentPath: path,
            parentPathFull: fullPath(path),
            parentPathShort: shortPath(path),
        };
    });
}

export function findPath(nodeId: string, tree: any): string {
    let path = '';
    for (let index = 0; index < tree.length; index++) {
        const element = tree[index];
        if (element.nodeID === nodeId.toString()) {
            path = element.path;
            break;
        } else if (element.children && element.children.length) {
            path = findPath(nodeId, element.children);
            if (path) {
                break;
            }
        }
    }
    return path;
}

export function shortPath(value: string) {
    const data: string[] = value.split('/');
    return data[data.length - 2];
}

export function fullPath(value: string) {
    const data: string[] = value.split('/');
    return data.slice(0, data.length - 2).join('/');
}
export const SKIP_LEDGERS_PAYLOAD = { status: 'SKIPPED', reason: '', reasonCode: '' };

/**
 * * Sample Input - Interest Calculation Worker
 * Sample Output - interestcalculationworker
 */
export const getUnParsedWorkerName = (workerName: string) => {
    return workerName
        .split(' ')
        .map((worker: string) => {
            return _.lowerFirst(worker);
        })
        .join('');
};

export const PHASE = 'phase';

export const ALL_WORKER = 'all';
export const WORKER_ID_MAP: { [key: string]: { name: string; id: string } } = {
    interestcalculationworker: {
        name: 'Interest Calculation Worker',
        id: 'interestcalculationworker',
    },
    daybookworker: {
        name: 'Daybook Worker',
        id: 'daybookworker',
    },
    monthlyinterest: {
        name: 'Monthly Interest',
        id: 'monthlyinterest',
    },
};

export const LEDGER_ENTITY = 'ledger';

export const DEFAULT_ENTITY_SORTBY = 'status';
export const DEFAULT_SORT_ORDER = 'desc';
export const STATUS_FAILURE = 'FAILURE';
export const STATUS_SKIPPED = 'SKIPPED';

export const WORKER_FIELD = 'workerId';
export const LEGDER_FIELD = 'ledgerID';

export const GET_LEDGER_DEFAULT_PAGE_SIZE = 10;
export const GET_LEDGER_DEFAULT_PAGE_ID = 1;

export const getWorkerNameFromId = (workerID: string) => {
    let workerName = workerID;
    let i = 0;
    if (workerID) {
        // we are removing '-' twice from the end of the workerID - first time for removing unique id, second time for removing periodicity
        while (i < 2) {
            workerName = workerID.substring(0, workerName.lastIndexOf('-'));
            i++;
        }
    }

    return workerName;
};

/**
 * a hashmap of give data based on the key for O(1) access
 * @param data - iterable data source
 * @param key - key to be used to creat the map
 * @returns object with key and data object as property
 */
export const keyMap = (data: any, key: string) => {
    if (!data) return {};
    return data.reduce((acc: any, obj: any) => {
        if (acc[obj[key]]) {
            acc[obj[key]].push(obj);
        } else {
            acc[obj[key]] = [obj];
        }
        return acc;
    }, {});
};

//To get periodicity based on the difference between nextPeriodStartTime & periodStartTime
export const getPeriodicity = (period: any) => {
    //Value of one day
    const oneDay = 1000 * 60 * 60 * 24;
    const timeDifference = period.nextPeriodStartTime - period.periodStartTime;
    //Calculation to get the day difference in number of days
    const daysDifference = Math.round(timeDifference / oneDay);
    if (daysDifference == 1) {
        return 'DAILY';
    } else if (daysDifference == 7) {
        return 'WEEKLY';
    } else if (daysDifference >= 28 && daysDifference <= 31) {
        return 'MONTHLY';
    } else if (daysDifference >= 90 && daysDifference <= 92) {
        return 'QUATERLY';
    } else if (daysDifference >= 180 && daysDifference <= 184) {
        return 'HALF YEARLY';
    } else if (daysDifference == 365 || daysDifference == 366) {
        return 'YEARLY';
    } else return '--';
};

/**
 * Function to get task form variables and flatten them to a single object
 * @param formVariables task variables
 * @returns flattened object
 */
export const flattenFormVariables = (formVariables: RheaTaskVariableModel): EODRunProcessVariables => {
    const flattenedFormVariables: { [key: string]: unknown } = {};
    Object.keys(formVariables).forEach((key) => {
        const variable = formVariables[key];
        if (variable?.value && variable?.value !== 'false') {
            flattenedFormVariables[key] = variable.value;
        }
    });
    return flattenedFormVariables;
};

export const getAuthDetails = (authResponse: GetAuthResponse) => {
    const AUTH_IDENTIFIER = '@authProfile.';
    const getAuthProfileAndDomainFromUserResourceJID = (userResourceJID: string) => {
        if (userResourceJID?.length) {
            const userDetails = userResourceJID.split(AUTH_IDENTIFIER);
            if (userDetails.length === 2) {
                return {
                    authProfileId: userDetails[0],
                    userJID: userResourceJID.split('/')[0],
                };
            }
            return null;
        }
        return null;
    };
    const authDetails = getAuthProfileAndDomainFromUserResourceJID(authResponse.userResourceJid);
    const tenantId = authResponse.tenant,
        sandboxId = authResponse.sandbox,
        userJID = authDetails.userJID,
        authProfileId = authDetails.authProfileId;

    return { tenantId, sandboxId, userJID, authProfileId };
};

export function getCurrentTimeInTimeZone(timezone: string) {
    const currentTime = dayjs().tz(timezone);
    return currentTime.format(DATE_TIME_FORMAT.DD_MMM_YYYY_h_mm_ss_A);
}

export function getDateInTimezone(ts: number, timezone: string): string {
    return dayjs.tz(ts, timezone).format(DATE_TIME_FORMAT.DD_MMM_YYY);
}
