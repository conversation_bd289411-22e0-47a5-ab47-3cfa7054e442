import { MisReportTypes } from 'MisReportTypes';
import { getFormattedTimeDurationForMisReport } from '.';
import {
    DATE_TIME_FORMAT,
    MIS_ORCHESTRA_PHASES,
    MIS_ORCHESTRA_PHASES_MAP,
    MIS_ORCHESTRA_END_PHASES,
    MIS_ORCHESTRA_START_PHASES,
    PHASE_RUN_INFO_KEYS,
    PHASE_RUN_KEYS,
    ORCHESTRA_RUN_INFO_KEYS,
    PONTUS_CODES,
    EXTRACTS_CODES
} from '../constants/misReportConstant';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import { CoaTypes } from 'CoaTypes';
import { StatusVerificationReportsTypes } from '@zeta-atalanta/core/dist/services/Interfaces/Tachyon';

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(customParseFormat);

export function processPhaseRunToReport(
    startPhasesData: MisReportTypes.FetchPhaseRunsForPeriodsResponse,
    endPhasesData: MisReportTypes.FetchPhaseRunsForPeriodsResponse,
    params: { [k: string]: string },
): { phaseRunInfo: MisReportTypes.PhaseRunInfo[]; orchestraRunInfo: MisReportTypes.OrchestraRunInfo } {
    const { timezone, localTimezone } = params;
    const { orchestraRuns: startPhasesOrchestraRun, phaseTimeline: startPhaseTimeline } = startPhasesData;
    const { orchestraRuns: endPhasesOrchestraRun, phaseTimeline: endPhaseTimeline } = endPhasesData;

    const phaseTimeline: CoaTypes.TransitionMap = {};
    MIS_ORCHESTRA_START_PHASES.forEach((phase: string) => {
        phaseTimeline[phase] = startPhaseTimeline[phase];
    });
    MIS_ORCHESTRA_END_PHASES.forEach((phase: string) => {
        phaseTimeline[phase] = endPhaseTimeline[phase];
    });

    const phaseStartRuns =
        startPhasesOrchestraRun?.filter((run: MisReportTypes.OrchestraRun) =>
            MIS_ORCHESTRA_START_PHASES.includes(run.phase as (typeof MIS_ORCHESTRA_START_PHASES)[number]),
        ) || [];
    if (!phaseStartRuns || phaseStartRuns.length === 0) {
        return;
    }
    const bookDate = dayjs(
        phaseStartRuns[0][PHASE_RUN_KEYS.CBU_START_DATE as keyof MisReportTypes.OrchestraRun] as string,
    ).format(DATE_TIME_FORMAT.DD_MMM_YYY);

    const phaseEndRuns =
        endPhasesOrchestraRun?.filter((run: MisReportTypes.OrchestraRun) =>
            MIS_ORCHESTRA_END_PHASES.includes(run.phase as (typeof MIS_ORCHESTRA_END_PHASES)[number]),
        ) || [];
    const phaseRuns: MisReportTypes.OrchestraRun[] = [...phaseStartRuns, ...phaseEndRuns];
    const phaseInfo = phaseRuns.reduce((phaseRun, currentPhaseRun) => {
        const phase = currentPhaseRun.phase as keyof typeof MIS_ORCHESTRA_PHASES_MAP;
        if (!phaseRun[phase]) {
            phaseRun[phase] = {
                [PHASE_RUN_INFO_KEYS.ORCHESTRA_RUNS]: [],
            };
        }
        phaseRun[phase][PHASE_RUN_INFO_KEYS.ORCHESTRA_RUNS].push(currentPhaseRun);
        return phaseRun;
    }, {} as MisReportTypes.PhaseInfoMap);

    if (typeof phaseInfo !== 'object') {
        throw new Error(`Failed to retrieve phase run information for Book Date ${bookDate}.`);
    }

    /**
     * Bug Ticket: https://zeta-saas.atlassian.net/browse/SPB-2506
     * If orchestra runs do not contain data for any phases then this means workers were not associated with the phase
     * We can assume that there were no failed runs for the Phase.
     * So, adding dummy orchestra runs for the phase to get failed runs.
     * */
    if (Object.keys(phaseInfo).length !== MIS_ORCHESTRA_PHASES.length) {
        const set = new Set(Object.keys(phaseInfo));
        MIS_ORCHESTRA_PHASES.forEach((phaseKey) => {
            if (!set.has(phaseKey)) {
                phaseInfo[phaseKey as keyof typeof MIS_ORCHESTRA_PHASES_MAP] = {
                    [PHASE_RUN_INFO_KEYS.ORCHESTRA_RUNS]: createOrchestraRunDataForPhase(phaseKey),
                };
            }
        });
    }

    let totalOrchestraRunTimeInMs = 0,
        orchestraRunStartTime,
        orchestraRunStartTimeInTs,
        orchestraRunLocalStartTime,
        orchestraRunEndTime,
        orchestraRunEndTimeInTs,
        orchestraRunLocalEndTime;
    Object.keys(phaseInfo).forEach((phase) => {
        const runs = phaseInfo[phase as keyof typeof MIS_ORCHESTRA_PHASES_MAP][PHASE_RUN_INFO_KEYS.ORCHESTRA_RUNS];
        delete phaseInfo[phase as keyof typeof MIS_ORCHESTRA_PHASES_MAP][PHASE_RUN_INFO_KEYS.ORCHESTRA_RUNS];

        const totalRuns = runs.length;

        const phaseKey = phase === MIS_ORCHESTRA_PHASES_MAP.CLOSED ? 'EOP' : phase;

        // Get timings from transition map
        const startedAt = phaseTimeline[phase]?.startTime;
        const finishedAt = phaseTimeline[phase]?.endTime;

        if (!finishedAt) {
            throw new Error(`${phaseKey} has not been completed for Book Date ${bookDate}.`);
        }

        const totalPhaseExecutionTimeInMs = finishedAt ? dayjs(finishedAt).diff(dayjs(startedAt)) : 0;

        totalOrchestraRunTimeInMs += totalPhaseExecutionTimeInMs;

        phaseInfo[phase as keyof typeof MIS_ORCHESTRA_PHASES_MAP] = {
            ...phaseInfo[phase as keyof typeof MIS_ORCHESTRA_PHASES_MAP],
            [PHASE_RUN_INFO_KEYS.PHASE]: phaseKey,
            [PHASE_RUN_INFO_KEYS.FAILED_RUNS]: totalRuns - 1,
            [PHASE_RUN_INFO_KEYS.START_TIME]: dayjs(startedAt)
                .tz(String(timezone))
                .format(DATE_TIME_FORMAT.DD_MMM_YYYY_h_mm_ss_A),
            [PHASE_RUN_INFO_KEYS.LOCAL_START_TIME]: dayjs(startedAt)
                .tz(String(localTimezone))
                .format(DATE_TIME_FORMAT.DD_MMM_YYYY_h_mm_ss_A),
            [PHASE_RUN_INFO_KEYS.END_TIME]: dayjs(finishedAt)
                .tz(String(timezone))
                .format(DATE_TIME_FORMAT.DD_MMM_YYYY_h_mm_ss_A),
            [PHASE_RUN_INFO_KEYS.LOCAL_END_TIME]: dayjs(finishedAt)
                .tz(String(localTimezone))
                .format(DATE_TIME_FORMAT.DD_MMM_YYYY_h_mm_ss_A),
            [PHASE_RUN_INFO_KEYS.TOTAL_EXECUTION_TIME]:
                getFormattedTimeDurationForMisReport(totalPhaseExecutionTimeInMs),
            [PHASE_RUN_INFO_KEYS.TOTAL_EXECUTION_TIME_IN_MS]: totalPhaseExecutionTimeInMs,
        };

        if (phase === MIS_ORCHESTRA_PHASES_MAP.EOPI) {
            orchestraRunStartTimeInTs = dayjs(startedAt).valueOf();
            orchestraRunStartTime = dayjs(startedAt)
                .tz(String(timezone))
                .format(DATE_TIME_FORMAT.DD_MMM_YYYY_h_mm_ss_A);
            orchestraRunLocalStartTime = dayjs(startedAt)
                .tz(String(localTimezone))
                .format(DATE_TIME_FORMAT.DD_MMM_YYYY_h_mm_ss_A);
        }

        if (phase === MIS_ORCHESTRA_PHASES_MAP.BOFI) {
            orchestraRunEndTimeInTs = dayjs(finishedAt).valueOf();
            orchestraRunEndTime = dayjs(finishedAt).tz(String(timezone)).format(DATE_TIME_FORMAT.DD_MMM_YYYY_h_mm_ss_A);
            orchestraRunLocalEndTime = dayjs(finishedAt)
                .tz(String(localTimezone))
                .format(DATE_TIME_FORMAT.DD_MMM_YYYY_h_mm_ss_A);
        }
    });
    const phaseRunInfo = [...MIS_ORCHESTRA_START_PHASES, ...MIS_ORCHESTRA_END_PHASES].map(
        (phase) => phaseInfo[phase as keyof typeof MIS_ORCHESTRA_PHASES_MAP],
    );
    const orchestraRunInfo = {
        [ORCHESTRA_RUN_INFO_KEYS.TOTAL_EXECUTION_TIME_IN_MS]: totalOrchestraRunTimeInMs,
        [ORCHESTRA_RUN_INFO_KEYS.TOTAL_EXECUTION_TIME]: getFormattedTimeDurationForMisReport(totalOrchestraRunTimeInMs),
        [ORCHESTRA_RUN_INFO_KEYS.BOOK_DATE]: bookDate,
        [ORCHESTRA_RUN_INFO_KEYS.BOOK_DATE_IN_TS]: dayjs(
            phaseStartRuns[0][PHASE_RUN_KEYS.CBU_START_DATE as keyof MisReportTypes.OrchestraRun] as string,
        ).valueOf(),
        [ORCHESTRA_RUN_INFO_KEYS.START_TIME]: orchestraRunStartTime,
        [ORCHESTRA_RUN_INFO_KEYS.END_TIME]: orchestraRunEndTime,
        [ORCHESTRA_RUN_INFO_KEYS.LOCAL_START_TIME]: orchestraRunLocalStartTime,
        [ORCHESTRA_RUN_INFO_KEYS.LOCAL_END_TIME]: orchestraRunLocalEndTime,
        [ORCHESTRA_RUN_INFO_KEYS.START_TIME_IN_TS]: orchestraRunStartTimeInTs,
        [ORCHESTRA_RUN_INFO_KEYS.END_TIME_IN_TS]: orchestraRunEndTimeInTs,
        [ORCHESTRA_RUN_INFO_KEYS.CUTOFF_TIME]: '_',
        [ORCHESTRA_RUN_INFO_KEYS.CUTOFF_EXECUTION_METHOD]: '_',
    } as unknown as MisReportTypes.OrchestraRunInfo;

    return { phaseRunInfo, orchestraRunInfo };
}

export function createOrchestraRunDataForPhase(phase: string): MisReportTypes.OrchestraRun[] {
    return [
        {
            executionID: 1,
            phase,
            status: 'SUCCEEDED',
        },
    ];
}

export function getPontusAndExtractReports(
    svr: StatusVerificationReportsTypes.ReportContent[],
): MisReportTypes.GetPontusAndExtractReports {
    const misRelatedReports = svr?.filter(
        ({ phase, report }: StatusVerificationReportsTypes.ReportContent) =>
            report?.attributes?.IS_EOD_EXECUTION_MIS === 'TRUE' && phase === 'CLOSED',
    );
    if (!misRelatedReports || misRelatedReports.length === 0) {
        return { pontusReport: undefined, extractReport: undefined };
    }
    const pontusStatusVerificationReport = misRelatedReports.find(
        ({ report }: StatusVerificationReportsTypes.ReportContent) =>
            report?.statusVerifierCode === PONTUS_CODES.STATUS_VERIFIER_CODE,
    );
    const extractStatusVerificationReport = misRelatedReports.find(
        ({ report }: StatusVerificationReportsTypes.ReportContent) =>
            report?.statusVerifierCode === EXTRACTS_CODES.STATUS_VERIFIER_CODE,
    );

    let pontusReport: MisReportTypes.StatusVerifierReportsExtract = {
            startTime: undefined,
            endTime: undefined,
        },
        extractReport: MisReportTypes.StatusVerifierReportsExtract = {
            startTime: undefined,
            endTime: undefined,
        };

    if (pontusStatusVerificationReport) {
        const { tasks } = pontusStatusVerificationReport.report;
        const { kpis: pontusStartKpis } =
            tasks?.find((task: StatusVerificationReportsTypes.Task) => task?.code === PONTUS_CODES.START_TASK_CODE) || {};
        const { kpis: pontusEndKpis } =
            tasks?.find((task: StatusVerificationReportsTypes.Task) => task?.code === PONTUS_CODES.END_TASK_CODE) || {};
        if (pontusStartKpis && pontusStartKpis.length) {
            const { metric } =
                pontusStartKpis.find((kpi: StatusVerificationReportsTypes.KPI) => kpi.code === PONTUS_CODES.START_KPI_CODE) || {};
            pontusReport.startTime = metric?.value;
        }
        if (pontusEndKpis && pontusEndKpis.length) {
            const { metric } =
                pontusEndKpis.find((kpi: StatusVerificationReportsTypes.KPI) => kpi.code === PONTUS_CODES.END_KPI_CODE) || {};
            pontusReport.endTime = metric?.value;
        }
    }

    if (extractStatusVerificationReport) {
        const { tasks } = extractStatusVerificationReport.report;
        const { kpis: extractKpis } =
            tasks.find((task: StatusVerificationReportsTypes.Task) => task?.code === EXTRACTS_CODES.TASK_CODE) || {};

        if (extractKpis && extractKpis.length) {
            const { metric: startMetric } =
                extractKpis?.find((kpi: StatusVerificationReportsTypes.KPI) => kpi?.code === EXTRACTS_CODES.START_KPI_CODE) || {};
            const { metric: endMetric } =
                extractKpis?.find((kpi: StatusVerificationReportsTypes.KPI) => kpi?.code === EXTRACTS_CODES.END_KPI_CODE) || {};
            extractReport.startTime = startMetric?.value;
            extractReport.endTime = endMetric?.value;
        }
    }

    return { pontusReport, extractReport };
}

export function processDagReport(
    dagReports: MisReportTypes.GetPontusAndExtractReports,
    params: { [k: string]: string },
): { pontusReport: MisReportTypes.PontusReport; extractReport: MisReportTypes.ExtractReport } {
    const { pontusReport, extractReport } = dagReports;
    const { timezone, localTimezone } = params;
    const pontusStartTimeTs = pontusReport?.startTime ? dayjs(pontusReport.startTime).valueOf() : undefined;
    const pontusEndTimeTs = pontusReport?.endTime ? dayjs(pontusReport.endTime).valueOf() : undefined;
    const pontusTotalExecutionTimeInMs = pontusEndTimeTs && pontusStartTimeTs ? pontusEndTimeTs - pontusStartTimeTs : 0;

    const pr: MisReportTypes.PontusReport = {
        startTime: pontusStartTimeTs
            ? dayjs(pontusStartTimeTs).tz(timezone).format(DATE_TIME_FORMAT.DD_MMM_YYYY_h_mm_ss_A)
            : '_',
        localStartTime: pontusStartTimeTs
            ? dayjs(pontusStartTimeTs).tz(localTimezone).format(DATE_TIME_FORMAT.DD_MMM_YYYY_h_mm_ss_A)
            : '_',
        startTimeInTs: pontusStartTimeTs || 0,
        endTime: pontusEndTimeTs
            ? dayjs(pontusEndTimeTs).tz(timezone).format(DATE_TIME_FORMAT.DD_MMM_YYYY_h_mm_ss_A)
            : '_',
        localEndTime: pontusEndTimeTs
            ? dayjs(pontusEndTimeTs).tz(localTimezone).format(DATE_TIME_FORMAT.DD_MMM_YYYY_h_mm_ss_A)
            : '_',
        endTimeInTs: pontusEndTimeTs || 0,
        totalExecutionTimeInMs: pontusTotalExecutionTimeInMs,
        totalExecutionTime: pontusTotalExecutionTimeInMs
            ? getFormattedTimeDurationForMisReport(pontusTotalExecutionTimeInMs)
            : '_',
    };

    const extractStartTimeTs = extractReport?.startTime ? dayjs(extractReport.startTime).valueOf() : undefined;
    const extractEndTimeTs = extractReport?.endTime ? dayjs(extractReport.endTime).valueOf() : undefined;
    const extractTotalExecutionTimeInMs =
        extractEndTimeTs && extractStartTimeTs ? extractEndTimeTs - extractStartTimeTs : 0;

    const er: MisReportTypes.PontusReport = {
        startTime: extractStartTimeTs
            ? dayjs(extractStartTimeTs).tz(timezone).format(DATE_TIME_FORMAT.DD_MMM_YYYY_h_mm_ss_A)
            : '_',
        localStartTime: extractStartTimeTs
            ? dayjs(extractStartTimeTs).tz(localTimezone).format(DATE_TIME_FORMAT.DD_MMM_YYYY_h_mm_ss_A)
            : '_',
        startTimeInTs: extractStartTimeTs || 0,
        endTime: extractEndTimeTs
            ? dayjs(extractEndTimeTs).tz(timezone).format(DATE_TIME_FORMAT.DD_MMM_YYYY_h_mm_ss_A)
            : '_',
        localEndTime: extractEndTimeTs
            ? dayjs(extractEndTimeTs).tz(localTimezone).format(DATE_TIME_FORMAT.DD_MMM_YYYY_h_mm_ss_A)
            : '_',
        endTimeInTs: extractEndTimeTs || 0,
        totalExecutionTimeInMs: extractTotalExecutionTimeInMs,
        totalExecutionTime: extractTotalExecutionTimeInMs
            ? getFormattedTimeDurationForMisReport(extractTotalExecutionTimeInMs)
            : '_',
    };

    return { pontusReport: pr, extractReport: er };
}