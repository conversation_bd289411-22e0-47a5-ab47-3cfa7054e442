import { CoaTypes } from 'CoaTypes';
import { PHASE_RUN_INFO_KEYS, MIS_ORCHESTRA_PHASES_MAP } from '../constants/misReportConstant';

export namespace MisReportTypes {
    export type PhaseName = keyof typeof MIS_ORCHESTRA_PHASES_MAP;

    export interface OrchestraRun {
        executionID: number;
        phase: string;
        status: string;
    }

    export interface PhaseRunInfo {
        [PHASE_RUN_INFO_KEYS.ORCHESTRA_RUNS]: OrchestraRun[];
        [PHASE_RUN_INFO_KEYS.PHASE]?: string;
        [PHASE_RUN_INFO_KEYS.FAILED_RUNS]?: number;
        [PHASE_RUN_INFO_KEYS.START_TIME]?: string;
        [PHASE_RUN_INFO_KEYS.LOCAL_START_TIME]?: string;
        [PHASE_RUN_INFO_KEYS.END_TIME]?: string;
        [PHASE_RUN_INFO_KEYS.LOCAL_END_TIME]?: string;
        [PHASE_RUN_INFO_KEYS.TOTAL_EXECUTION_TIME]?: string;
        [PHASE_RUN_INFO_KEYS.TOTAL_EXECUTION_TIME_IN_MS]?: number;
    }

    export interface Phase {
        name: PhaseName;
    }

    export type PhaseInfoMap = {
        [key in PhaseName]?: PhaseRunInfo;
    };

    export interface OrchestraRunInfo {
        totalExecutionTimeInMs: number;
        totalExecutionTime: string;
        bookDate: string;
        bookDateInTs: string;
        startTime: string;
        endTime: string;
        startTimeInTs: number;
        endTimeInTs: number;
        localStartTime: string;
        localEndTime: string;
        cutoffTime: string;
        cutoffExecutionMethod: string;
    }
    
    export interface PontusReport {
        totalExecutionTimeInMs: number;
        totalExecutionTime: string;
        startTime: string;
        endTime: string;
        localStartTime: string;
        localEndTime: string;
        startTimeInTs: number;
        endTimeInTs: number;
    }

    export interface ExtractReport {
        totalExecutionTimeInMs: number;
        totalExecutionTime: string;
        startTime: string;
        endTime: string;
        localStartTime: string;
        localEndTime: string;
        startTimeInTs: number;
        endTimeInTs: number;
    }

    export interface Report {
        phaseRunInfo: PhaseRunInfo[];
        orchestraRunInfo: OrchestraRunInfo;
        pontusReport: PontusReport;
        extractReport: ExtractReport;
    }

    export interface Summary {
        totalRun: number;
        totalExecutionTimeInMs: number;
        totalExecutionTime: string;
        averageExecutionTimeInMs: number;
        averageExecutionTime: string;
    }

    export interface CoaInfo {
        coaName: string;
        coaCode: string;
    }

    export interface GetReportRequest {
        tenantId: number | string;
        calendarId: string;
        coaId: string;
        clockId: string;
        cycleId: string;
        periodTime: number | string;
        startDate: string;
        endDate: string;
        timezone: string;
        days: string;
    }
    export interface GetReportResponse {
        reports: Report[];
        summary: Summary;
        coaInfo: CoaInfo;
    }
    export interface GetReportParams {
        periodTime: number;
        days: number;
        timezone: string;
        localTimezone: string;
        coaCode?: string;
    }

    export interface FetchPhaseRunsForPeriodsResponse {
        orchestraRuns: MisReportTypes.OrchestraRun[];
        phaseTimeline: CoaTypes.TransitionMap;
    }

    export interface PeriodData {
        periodId: string;
        phaseTimeline: CoaTypes.TransitionMap;
        bookDate?: string;
    }

    export interface GetDataForSchedulingReportParams extends GetReportParams {
        isExport?: boolean;
        reportType?: 'Daily' | 'Weekly' | 'Monthly';
    }

    export interface MapPhaseInfoResponse {
        EOPI_START_TIME: string;
        EOPI_END_TIME: string;
        TOTAL_EOPI_TIME: string;

        EOFI_START_TIME: string;
        EOFI_END_TIME: string;
        TOTAL_EOFI_TIME: string;

        EOP_START_TIME: string;
        EOP_END_TIME: string;
        TOTAL_EOP_TIME: string;

        BOPI_START_TIME: string;
        BOPI_END_TIME: string;
        TOTAL_BOPI_TIME: string;

        BOFI_START_TIME: string;
        BOFI_END_TIME: string;
        TOTAL_BOFI_TIME: string;
    }
    export interface DataForSchedulingReport extends MapPhaseInfoResponse {
        COA_CODE?: string;
        COA_NAME?: string;
        REPORT_GENERATION_TIME?: string;
        AVERAGE_ORCHESTRA_RUN_TIME?: string;

        CBU_DATE: string;
        CUTOFF_TIME: string;

        ORCHESTRA_START_TIME: string;
        ORCHESTRA_END_TIME: string;
        TOTAL_ORCHESTRA_RUN_TIME: string;

        PONTUS_START_TIME: string;
        PONTUS_END_TIME: string;
        TOTAL_PONTUS_RUN_TIME: string;

        EXTRACT_START_TIME: string;
        EXTRACT_END_TIME: string;
        TOTAL_EXTRACT_RUN_TIME: string;
    }

    export interface GetDataForSchedulingReportResponse {
        data: DataForSchedulingReport[];
    }

    export interface StatusVerifierReportsExtract {
        startTime: string | undefined;
        endTime: string | undefined;
    }

    export interface GetPontusAndExtractReports {
        pontusReport: StatusVerifierReportsExtract;
        extractReport: StatusVerifierReportsExtract;
    }
}
