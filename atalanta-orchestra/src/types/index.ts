import { Express } from 'express';
import { GetLedgerResponse } from '@zeta-atalanta/core/dist/services/Interfaces/Tachyon';
export interface AppModule {
    name: string;
    setup: (app: Express) => void;
}

export interface CBUStatusTriggerParameters {
    currentTrigger: string;
    nextTrigger: string;
    nextPhase: CBUStatusType;
}

export type CBUStatusType = 'INACTIVE' | 'BOPI' | 'BOFI' | 'ACTIVE' | 'EOPI' | 'EOFI' | 'CLOSED';

export type CBUStatusTrigger = {
    [key in CBUStatusType]: CBUStatusTriggerParameters;
};
export interface GetPhaseTriggerStatus {
    coaID?: string;
    periodID?: string;
    status: STATUS;
    trigger?: string;
    tenantID?: string;
    entityTrackersProgress: TrackerProgress[];
    transitionMap: TransitionMapObject;
}
export interface TrackerProgress {
    coaID?: string;
    periodID?: string;
    status: STATUS;
    entityType: string;
    tenantID?: string;
    trigger: string;
    transitionMap: TransitionMapObject;
}
export type STATUS =
    | 'COMPLETED'
    | 'INITIATED'
    | 'TRACKERS_NOTIFIED'
    | 'TRACKERS_ACKED'
    | 'FAILED'
    | 'SUCCESS'
    | 'FAILURE';
export type TransitionMapObject = {
    [key in STATUS]?: {
        startTime: number;
        endTime?: number;
    };
};
export interface BatchRunCoASummary {
    coaId: string;
    coaPhaseStatus?: string;
    startTime?: number;
    endTime?: number;
    failedAt?: number;
}
export interface BatchRunWorkerSummary {
    coaId: string;
    totalWorkers?: number;
    totalFailedWorkers?: number;
    totalSuccessfulWorkers?: number;
    status?: string;
    jobId?: string;
    executionId?: number;
    workerId?: string;
    createdAt?: string;
    updatedAt?: string;
    startedAt?: string;
    finishedAt?: string;
    timeTaken?: string;
}
export interface BatchRunPhaseCycleData extends BatchRunCoASummary {
    phaseDetails?: PhaseDetails[];
}
export interface BatchRunPhaseCycleResponse {
    data: BatchRunPhaseCycleData;
}
export interface BatchRunCoASummaryResponse {
    data: BatchRunCoASummary[];
}
export interface BatchRunWorkerSummaryResponse {
    data: BatchRunWorkerSummary[];
}
export interface PhaseDetails {
    phaseName: string;
    initiatedStartTime?: number;
    completedStartTime?: number;
}
export interface COATreeModel {
    aggregatedBalance: number;
    children: COATreeModel[];
    nodeID: string | number;
    nodeType: string;
    nodeValue: string;
    path?: string;
}

export interface LedgerResponse extends GetLedgerResponse {
    parentPath: string;
    parentPathFull: string;
    parentPathShort: string;
    workerName: string;
}

export interface GetAllPeriodsQuery {
    clockType?: string;
    periodicity?: string;
    startDate?: string;
    endDate?: string;
    pageNo?: number;
    pageSize?: number;
}

export interface GetAllPeriodsResponse {
    sequenceNumber: number;
    endCbuSequenceNumber: number;
    startTime: number;
    nextPeriodStartTime: number;
    periodicity: string;
    cycleCode: string;
    cycleName: string;
    clockType: string;
    periodStatus: string;
}

export interface GetAllPeriodsAtalantaResponse {
    data: GetAllPeriodsResponse[];
}
export interface SkipLegdersRequest {
    tenantId: string;
    coaId: string;
    periodId: string;
    phase: string;
    ledgerId: string;
    worker?: string;
    token: string;
}

export interface FailedWorkerResponse {
    id: string;
    name: string;
    periodId: string;
}


export type WORKFLOW_COMPLETION_TYPE = 'COMPLETED' | 'FAILED' | 'IN_PROGRESS';
export interface EODRunProcessResponse {
    taskId: string; // task ID
    taskName: string; // task name
    taskStatus: string; // status of the task
    taskCreatedAt: number; // when was the task created
    businessKey: string; // business key of the task
    businessKeyStatus: number; // business key status of the task - not that required
    workCompletionStatus: WORKFLOW_COMPLETION_TYPE | null; // manually created status to check if the workflow is completed or not
    assigneeName?: string; // who is the task assigned to
    taskCompletedAt?: string | number; // when was the task completed
    runStatus?: string; // whether the trigger was successful or not
    [key: string]: any; // other form variables if present
}

export interface EODRunProcessVariables {
    isRetry?: boolean;
    triggerSuccess?: boolean;
    [key: string]: any;
}

export interface EODRunProcessQuery {
    workbenchId: string;
    catalogId?: string;
    startDate?: string;
    endDate?: string;
    pageSize?: string;
    pageNumber?: string;
    includeFormVariables?: string;
    [key: string]: string;
}

export interface RheaTaskVariableModel {
    [key: string]: {
        value: any;
        type: string;
        valueInfo?: Object;
    };
}

export * from './CalendarTypes';
export * from './CoaTypes';
export * from './MisReportTypes';
export * from './PhaseRunTypes';