import { CoaTypes } from 'CoaTypes';
export namespace CalendarTypes {
    export interface GetAllCalendarsRequest {
        tenantId: number;
    }
    export interface Calendar {
        id: string;
        tenantID: string;
        code: string;
        name: string;
        timezone: string;
        description: string;
        status: string;
        weekStartOffset: number;
        yearStartDate: string;
    }

    export interface GetCurrentCBUByCalendarIdRequest {
        tenantId: number;
        calendarId: number;
    }
    export interface PhaseTransitionHistory {
        [phase: string]: {
            startTime?: number;
            endTime?: number;
        };
    }

    export interface CBU {
        id: string;
        tenantID: string;
        startCbuSequenceNumber: number;
        status: string;
        calendarID: string;
        clockID: string;
        cycleID: string;
        endCbuSequenceNumber: number;
        sequenceNumber: number;
        tags: string[];
        startTime: number;
        nextPeriodStartTime: number;
        formattedStartTime: string;
        formattedNextPeriodStartTime: string;
        liveProcessingState: string;
        phaseTransitionHistory: PhaseTransitionHistory;
    }

    export interface GetClocksByCalendarIdRequest {
        tenantId: number;
        calendarId: number;
    }
    export interface Clock {
        id: string;
        tenantID: string;
        code: string;
        name: string;
        type: string;
        cbuDuration: number;
        startTime: number;
        cbusInAdvance: number;
        status: string;
        calendarID: string;
        cbuCycleID: string;
    }

    export interface GetInfoByCalendarIdRequest {
        tenantId: number;
        calendarId: number;
    }
    export interface GetInfoByCalendarIdResponse {
        coas: CoaTypes.Coa[];
        currentCBU: CBU;
        bookClockDetails: Clock;
    }

    export interface GetCyclesByCalendarAndClockIdRequest {
        tenantId: number | string;
        calendarId: string;
        clockId: string;
    }
    export interface Cycle {
        id: string;
        tenantID: string;
        periodicity: string;
        code: string;
        startCbuSequenceNumber: number;
        status: string;
        calendarID: string;
        clockID: string;
    }

    export interface GetPeriodsByCalendarClockAndCycleIdRequest {
        tenantId: number | string;
        calendarId: string;
        clockId: string;
        cycleId: string;
    }

    export interface GetPeriodsByCalendarClockAndCycleIdParams {
        startDate: string;
        endDate: string;
    }
    export interface Period {
        id: string;
        tenantID: string;
        startCbuSequenceNumber: number;
        status: string;
        calendarID: string;
        clockID: string;
        cycleID: string;
        endCbuSequenceNumber: number;
        sequenceNumber: number;
        tags: string[];
        startTime: number;
        nextPeriodStartTime: number;
        formattedStartTime: string;
        formattedNextPeriodStartTime: string;
        liveProcessingState: string;
        phaseTransitionHistory: PhaseTransitionHistory;
    }
    export interface GetSchedulesByCalendarClockAndCycleIdRequest {
        tenantId: number | string;
        calendarId: string;
        clockId: string;
        cycleId: string;
    }
    export interface Scheduler {
        type: string;
        jobID: string;
    }

    export interface Schedule {
        id: number;
        code: string;
        tenantID: number;
        calendarID: number;
        clockID: number;
        cbuCycleID: number;
        name: string;
        description: string;
        cronExpression: string;
        scheduler: Scheduler;
        state: string;
        createdAt: string;
        modifiedAt: string;
    }
}
