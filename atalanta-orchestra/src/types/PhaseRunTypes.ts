import { OrchestraRunResponse } from '@zeta-atalanta/core/dist/services/Interfaces/Aura';

export namespace PhaseRunTypes {
    export interface GetPhaseRunDetailsParams {
        phase?: string;
        startPeriodId: string;
        endPeriodId?: string;
        includeWorkerRuns?: string;
        pageSize?: string;
        pageId?: string;
    }

    export interface GetPhaseRunDetailsResponse {
        startPeriodId: OrchestraRunResponse;
        endPeriodId: OrchestraRunResponse;
    }
}
