export namespace CoaTypes {
    export interface GetAllCoaRequest {
        tenantId: number;
    }
    export interface Coa {
        id: string;
        tenantID: string;
        code: string;
        name: string;
        currency: string;
        description: string;
        status: string;
        calendarID: string;
        rootNodeID: string | null;
        attributes: { [key: string]: string } | null;
    }

    export interface TransitionMap {
        [phase: string]: {
            startTime: number;
            endTime: number | null;
        };
    }

    export interface PeriodDetailsRequest {
        periodId: string;
    }

    export interface PeriodDetailsResponse {
        id: string;
        tenantID: string;
        transitionMap: TransitionMap;
    }
}
