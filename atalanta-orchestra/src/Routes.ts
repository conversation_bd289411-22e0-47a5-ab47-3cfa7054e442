import { Router } from 'express';
import { getCoASummaryData, getPhaseCycleData, getWorkerSummaryData } from './controllers/BatchRunSummaryController';
import { getAllPeriods } from './controllers/OrchestraPeriodsController';
import { skipLedgers } from './controllers/SkipLegdersController';
import { getWorkerDetails } from './controllers/WorkerDetailsController';
import { getFailedLedgersForWorker, getFailedLedgersForAllWorkers } from './controllers/FailedLedgersController';
import { getWorkersList, getWorkersMasterList } from './controllers/WorkerController';
import { getSkippedLedgers } from './controllers/SkippedLedgersController';
import { getFailedWorkers } from './controllers/FailedWorkersController';
import { getPhaseRunDetails } from './controllers/PhaseRunController';
import { getEODRunProcesses } from './controllers/EodRunProcessController';
import {
    visualizationData,
    currentCBUAndType,
    summaryData,
    getCurrentTransactions,
    getCurrentTransactionsNew,
    getWorkersListStats,
    executeVmUiQuery,
} from './controllers/BackrubVisualizationController';
import CoaController from './controllers/CoaController';
import CalendarController from './controllers/CalendarController';
import MisReportController from './controllers/MisReportController';

export const Routes = (route: Router) => {
    route.get('/orchestra/api/v1/tenants/:tenantId/phase-cycle', getPhaseCycleData);
    route.get('/orchestra/api/v1/tenants/:tenantId/periods/:periodId/coa-summary', getCoASummaryData);
    route.get('/orchestra/api/v1/tenants/:tenantId/periods/:periodId/worker-summary', getWorkerSummaryData);
    route.get('/orchestra/api/v1/tenants/:tenantId/worker-details', getWorkerDetails);
    route.get(
        '/orchestra/api/v1/tenants/:tenantId/coas/:coaId/periods/:periodId/phases/:phaseId/workers/:workerId/failed-ledgers',
        getFailedLedgersForWorker,
    );
    route.get(
        '/orchestra/api/v1/tenants/:tenantId/coas/:coaId/periods/:periodId/phases/:phaseId/coa-failed-ledgers',
        getFailedLedgersForAllWorkers,
    );
    route.get('/orchestra/api/v1/tenants/:tenantId/coas/:coaId/workers-master-list', getWorkersMasterList);
    route.get('/orchestra/api/v1/tenants/:tenantId/calendars/:calendarId/all-periods', getAllPeriods);
    route.post('/orchestra/api/v1/tenants/:tenantId/periods/:periodId/skip-ledgers', skipLedgers);
    route.post('/orchestra/api/v1/tenants/:tenantId/coas/:coaId/periods/:periodId/skipped-ledgers', getSkippedLedgers);
    route.get('/orchestra/api/v1/tenants/:tenantId/coas/:coaId/failed-workers', getFailedWorkers);
    route.get('/orchestra/api/v1/tenants/:tenantId/coas/:coaId/phase-runs', getPhaseRunDetails);
    route.get('/orchestra/api/v1/tenants/:tenantId/coas/:coaId/workers-list', getWorkersList);
    route.get('/orchestra/api/v1/tenants/:tenantId/eod-run-processes', getEODRunProcesses);
    route.get('/orchestra/api/v1/tenants/:tenantId/phase/:phaseId/type/:type/backrub-visualization', visualizationData);
    route.get('/orchestra/api/v1/tenants/:tenantId/phase/current-cbu-and-type', currentCBUAndType);
    route.get('/orchestra/api/v1/tenants/:tenantId/phase/:phaseId/summary-data', summaryData);
    route.get('/orchestra/api/v1/tenants/:tenantId/type/:type/current-transactions', getCurrentTransactions);
    route.get('/orchestra/api/v1/tenants/:tenantId/coas/:coaId/workers-list-stats', getWorkersListStats);
    route.get(
        '/orchestra/api/v1/tenants/:tenantId/phase/:phase/type/:type/current-transactions',
        getCurrentTransactionsNew,
    );
    route.get('/orchestra/api/v1/tenants/:tenantId/execute-vmui-query', executeVmUiQuery);
    route.get('/orchestra/api/v1/tenants/:tenantId/coas', CoaController.getAllCoa);
    route.get('/orchestra/api/v1/tenants/:tenantId/coas/:coaId', CoaController.getCoaById);
    route.get(
        '/orchestra/api/v1/tenants/:tenantId/coas/:coaId/periods/:periodId',
        CoaController.getPeriodDetailsByPeriodId,
    );
    route.get('/orchestra/api/v1/tenants/:tenantId/calendars', CalendarController.getAllCalendar);
    route.get('/orchestra/api/v1/tenants/:tenantId/calendars/:calendarId', CalendarController.getCalendarById);
    route.get(
        '/orchestra/api/v1/tenants/:tenantId/calendars/:calendarId/currentCBU',
        CalendarController.getCurrentCBUByCalendarId,
    );
    route.get(
        '/orchestra/api/v1/tenants/:tenantId/calendars/:calendarId/clocks',
        CalendarController.getClocksByCalendarId,
    );
    route.get(
        '/orchestra/api/v1/tenants/:tenantId/calendars/:calendarId/infoByCalendarId',
        CalendarController.getInfoByCalendarId,
    );
    route.get(
        '/orchestra/api/v1/tenants/:tenantId/calendars/:calendarId/clocks/:clockId/cycles',
        CalendarController.getCyclesByCalendarAndClockId,
    );
    route.get(
        '/orchestra/api/v1/tenants/:tenantId/calendars/:calendarId/clocks/:clockId/cycles/:cycleId/periods',
        CalendarController.getPeriodsByCalendarClockAndCycleId,
    );
    route.get(
        '/orchestra/api/v1/tenants/:tenantId/calendars/:calendarId/clocks/:clockId/cycles/:cycleId/periods/:periodId',
        CalendarController.getPeriodByCalendarClockCycleAndPeriodId,
    );
    route.get(
        '/orchestra/api/v1/tenants/:tenantId/calendars/:calendarId/clocks/:clockId/cycles/:cycleId/schedules',
        CalendarController.getSchedulesByCalendarClockAndCycleId,
    );
    route.get(
        '/orchestra/api/v1/tenants/:tenantId/calendars/:calendarId/coas/:coaId/clocks/:clockId/cycles/:cycleId/get-report',
        MisReportController.getReport,
    );
    route.get(
        '/orchestra/api/v1/tenants/:tenantId/calendars/:calendarId/coas/:coaId/clocks/:clockId/cycles/:cycleId/schedule-report-data',
        MisReportController.getDataForSchedulingReport,
    );
    return route;
};
