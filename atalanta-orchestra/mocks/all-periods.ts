export const mockAllPeriodsResponse = {
    data: [
        {
            sequenceNumber: 2,
            endCbuSequenceNumber: 2,
            startTime: 1262370600000,
            nextPeriodStartTime: 1262457000000,
            periodicity: 'DAILY',
            cycleCode: 'SYS_CLK_COA_Weekly_Dev_Sanity_CBU',
            clockType: 'SYSTEM',
            periodStatus: 'INACTIVE',
        },
        {
            sequenceNumber: 3,
            endCbuSequenceNumber: 3,
            startTime: 1262457000000,
            nextPeriodStartTime: 1262543400000,
            periodicity: 'DAILY',
            cycleCode: 'SYS_CLK_COA_Weekly_Dev_Sanity_CBU',
            clockType: 'SYSTEM',
            periodStatus: 'INACTIVE',
        },
    ],
};

export const mockAllClocks = [
    {
        id: '4597511537766917256',
        tenantID: '140793',
        code: 'SYS_CLK_COA_Weekly_Dev_Sanity',
        name: 'SYS_CLK_COA_Weekly_Dev_Sanity',
        type: 'SYSTEM',
        cbuDuration: 1,
        startTime: 1262284200000,
        cbusInAdvance: 7,
        status: 'ENABLED',
        calendarID: '1751072138966405248',
        cbuCycleID: '1079745508859266395',
    },
];

export const mockAllCycles = [
    {
        id: '1079745508859266395',
        tenantID: '140793',
        periodicity: 'DAILY',
        code: 'SYS_CLK_COA_Weekly_Dev_Sanity_CBU',
        startCbuSequenceNumber: 1,
        status: 'ENABLED',
        calendarID: '1751072138966405248',
        clockID: '4597511537766917256',
    },
];

export const mockAllPeriods = [
    {
        id: '8723648260529433058',
        tenantID: '140793',
        startCbuSequenceNumber: 2,
        status: 'INACTIVE',
        calendarID: '1751072138966405248',
        clockID: '4597511537766917256',
        cycleID: '1079745508859266395',
        endCbuSequenceNumber: 2,
        sequenceNumber: 2,
        startTime: 1262370600000,
        nextPeriodStartTime: 1262457000000,
        formattedStartTime: '2010-01-02T00:00+05:30[Asia/Kolkata]',
        formattedNextPeriodStartTime: '2010-01-03T00:00+05:30[Asia/Kolkata]',
    },
    {
        id: '7303931645146562768',
        tenantID: '140793',
        startCbuSequenceNumber: 3,
        status: 'INACTIVE',
        calendarID: '1751072138966405248',
        clockID: '4597511537766917256',
        cycleID: '1079745508859266395',
        endCbuSequenceNumber: 3,
        sequenceNumber: 3,
        startTime: 1262457000000,
        nextPeriodStartTime: 1262543400000,
        formattedStartTime: '2010-01-03T00:00+05:30[Asia/Kolkata]',
        formattedNextPeriodStartTime: '2010-01-04T00:00+05:30[Asia/Kolkata]',
    },
];
