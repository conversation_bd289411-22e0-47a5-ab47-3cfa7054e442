export const mockOrchestraRunData = {
    pagination: {
        pageID: 1,
        pageSize: 50,
        pagesCount: 1,
        totalCount: 1,
    },
    orchestraRuns: [
        {
            id: 'job-e611e479-746c-4ed2-8c2f-dce60e47d0a0-1',
            executionID: 1,
            coaID: '7242184752828075514',
            cbuID: '776295113466452341',
            cbuStartDate: '2022-08-28',
            cbuEndDate: '2022-08-28',
            phase: 'BOFI',
            jobSpecID: 'ORC_151613_RubyOrchestra',
            dagName: 'ORC_151613_RubyOrchestra-0.0.0',
            status: 'FAILED',
            createdAt: '2023-02-28T07:23:41.701353+00:00',
            updatedAt: '2023-02-28T07:26:29.184415+00:00',
            startedAt: '2023-02-28T07:23:41.755279+00:00',
            finishedAt: '2023-02-28T07:26:29.158713+00:00',
            workerRuns: [
                {
                    runID: 'job-e611e479-746c-4ed2-8c2f-dce60e47d0a0-1-couponExpiryWorker-DAILY-1',
                    executionID: '1',
                    workerID: 'couponExpiryWorker-DAILY-1',
                    status: 'FAILED',
                    entityType: 'ledger',
                    createdAt: '2023-02-28T07:23:41.701353+00:00',
                    updatedAt: '2023-02-28T07:26:29.158713+00:00',
                    entityCount: {
                        total: 42,
                        failure: 7,
                        success: 35,
                        pending: 0,
                    },
                    period: {
                        periodID: '776295113466452341',
                        periodStartTime: 1661625000000,
                        nextPeriodStartTime: 1661711400000,
                        endCbuSequenceNumber: 1,
                        periodSequenceNumber: 1,
                        startCbuSequenceNumber: 1,
                        startDate: '2022-08-28',
                        endDate: '2022-08-28',
                    },
                },
            ],
        },
    ],
};

export const mockPhaseTriggerDataBOFI = {
    tenantID: '151613',
    coaID: '7242184752828075514',
    trigger: 'initiateBOF',
    periodID: '776295113466452341',
    status: 'FAILED',
    entityTrackersProgress: [
        {
            tenantID: '151613',
            coaID: '7242184752828075514',
            periodID: '776295113466452341',
            trigger: 'initiateBOF',
            status: 'FAILURE',
            entityType: 'ledger',
            transitionMap: {
                FAILURE: {
                    startTime: 1677569191960,
                },
                INITIATED: {
                    startTime: 1677569021109,
                    endTime: 1677569191960,
                },
            },
        },
    ],
    transitionMap: {
        FAILED: {
            startTime: 1677569191966,
        },
        INITIATED: {
            startTime: 1677569021105,
            endTime: 1677569191966,
        },
    },
};

export const mockPhaseTriggerDataBOPI = {
    tenantID: '151613',
    coaID: '7242184752828075514',
    trigger: 'initiateBOP',
    periodID: '776295113466452341',
    status: 'COMPLETED',
    entityTrackersProgress: [
        {
            tenantID: '151613',
            coaID: '7242184752828075514',
            periodID: '776295113466452341',
            trigger: 'initiateBOP',
            status: 'SUCCESS',
            entityType: 'ledger',
            transitionMap: {
                INITIATED: {
                    startTime: 1677568874036,
                    endTime: 1677568956734,
                },
                SUCCESS: {
                    startTime: 1677568956734,
                },
            },
        },
    ],
    transitionMap: {
        COMPLETED: {
            startTime: 1677569020948,
        },
        INITIATED: {
            startTime: 1677568874026,
            endTime: 1677568956740,
        },
        TRACKERS_ACKED: {
            startTime: 1677568956740,
            endTime: 1677569020948,
        },
    },
};

export const workerSummaryMockData = [
    {
        coaId: '7242184752828075514',
        totalFailedWorkers: 7,
        totalSuccessfulWorkers: 35,
        totalWorkers: 42,
        createdAt: '2023-02-28T07:23:41.755279+00:00',
        updatedAt: '2023-02-28T07:26:29.158713+00:00',
        startedAt: '2023-02-28T07:23:41.755279+00:00',
        finishedAt: '2023-02-28T07:26:29.158713+00:00',
        jobId: 'job-e611e479-746c-4ed2-8c2f-dce60e47d0a0-1',
        executionId: 1,
        timeTaken: '2Min 47Sec',
        status: 'FAILED',
    },
];

export const coaSummaryMockData = [
    {
        coaId: '7242184752828075514',
        coaPhaseStatus: 'FAILED',
        startTime: 1677569021105,
        endTime: 1677569191966,
    },
];

export const phaseCycleMockData = {
    ...coaSummaryMockData[0],
    phaseDetails: [
        {
            phaseName: 'BOPI',
            initiatedStartTime: 1677568874026,
            completedStartTime: 1677569020948,
        },
        {
            phaseName: 'BOFI',
            initiatedStartTime: 1677569021105,
            completedStartTime: 1677569191966,
        },
        {
            phaseName: 'Active',
        },
        {
            phaseName: 'EOPI',
        },
        {
            phaseName: 'EOFI',
        },
        {
            phaseName: 'Closed',
        },
    ],
};
