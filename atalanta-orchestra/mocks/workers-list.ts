export const workersListMockResponse = [{
    dependsOn: [],
    description: "-",
    name: "dataPreparationWorker",
    periodicities: ["DAILY"],
    phases: ["BOPI", "BOFI", "EOPI", "EOFI", "CLOSED"],
    status: "ACTIVE"
  }, {
    dependsOn: ["dataPreparationWorker"],
    description: "-",
    name: "couponProcessingWorker",
    periodicities: ["DAILY"],
    phases: ["EOPI", "EOFI"],
    status: "ACTIVE"
  }, {
    dependsOn: ["couponProcessingWorker"],
    description: "-",
    name: "dataPreparationWorker-1",
    periodicities: ["DAILY"],
    phases: ["EOPI", "EOFI"],
    status: "ACTIVE"
  }, {
    dependsOn: ["dataPreparationWorker-1"],
    description: "-",
    name: "installmentbillingworkerv2",
    periodicities: ["DAILY"],
    phases: ["EOPI"],
    status: "ACTIVE"
  }, {
    dependsOn: ["dataPreparationWorker"],
    description: "-",
    name: "principalliquidationworkerv2",
    periodicities: ["DAILY"],
    phases: ["BOPI"],
    status: "ACTIVE"
  }, {
    dependsOn: ["dataPreparationWorker"],
    description: "-",
    name: "loanrepaymentworkerv2",
    periodicities: ["DAILY"],
    phases: ["BOFI"],
    status: "ACTIVE"
  }, {
    dependsOn: ["dataPreparationWorker"],
    description: "-",
    name: "loanmaturityworkerv2",
    periodicities: ["DAILY"],
    phases: ["CLOSED"],
    status: "ACTIVE"
  }, {
    dependsOn: ["dataPreparationWorker"],
    description: "-",
    name: "couponexpiryworkerv2",
    periodicities: ["DAILY"],
    phases: ["BOPI"],
    status: "ACTIVE"
  }, {
    dependsOn: ["dataPreparationWorker-1"],
    description: "-",
    name: "interestliquidationworkerv2",
    periodicities: ["WEEKLY", "MONTHLY"],
    phases: ["EOPI"],
    status: "ACTIVE"
  }, {
    dependsOn: ["dataPreparationWorker-1"],
    description: "-",
    name: "rubyfeeworker",
    periodicities: ["DAILY"],
    phases: ["EOPI"],
    status: "ACTIVE"
  }, {
    dependsOn: ["dataPreparationWorker-1"],
    description: "-",
    name: "rubyendofdayworker",
    periodicities: ["DAILY"],
    phases: ["EOFI"],
    status: "ACTIVE"
  }, {
    dependsOn: ["dataPreparationWorker-1"],
    description: "-",
    name: "rubydormancyworker",
    periodicities: ["DAILY"],
    phases: ["EOFI"],
    status: "ACTIVE"
  }, {
    dependsOn: ["dataPreparationWorker"],
    description: "-",
    name: "rubypostbillingworker",
    periodicities: ["WEEKLY", "MONTHLY"],
    phases: ["CLOSED"],
    status: "ACTIVE"
  }, {
    dependsOn: ["dataPreparationWorker"],
    description: "-",
    name: "balanceworker",
    periodicities: ["DAILY", "WEEKLY", "MONTHLY"],
    phases: ["CLOSED"],
    status: "ACTIVE"
  }, {
    dependsOn: ["dataPreparationWorker"],
    description: "-",
    name: "rubyexcesscreditworker",
    periodicities: ["WEEKLY", "MONTHLY"],
    phases: ["BOFI"],
    status: "ACTIVE"
  }]